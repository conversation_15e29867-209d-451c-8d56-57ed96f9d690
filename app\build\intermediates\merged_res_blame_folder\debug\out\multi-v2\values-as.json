{"logs": [{"outputFile": "com.workly.app-mergeDebugResources-77:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\85666ebb484fc6a37ee9760c5f520352\\transformed\\appcompat-1.2.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,612,732,809,884,975,1068,1163,1257,1357,1450,1545,1639,1730,1821,1907,2020,2128,2227,2336,2452,2572,2739,2841", "endColumns": "107,98,106,90,101,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,98,108,115,119,166,101,82", "endOffsets": "208,307,414,505,607,727,804,879,970,1063,1158,1252,1352,1445,1540,1634,1725,1816,1902,2015,2123,2222,2331,2447,2567,2734,2836,2919"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "277,385,484,591,682,784,904,981,1056,1147,1240,1335,1429,1529,1622,1717,1811,1902,1993,2079,2192,2300,2399,2508,2624,2744,2911,13644", "endColumns": "107,98,106,90,101,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,98,108,115,119,166,101,82", "endOffsets": "380,479,586,677,779,899,976,1051,1142,1235,1330,1424,1524,1617,1712,1806,1897,1988,2074,2187,2295,2394,2503,2619,2739,2906,3008,13722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\631285464bc3699b5e0c057776a78762\\transformed\\ui-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,378,476,563,660,759,848,938,1006,1078,1161,1246,1325,1400,1466", "endColumns": "94,84,92,97,86,96,98,88,89,67,71,82,84,78,74,65,117", "endOffsets": "195,280,373,471,558,655,754,843,933,1001,1073,1156,1241,1320,1395,1461,1579"}, "to": {"startLines": "48,49,82,83,85,90,91,140,141,142,143,145,146,149,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4598,4693,8378,8471,8642,9031,9128,12989,13078,13168,13236,13389,13472,13727,14100,14175,14241", "endColumns": "94,84,92,97,86,96,98,88,89,67,71,82,84,78,74,65,117", "endOffsets": "4688,4773,8466,8564,8724,9123,9222,13073,13163,13231,13303,13467,13552,13801,14170,14236,14354"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b5d7701da16b09423affca14e07f926c\\transformed\\material-1.4.0-beta01\\res\\values-as\\values-as.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,304,406,529,608,673,762,827,886,972,1036,1099,1169,1233,1287,1392,1450,1512,1566,1638,1755,1842,1925,2035,2112,2193,2284,2351,2417,2487,2564,2651,2722,2799,2868,2937,3028,3100,3189,3278,3352,3424,3510,3560,3626,3706,3790,3852,3916,3979,4079,4176,4268,4367", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,101,122,78,64,88,64,58,85,63,62,69,63,53,104,57,61,53,71,116,86,82,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,80", "endOffsets": "222,299,401,524,603,668,757,822,881,967,1031,1094,1164,1228,1282,1387,1445,1507,1561,1633,1750,1837,1920,2030,2107,2188,2279,2346,2412,2482,2559,2646,2717,2794,2863,2932,3023,3095,3184,3273,3347,3419,3505,3555,3621,3701,3785,3847,3911,3974,4074,4171,4263,4362,4443"}, "to": {"startLines": "2,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3467,4294,4396,4519,8729,8877,8966,9227,9286,9372,9436,9499,9569,9633,9687,9792,9850,9912,9966,10038,10155,10242,10325,10435,10512,10593,10684,10751,10817,10887,10964,11051,11122,11199,11268,11337,11428,11500,11589,11678,11752,11824,11910,11960,12026,12106,12190,12252,12316,12379,12479,12576,12668,13308", "endLines": "5,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "endColumns": "12,76,101,122,78,64,88,64,58,85,63,62,69,63,53,104,57,61,53,71,116,86,82,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,80", "endOffsets": "272,3539,4391,4514,4593,8789,8961,9026,9281,9367,9431,9494,9564,9628,9682,9787,9845,9907,9961,10033,10150,10237,10320,10430,10507,10588,10679,10746,10812,10882,10959,11046,11117,11194,11263,11332,11423,11495,11584,11673,11747,11819,11905,11955,12021,12101,12185,12247,12311,12374,12474,12571,12663,12762,13384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d56bc5ab2f06c8c057783f978bdff8f7\\transformed\\material3-1.1.2\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,286,395,509,592,682,790,930,1047,1185,1266,1364,1455,1550,1662,1786,1889,2016,2142,2267,2443,2559,2673,2794,2909,3001,3093,3210,3332,3425,3531,3635,3766,3906,4012,4109,4182,4265,4352,4440,4545,4623,4704,4799,4900,4994,5092,5175,5278,5371,5470,5599,5679,5778", "endColumns": "114,115,108,113,82,89,107,139,116,137,80,97,90,94,111,123,102,126,125,124,175,115,113,120,114,91,91,116,121,92,105,103,130,139,105,96,72,82,86,87,104,77,80,94,100,93,97,82,102,92,98,128,79,98,89", "endOffsets": "165,281,390,504,587,677,785,925,1042,1180,1261,1359,1450,1545,1657,1781,1884,2011,2137,2262,2438,2554,2668,2789,2904,2996,3088,3205,3327,3420,3526,3630,3761,3901,4007,4104,4177,4260,4347,4435,4540,4618,4699,4794,4895,4989,5087,5170,5273,5366,5465,5594,5674,5773,5863"}, "to": {"startLines": "33,34,35,36,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,84,87,147,150,152,156,157,158,159,160,161,162,163,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3013,3128,3244,3353,4778,4861,4951,5059,5199,5316,5454,5535,5633,5724,5819,5931,6055,6158,6285,6411,6536,6712,6828,6942,7063,7178,7270,7362,7479,7601,7694,7800,7904,8035,8175,8281,8569,8794,13557,13806,13995,14359,14437,14518,14613,14714,14808,14906,14989,15092,15185,15284,15413,15493,15592", "endColumns": "114,115,108,113,82,89,107,139,116,137,80,97,90,94,111,123,102,126,125,124,175,115,113,120,114,91,91,116,121,92,105,103,130,139,105,96,72,82,86,87,104,77,80,94,100,93,97,82,102,92,98,128,79,98,89", "endOffsets": "3123,3239,3348,3462,4856,4946,5054,5194,5311,5449,5530,5628,5719,5814,5926,6050,6153,6280,6406,6531,6707,6823,6937,7058,7173,7265,7357,7474,7596,7689,7795,7899,8030,8170,8276,8373,8637,8872,13639,13889,14095,14432,14513,14608,14709,14803,14901,14984,15087,15180,15279,15408,15488,15587,15677"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9209685f1972143d9ab1eeffcca4ab41\\transformed\\core-1.12.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "38,39,40,41,42,43,44,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3544,3645,3748,3856,3961,4065,4165,13894", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "3640,3743,3851,3956,4060,4160,4289,13990"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\870103ce12d4503c553c2f969e6a0d11\\transformed\\navigation-ui-2.7.5\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,116", "endOffsets": "155,272"}, "to": {"startLines": "138,139", "startColumns": "4,4", "startOffsets": "12767,12872", "endColumns": "104,116", "endOffsets": "12867,12984"}}]}]}