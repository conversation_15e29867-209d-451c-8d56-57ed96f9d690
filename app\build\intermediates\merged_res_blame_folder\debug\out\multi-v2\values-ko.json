{"logs": [{"outputFile": "com.workly.app-mergeDebugResources-77:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9209685f1972143d9ab1eeffcca4ab41\\transformed\\core-1.12.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "38,39,40,41,42,43,44,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3306,3398,3498,3592,3689,3785,3883,12590", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "3393,3493,3587,3684,3780,3878,3978,12686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\631285464bc3699b5e0c057776a78762\\transformed\\ui-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,184,260,348,438,518,593,672,751,830,894,958,1031,1107,1175,1249,1313", "endColumns": "78,75,87,89,79,74,78,78,78,63,63,72,75,67,73,63,113", "endOffsets": "179,255,343,433,513,588,667,746,825,889,953,1026,1102,1170,1244,1308,1422"}, "to": {"startLines": "48,49,82,83,85,90,91,140,141,142,143,145,146,149,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4247,4326,7582,7670,7830,8185,8260,11779,11858,11937,12001,12137,12210,12444,12782,12856,12920", "endColumns": "78,75,87,89,79,74,78,78,78,63,63,72,75,67,73,63,113", "endOffsets": "4321,4397,7665,7755,7905,8255,8334,11853,11932,11996,12060,12205,12281,12507,12851,12915,13029"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\870103ce12d4503c553c2f969e6a0d11\\transformed\\navigation-ui-2.7.5\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "138,139", "startColumns": "4,4", "startOffsets": "11577,11677", "endColumns": "99,101", "endOffsets": "11672,11774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\85666ebb484fc6a37ee9760c5f520352\\transformed\\appcompat-1.2.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "254,351,445,546,628,726,832,912,987,1078,1171,1266,1360,1460,1553,1648,1742,1833,1924,2004,2102,2196,2291,2391,2488,2588,2740,12365", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "346,440,541,623,721,827,907,982,1073,1166,1261,1355,1455,1548,1643,1737,1828,1919,1999,2097,2191,2286,2386,2483,2583,2735,2829,12439"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b5d7701da16b09423affca14e07f926c\\transformed\\material-1.4.0-beta01\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,204,273,356,462,537,599,680,742,799,886,944,1002,1061,1118,1172,1267,1323,1380,1434,1500,1604,1679,1756,1847,1912,1977,2056,2123,2189,2253,2323,2400,2468,2539,2606,2676,2756,2833,2913,2995,3067,3132,3204,3252,3316,3391,3468,3530,3594,3657,3741,3820,3900,3980", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,68,82,105,74,61,80,61,56,86,57,57,58,56,53,94,55,56,53,65,103,74,76,90,64,64,78,66,65,63,69,76,67,70,66,69,79,76,79,81,71,64,71,47,63,74,76,61,63,62,83,78,79,79,71", "endOffsets": "199,268,351,457,532,594,675,737,794,881,939,997,1056,1113,1167,1262,1318,1375,1429,1495,1599,1674,1751,1842,1907,1972,2051,2118,2184,2248,2318,2395,2463,2534,2601,2671,2751,2828,2908,2990,3062,3127,3199,3247,3311,3386,3463,3525,3589,3652,3736,3815,3895,3975,4047"}, "to": {"startLines": "2,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3237,3983,4066,4172,7910,8042,8123,8339,8396,8483,8541,8599,8658,8715,8769,8864,8920,8977,9031,9097,9201,9276,9353,9444,9509,9574,9653,9720,9786,9850,9920,9997,10065,10136,10203,10273,10353,10430,10510,10592,10664,10729,10801,10849,10913,10988,11065,11127,11191,11254,11338,11417,11497,12065", "endLines": "5,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "endColumns": "12,68,82,105,74,61,80,61,56,86,57,57,58,56,53,94,55,56,53,65,103,74,76,90,64,64,78,66,65,63,69,76,67,70,66,69,79,76,79,81,71,64,71,47,63,74,76,61,63,62,83,78,79,79,71", "endOffsets": "249,3301,4061,4167,4242,7967,8118,8180,8391,8478,8536,8594,8653,8710,8764,8859,8915,8972,9026,9092,9196,9271,9348,9439,9504,9569,9648,9715,9781,9845,9915,9992,10060,10131,10198,10268,10348,10425,10505,10587,10659,10724,10796,10844,10908,10983,11060,11122,11186,11249,11333,11412,11492,11572,12132"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d56bc5ab2f06c8c057783f978bdff8f7\\transformed\\material3-1.1.2\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,156,256,358,458,529,614,717,832,938,1056,1134,1227,1308,1394,1499,1609,1707,1816,1923,2031,2160,2265,2365,2469,2575,2657,2748,2853,2958,3045,3139,3232,3341,3456,3551,3638,3708,3778,3857,3935,4026,4102,4180,4273,4364,4455,4548,4627,4719,4809,4902,5015,5091,5181", "endColumns": "100,99,101,99,70,84,102,114,105,117,77,92,80,85,104,109,97,108,106,107,128,104,99,103,105,81,90,104,104,86,93,92,108,114,94,86,69,69,78,77,90,75,77,92,90,90,92,78,91,89,92,112,75,89,86", "endOffsets": "151,251,353,453,524,609,712,827,933,1051,1129,1222,1303,1389,1494,1604,1702,1811,1918,2026,2155,2260,2360,2464,2570,2652,2743,2848,2953,3040,3134,3227,3336,3451,3546,3633,3703,3773,3852,3930,4021,4097,4175,4268,4359,4450,4543,4622,4714,4804,4897,5010,5086,5176,5263"}, "to": {"startLines": "33,34,35,36,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,84,87,147,150,152,156,157,158,159,160,161,162,163,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2834,2935,3035,3137,4402,4473,4558,4661,4776,4882,5000,5078,5171,5252,5338,5443,5553,5651,5760,5867,5975,6104,6209,6309,6413,6519,6601,6692,6797,6902,6989,7083,7176,7285,7400,7495,7760,7972,12286,12512,12691,13034,13110,13188,13281,13372,13463,13556,13635,13727,13817,13910,14023,14099,14189", "endColumns": "100,99,101,99,70,84,102,114,105,117,77,92,80,85,104,109,97,108,106,107,128,104,99,103,105,81,90,104,104,86,93,92,108,114,94,86,69,69,78,77,90,75,77,92,90,90,92,78,91,89,92,112,75,89,86", "endOffsets": "2930,3030,3132,3232,4468,4553,4656,4771,4877,4995,5073,5166,5247,5333,5438,5548,5646,5755,5862,5970,6099,6204,6304,6408,6514,6596,6687,6792,6897,6984,7078,7171,7280,7395,7490,7577,7825,8037,12360,12585,12777,13105,13183,13276,13367,13458,13551,13630,13722,13812,13905,14018,14094,14184,14271"}}]}]}