package com.workly.app.ui.statistics

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.workly.app.R
import com.workly.app.ui.statistics.components.*
import com.workly.app.ui.viewmodel.MainViewModel
import com.workly.app.ui.viewmodel.StatisticsViewModel
import com.workly.app.ui.viewmodel.ViewModelFactory

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StatisticsScreen(
    navController: Nav<PERSON><PERSON><PERSON>er,
    mainViewModel: MainViewModel
) {
    val application = androidx.compose.ui.platform.LocalContext.current.applicationContext as com.workly.app.WorklyApplication
    val viewModel: StatisticsViewModel = viewModel(
        factory = ViewModelFactory(application)
    )

    val statisticsData by viewModel.statisticsData.collectAsState()
    val availableShifts by viewModel.availableShifts.collectAsState()
    val selectedPeriod by viewModel.selectedPeriod.collectAsState()
    val selectedShiftId by viewModel.selectedShiftId.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()

    // Show error snackbar
    error?.let { errorMessage ->
        LaunchedEffect(errorMessage) {
            // TODO: Show snackbar
            viewModel.clearError()
        }
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top bar
        TopAppBar(
            title = { Text(stringResource(R.string.nav_statistics)) },
            navigationIcon = {
                IconButton(onClick = { navController.popBackStack() }) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                }
            },
            actions = {
                IconButton(
                    onClick = {
                        viewModel.exportStatistics { exportData ->
                            // TODO: Share or save the exported data
                        }
                    }
                ) {
                    Icon(Icons.Default.FileDownload, contentDescription = "Export")
                }
            }
        )

        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                item {
                    // Filters Section
                    StatisticsFiltersSection(
                        selectedPeriod = selectedPeriod,
                        availableShifts = availableShifts,
                        selectedShiftId = selectedShiftId,
                        onPeriodChange = viewModel::updateSelectedPeriod,
                        onShiftChange = viewModel::updateSelectedShift,
                        onCustomDateRangeChange = viewModel::updateCustomDateRange
                    )
                }

                item {
                    // Summary Cards
                    StatisticsSummarySection(statisticsData)
                }

                item {
                    // Status Distribution
                    StatusDistributionSection(statisticsData.statusDistribution)
                }

                item {
                    // Weekly Breakdown
                    WeeklyBreakdownSection(statisticsData.weeklyBreakdown)
                }

                item {
                    // Trends
                    TrendsSection(statisticsData.trends)
                }
            }
        }
    }
}
