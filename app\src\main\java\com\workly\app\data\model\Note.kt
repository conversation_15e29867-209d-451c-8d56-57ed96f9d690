package com.workly.app.data.model

import kotlinx.serialization.Serializable

@Serializable
data class Note(
    val id: String,
    val title: String,
    val content: String,
    val reminderTime: String? = null, // HH:mm format
    val associatedShiftIds: List<String> = emptyList(),
    val explicitReminderDays: List<String> = emptyList(), // Specific days if not following shift schedule
    val isActive: Boolean = true,
    val priority: NotePriority = NotePriority.NORMAL,
    val createdAt: String,
    val updatedAt: String
) {
    companion object {
        fun createNew(
            title: String,
            content: String,
            reminderTime: String? = null,
            associatedShiftIds: List<String> = emptyList(),
            explicitReminderDays: List<String> = emptyList(),
            priority: NotePriority = NotePriority.NORMAL
        ): Note {
            val now = kotlinx.datetime.Clock.System.now().toString()
            return Note(
                id = java.util.UUID.randomUUID().toString(),
                title = title,
                content = content,
                reminderTime = reminderTime,
                associatedShiftIds = associatedShiftIds,
                explicitReminderDays = explicitReminderDays,
                priority = priority,
                createdAt = now,
                updatedAt = now
            )
        }
    }
    
    fun shouldRemindToday(dayOfWeek: String, activeShift: Shift?): Boolean {
        if (!isActive || reminderTime == null) return false
        
        // If explicit days are set, use them
        if (explicitReminderDays.isNotEmpty()) {
            return explicitReminderDays.contains(dayOfWeek)
        }
        
        // If associated with shifts, check if any shift applies today
        if (associatedShiftIds.isNotEmpty() && activeShift != null) {
            return associatedShiftIds.contains(activeShift.id) && activeShift.isApplicableToday(dayOfWeek)
        }
        
        return false
    }

    /**
     * Get next reminder time for this note
     */
    fun getNextReminderTime(dayOfWeek: String, activeShift: Shift?): String? {
        if (!shouldRemindToday(dayOfWeek, activeShift)) return null
        return reminderTime
    }

    /**
     * Check if this note has high priority
     */
    fun isHighPriority(): Boolean = priority == NotePriority.HIGH

    /**
     * Get truncated title for display
     */
    fun getTruncatedTitle(maxLength: Int = 30): String {
        return if (title.length <= maxLength) title
        else "${title.take(maxLength)}..."
    }

    /**
     * Get truncated content for display
     */
    fun getTruncatedContent(maxLength: Int = 50): String {
        return if (content.length <= maxLength) content
        else "${content.take(maxLength)}..."
    }
}

@Serializable
enum class NotePriority(val displayName: String, val icon: String) {
    LOW("Thấp", "🔵"),
    NORMAL("Bình thường", "⚪"),
    HIGH("Cao", "⭐")
}
