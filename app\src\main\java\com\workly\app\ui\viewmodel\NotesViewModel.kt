package com.workly.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.workly.app.data.model.Note
import com.workly.app.data.model.Shift
import com.workly.app.data.repository.NotesRepository
import com.workly.app.data.repository.ShiftRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

class NotesViewModel(
    private val notesRepository: NotesRepository,
    private val shiftRepository: ShiftRepository,
    private val alarmManager: com.workly.app.business.AlarmManager? = null
) : ViewModel() {

    // State flows
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    private val _successMessage = MutableStateFlow<String?>(null)
    val successMessage: StateFlow<String?> = _successMessage.asStateFlow()

    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()

    // Notes data
    private val allNotes = notesRepository.getNotesFlow()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    // Filtered notes based on search query
    val notes = combine(allNotes, searchQuery) { notes, query ->
        if (query.isBlank()) {
            notes.sortedByDescending { it.updatedAt }
        } else {
            notes.filter { note ->
                note.title.contains(query, ignoreCase = true) ||
                note.content.contains(query, ignoreCase = true)
            }.sortedByDescending { it.updatedAt }
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
    )

    // Available shifts for note association
    val availableShifts = shiftRepository.getShiftsFlow()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    /**
     * Update search query
     */
    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
    }

    /**
     * Clear search
     */
    fun clearSearch() {
        _searchQuery.value = ""
    }

    /**
     * Delete note
     */
    fun deleteNote(note: Note) {
        viewModelScope.launch {
            try {
                _isLoading.value = true

                // Cancel alarm for this note
                alarmManager?.onNoteDeleted(note)

                // Delete from repository
                notesRepository.deleteNote(note.id)

                _successMessage.value = "Đã xóa ghi chú '${note.title}'"

            } catch (e: Exception) {
                _error.value = "Lỗi khi xóa ghi chú: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Toggle note active status
     */
    fun toggleNoteActive(note: Note) {
        viewModelScope.launch {
            try {
                val updatedNote = note.copy(
                    isActive = !note.isActive,
                    updatedAt = kotlinx.datetime.Clock.System.now().toString()
                )

                notesRepository.updateNote(updatedNote)

                // Update alarm
                alarmManager?.onNoteChanged(updatedNote)

                val status = if (updatedNote.isActive) "kích hoạt" else "vô hiệu hóa"
                _successMessage.value = "Đã $status ghi chú '${note.title}'"

            } catch (e: Exception) {
                _error.value = "Lỗi khi cập nhật ghi chú: ${e.message}"
            }
        }
    }

    /**
     * Get notes for today
     */
    fun getNotesForToday(): Flow<List<Note>> {
        return combine(allNotes, shiftRepository.getActiveShiftFlow()) { notes, activeShift ->
            val today = kotlinx.datetime.Clock.System.todayIn(kotlinx.datetime.TimeZone.currentSystemDefault())
            val todayDayOfWeek = getDayOfWeekString(today.dayOfWeek)

            notes.filter { note ->
                note.shouldRemindToday(todayDayOfWeek, activeShift)
            }
        }
    }

    /**
     * Get notes by shift
     */
    fun getNotesByShift(shiftId: String): Flow<List<Note>> {
        return allNotes.map { notes ->
            notes.filter { note ->
                note.associatedShiftIds.contains(shiftId)
            }
        }
    }

    private fun getDayOfWeekString(dayOfWeek: kotlinx.datetime.DayOfWeek): String {
        return when (dayOfWeek) {
            kotlinx.datetime.DayOfWeek.MONDAY -> "Mon"
            kotlinx.datetime.DayOfWeek.TUESDAY -> "Tue"
            kotlinx.datetime.DayOfWeek.WEDNESDAY -> "Wed"
            kotlinx.datetime.DayOfWeek.THURSDAY -> "Thu"
            kotlinx.datetime.DayOfWeek.FRIDAY -> "Fri"
            kotlinx.datetime.DayOfWeek.SATURDAY -> "Sat"
            kotlinx.datetime.DayOfWeek.SUNDAY -> "Sun"
        }
    }

    fun clearError() {
        _error.value = null
    }

    fun clearSuccessMessage() {
        _successMessage.value = null
    }
}
