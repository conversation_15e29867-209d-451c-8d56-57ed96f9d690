package com.workly.app.ui.home.components

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.workly.app.R
import com.workly.app.data.model.WorkStatus
import kotlinx.datetime.LocalDate

@Composable
fun WeeklyStatusGrid(
    onDayClick: (LocalDate) -> Unit,
    onDayLongClick: (LocalDate) -> Unit,
    modifier: Modifier = Modifier
) {
    // TODO: Get actual weekly data from ViewModel
    val weeklyData = getDummyWeeklyData()
    
    Card(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Trạng thái tuần",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Week navigation (TODO: Add navigation arrows)
            Text(
                text = "Tuần này",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Days grid
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                weeklyData.forEachIndexed { index, dayData ->
                    DayStatusItem(
                        dayData = dayData,
                        onClick = { onDayClick(dayData.date) },
                        onLongClick = { onDayLongClick(dayData.date) },
                        modifier = Modifier.weight(1f)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Week summary
            WeekSummary(weeklyData)
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun DayStatusItem(
    dayData: DayStatusData,
    onClick: () -> Unit,
    onLongClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(horizontal = 2.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Day name
        Text(
            text = dayData.dayName,
            style = MaterialTheme.typography.labelSmall,
            color = if (dayData.isToday) MaterialTheme.colorScheme.primary 
                   else MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        // Status box
        Box(
            modifier = Modifier
                .size(40.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(getStatusColor(dayData.status))
                .combinedClickable(
                    onClick = onClick,
                    onLongClick = if (dayData.canEdit) onLongClick else null
                ),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = dayData.dayNumber.toString(),
                    fontSize = 10.sp,
                    color = Color.White,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = dayData.status.getIcon(),
                    fontSize = 12.sp
                )
            }
        }
        
        // Today indicator
        if (dayData.isToday) {
            Spacer(modifier = Modifier.height(2.dp))
            Box(
                modifier = Modifier
                    .size(4.dp)
                    .clip(RoundedCornerShape(2.dp))
                    .background(MaterialTheme.colorScheme.primary)
            )
        }
    }
}

@Composable
private fun WeekSummary(weeklyData: List<DayStatusData>) {
    val workDays = weeklyData.count { 
        it.status in listOf(WorkStatus.DU_CONG, WorkStatus.DI_MUON, WorkStatus.VE_SOM, WorkStatus.DI_MUON_VE_SOM) 
    }
    val totalHours = 0.0 // TODO: Calculate from actual data
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        SummaryItem(
            label = "Ngày làm",
            value = "$workDays/7"
        )
        SummaryItem(
            label = "Tổng giờ",
            value = "${String.format("%.1f", totalHours)}h"
        )
        SummaryItem(
            label = "OT",
            value = "0.0h"
        )
    }
}

@Composable
private fun SummaryItem(
    label: String,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.SemiBold
        )
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

private fun getStatusColor(status: WorkStatus): Color {
    return when (status) {
        WorkStatus.DU_CONG -> Color(0xFF4CAF50) // Green
        WorkStatus.THIEU_LOG -> Color(0xFFFF9800) // Orange
        WorkStatus.DI_MUON, WorkStatus.VE_SOM, WorkStatus.DI_MUON_VE_SOM -> Color(0xFFFF5722) // Red-Orange
        WorkStatus.NGHI_PHEP -> Color(0xFF2196F3) // Blue
        WorkStatus.NGHI_BENH -> Color(0xFF9C27B0) // Purple
        WorkStatus.NGHI_LE -> Color(0xFF607D8B) // Blue-Grey
        WorkStatus.VANG_MAT -> Color(0xFFF44336) // Red
        WorkStatus.UNKNOWN -> Color(0xFF9E9E9E) // Grey
    }
}

// Dummy data for testing
private data class DayStatusData(
    val date: LocalDate,
    val dayName: String,
    val dayNumber: Int,
    val status: WorkStatus,
    val isToday: Boolean,
    val canEdit: Boolean
)

private fun getDummyWeeklyData(): List<DayStatusData> {
    val today = kotlinx.datetime.Clock.System.todayIn(kotlinx.datetime.TimeZone.currentSystemDefault())
    val startOfWeek = today.minus(today.dayOfWeek.ordinal, kotlinx.datetime.DateTimeUnit.DAY)
    
    return (0..6).map { dayOffset ->
        val date = startOfWeek.plus(dayOffset, kotlinx.datetime.DateTimeUnit.DAY)
        val isToday = date == today
        
        DayStatusData(
            date = date,
            dayName = when (dayOffset) {
                0 -> stringResource(R.string.day_monday)
                1 -> stringResource(R.string.day_tuesday)
                2 -> stringResource(R.string.day_wednesday)
                3 -> stringResource(R.string.day_thursday)
                4 -> stringResource(R.string.day_friday)
                5 -> stringResource(R.string.day_saturday)
                6 -> stringResource(R.string.day_sunday)
                else -> ""
            },
            dayNumber = date.dayOfMonth,
            status = if (date > today) WorkStatus.UNKNOWN else WorkStatus.DU_CONG,
            isToday = isToday,
            canEdit = date <= today
        )
    }
}
