-- Merging decision tree log ---
provider#androidx.startup.InitializationProvider
INJECTED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:80:9-88:20
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b1b8179c4e142f5c9a8430a2c3ceea5\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b1b8179c4e142f5c9a8430a2c3ceea5\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fb2cadf6a9c20bd881ef628d816b94d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fb2cadf6a9c20bd881ef628d816b94d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d31dc1be7ac700a51a2810bc02aacfe0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d31dc1be7ac700a51a2810bc02aacfe0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:84:13-31
	android:authorities
		INJECTED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:82:13-68
	android:exported
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:83:13-37
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:81:13-67
manifest
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:2:1-92:12
INJECTED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:2:1-92:12
INJECTED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:2:1-92:12
INJECTED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:2:1-92:12
MERGED from [androidx.databinding:viewbinding:8.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\8e36d98548c0327cca6ce7a1fed685fb\transformed\viewbinding-8.1.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\10f7657a1a0caac76080b89a5c42703e\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\d9ff5a80f193d1d49c479ca452a461ce\transformed\navigation-common-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\e9d74477538414c499e216f4f2508d80\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\0c7a235ec6bee5ebf852f3ccb26654eb\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\870103ce12d4503c553c2f969e6a0d11\transformed\navigation-ui-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\92d4df3efeec41ad5d46e7af8ef27702\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\4e5f64b8dc726a37c9008501ceb1eafc\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\903dc4cc5503c6647c8f8fe6717899da\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\5cbf867ccd2da1016e5a62af6c2138de\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\54046794dae8491ee48cded09d634ab2\transformed\navigation-compose-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b2dd5a639d79853a2cd8889e06ca4a8\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\d56bc5ab2f06c8c057783f978bdff8f7\transformed\material3-1.1.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\7bef14388b763436512da2b050f8bca2\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.4.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\b5d7701da16b09423affca14e07f926c\transformed\material-1.4.0-beta01\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3000c410dcad82b0d03697f4c5499ff\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d779a7f5d9d2c61b45672a1511f8995\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\85666ebb484fc6a37ee9760c5f520352\transformed\appcompat-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\55a108e432aefc332db03dc6f1b363f7\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\bf30b34a79c9e048b98ed2422ffa9a20\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\c52215f32594de51c3cb6efe719a0515\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d91a80c2cbe6d8b82b8acf254d0d240e\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\da90ab4e24ed9bf80f68627ba8e2966c\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\1b3b20fc971a3a4b1f673d86cb812801\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7ae61f9697f9c3d25b8097b2a37c8e66\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\4ecaa8f8b026aef37b3f7fcc8895ad99\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\78e7c8fbd9b70f529ec86b8acc2cd2b8\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2d31c9c5989ad42222b6e2da76a9f51b\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9e95732323200f7dd0b4c9faf6941f5d\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a6cfa7af81f3e37f1b147cbde38eecac\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\43bf4afe15a2a12471e8c312e08c307d\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\f7d29c88f7fd290ffbbee3a95cc2e5f5\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d3ac4181f5cb575e4aa7fd69b8f87ae7\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ba666db4a41c4324065f6424e4a03ff3\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\f62423f68cfb6548feaa2ba7fda583ce\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7908f9b71f90ccc5baf5b9d6d63a4586\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\69a61ce86c778bbae23854f9e31e5eb0\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\191dab1375b735307b2b6d64637a3912\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\3a099f768b16f4ee4504c9f0cf559581\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b1b8179c4e142f5c9a8430a2c3ceea5\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fb2cadf6a9c20bd881ef628d816b94d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\20c58bdba6dd9dd2b333886a6a2665a9\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4c902ae88e3fcb8414bdcdd7e15533a\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bb76109e1cf50429309d8327a4459bf8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\72b26c8feb6548678a270c4155c913a0\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad9ec581aef95c5b6622e3ad6be5416f\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\320a0deee2b6028ed42cfd28d5803bfa\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc3ee36d4139769935bca4ef7f352ea2\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a50754818a4ae1873f5d13bfd13b179\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d73eb0f4d2d5505102963fa57b0963c\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\fb1f53f58221eda3661f250d3d0dfa9b\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\109dbf7799b57adacd695701d8a3fe4b\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\209d02d86ddb4afa54e00db4faec5192\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\668cb6d9aa0438da7c23db0bfe5b8d11\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc5999e2e4219e3a597f13f8afe64fed\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6dac167f394d790876e2bd1776b58681\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\631285464bc3699b5e0c057776a78762\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b82e9ad9697de4fbf875105b7d514266\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8aaabc6fb5e0bd0ce127cf10c8667b5a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3abd17ac5a9bc399eff2cd65e2d0b40\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5264e110e148ebb43f952125d09f8ee9\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\afc1062ae6311f9c0831264a65504776\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b42f0099f4c41b34f15ef41dbea2ec\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f16a887bd3acc6bccbd27e3ed05e942a\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\a76a40a9f945bc4c29d8f934fc62240b\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e8ad4fd69f735c0e2b9d9ded7f214bc\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e77266ad0ade30e0a75c5f15889e6b2\transformed\appcompat-resources-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d2c3b32cc5eb9616938597d93b4d64b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef6672bca367874b7c100aeb1195e5de\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\750325b8e3d20176c30f2d06d660611d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2a2d5ed04723da4960fb0081ca52ce24\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ce8859fe52d24f21b4a4e84fa8be4040\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\00b888c8506bc1c13efec462b0d4e10a\transformed\datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\de295e408540d8cdec8093e86971a328\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e260cb7545749307c05255c37d7a556a\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\816a974bff35f9e39c6e664b6c114388\transformed\napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8daae7748867931ad677aed9ea5a82b8\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f54a7aceaac3552e8cad92af6fb09c1\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\02040f5c1aa470d4d494d1f8ed926cf8\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e7ce8be7af99c7ca0299590a32ee5f41\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\92f00cb8eb866dde276465585bf65b73\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c829988b99328910756e01a809ef42ba\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d31dc1be7ac700a51a2810bc02aacfe0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\af5a3438dff377b9c029b7172c8a8811\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\55edbade2287c9acfa01bffd0cb4cd15\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7916ca1d00e2b0b0196fdf3965aa0dc2\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a68aa31ffbd56c29a8ddadab1f1fcdf6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d35fbb03a21a639f61973f09262463b2\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d17164c2d8b0627ded1f9c50cec270b1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\868e248ac7e73007b5c7fe0fc2dbe417\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.VIBRATE
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:8:5-66
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:8:22-63
uses-permission#android.permission.WAKE_LOCK
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:9:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:9:22-65
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:10:5-81
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:10:22-78
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:11:5-79
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:11:22-76
uses-permission#android.permission.USE_EXACT_ALARM
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:12:5-74
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:12:22-71
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:13:5-77
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:13:22-74
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:14:5-79
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:14:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:15:5-81
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:15:22-78
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:16:5-17:38
	android:maxSdkVersion
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:17:9-35
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:16:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:18:5-19:38
	android:maxSdkVersion
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:19:9-35
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:18:22-77
application
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:21:5-90:19
INJECTED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:21:5-90:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [com.google.android.material:material:1.4.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\b5d7701da16b09423affca14e07f926c\transformed\material-1.4.0-beta01\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.4.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\b5d7701da16b09423affca14e07f926c\transformed\material-1.4.0-beta01\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d779a7f5d9d2c61b45672a1511f8995\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d779a7f5d9d2c61b45672a1511f8995\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\69a61ce86c778bbae23854f9e31e5eb0\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\69a61ce86c778bbae23854f9e31e5eb0\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\191dab1375b735307b2b6d64637a3912\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\191dab1375b735307b2b6d64637a3912\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b1b8179c4e142f5c9a8430a2c3ceea5\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b1b8179c4e142f5c9a8430a2c3ceea5\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fb2cadf6a9c20bd881ef628d816b94d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fb2cadf6a9c20bd881ef628d816b94d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e8ad4fd69f735c0e2b9d9ded7f214bc\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e8ad4fd69f735c0e2b9d9ded7f214bc\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8daae7748867931ad677aed9ea5a82b8\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8daae7748867931ad677aed9ea5a82b8\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\92f00cb8eb866dde276465585bf65b73\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\92f00cb8eb866dde276465585bf65b73\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d31dc1be7ac700a51a2810bc02aacfe0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d31dc1be7ac700a51a2810bc02aacfe0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:29:9-35
	android:label
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:27:9-41
	android:fullBackupContent
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:25:9-54
	android:roundIcon
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:28:9-54
	tools:targetApi
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:31:9-29
	android:icon
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:26:9-43
	android:allowBackup
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:23:9-35
	android:theme
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:30:9-44
	android:dataExtractionRules
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:24:9-65
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:22:9-42
activity#com.workly.app.ui.MainActivity
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:33:9-42:20
	android:label
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:36:13-45
	android:exported
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:35:13-36
	android:theme
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:37:13-48
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:34:13-44
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:38:13-41:29
action#android.intent.action.MAIN
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:39:17-69
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:39:25-66
category#android.intent.category.LAUNCHER
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:40:17-77
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:40:27-74
activity#com.workly.app.ui.alarm.AlarmActivity
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:45:9-51:57
	android:turnScreenOn
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:50:13-40
	android:launchMode
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:48:13-43
	android:exported
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:47:13-37
	android:showOnLockScreen
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:49:13-44
	android:theme
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:51:13-54
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:46:13-51
receiver#com.workly.app.alarm.AlarmReceiver
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:54:9-60:20
	android:exported
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:56:13-37
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:55:13-48
intent-filter#action:name:com.workly.app.ALARM_ACTION
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:57:13-59:29
action#com.workly.app.ALARM_ACTION
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:58:17-70
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:58:25-67
receiver#com.workly.app.alarm.BootReceiver
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:63:9-72:20
	android:exported
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:65:13-37
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:64:13-47
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.PACKAGE_REPLACED+data:scheme:package
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:66:13-71:29
	android:priority
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:66:28-51
action#android.intent.action.BOOT_COMPLETED
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:67:17-79
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:67:25-76
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:68:17-84
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:68:25-81
action#android.intent.action.PACKAGE_REPLACED
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:69:17-81
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:69:25-78
data
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:70:17-50
	android:scheme
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:70:23-47
receiver#com.workly.app.notification.NotificationDismissReceiver
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:75:9-77:40
	android:exported
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:77:13-37
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:76:13-69
meta-data#androidx.work.WorkManagerInitializer
ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:85:13-87:52
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:87:17-49
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:86:17-68
uses-sdk
INJECTED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml
INJECTED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\8e36d98548c0327cca6ce7a1fed685fb\transformed\viewbinding-8.1.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\8e36d98548c0327cca6ce7a1fed685fb\transformed\viewbinding-8.1.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\10f7657a1a0caac76080b89a5c42703e\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\10f7657a1a0caac76080b89a5c42703e\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\d9ff5a80f193d1d49c479ca452a461ce\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\d9ff5a80f193d1d49c479ca452a461ce\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\e9d74477538414c499e216f4f2508d80\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\e9d74477538414c499e216f4f2508d80\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\0c7a235ec6bee5ebf852f3ccb26654eb\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\0c7a235ec6bee5ebf852f3ccb26654eb\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\870103ce12d4503c553c2f969e6a0d11\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\870103ce12d4503c553c2f969e6a0d11\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\92d4df3efeec41ad5d46e7af8ef27702\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\92d4df3efeec41ad5d46e7af8ef27702\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\4e5f64b8dc726a37c9008501ceb1eafc\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\4e5f64b8dc726a37c9008501ceb1eafc\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\903dc4cc5503c6647c8f8fe6717899da\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\903dc4cc5503c6647c8f8fe6717899da\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\5cbf867ccd2da1016e5a62af6c2138de\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\5cbf867ccd2da1016e5a62af6c2138de\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\54046794dae8491ee48cded09d634ab2\transformed\navigation-compose-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\54046794dae8491ee48cded09d634ab2\transformed\navigation-compose-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b2dd5a639d79853a2cd8889e06ca4a8\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b2dd5a639d79853a2cd8889e06ca4a8\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\d56bc5ab2f06c8c057783f978bdff8f7\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\d56bc5ab2f06c8c057783f978bdff8f7\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\7bef14388b763436512da2b050f8bca2\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\7bef14388b763436512da2b050f8bca2\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.4.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\b5d7701da16b09423affca14e07f926c\transformed\material-1.4.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.4.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\b5d7701da16b09423affca14e07f926c\transformed\material-1.4.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3000c410dcad82b0d03697f4c5499ff\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3000c410dcad82b0d03697f4c5499ff\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d779a7f5d9d2c61b45672a1511f8995\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\1d779a7f5d9d2c61b45672a1511f8995\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\85666ebb484fc6a37ee9760c5f520352\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\85666ebb484fc6a37ee9760c5f520352\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\55a108e432aefc332db03dc6f1b363f7\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\55a108e432aefc332db03dc6f1b363f7\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\bf30b34a79c9e048b98ed2422ffa9a20\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\bf30b34a79c9e048b98ed2422ffa9a20\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\c52215f32594de51c3cb6efe719a0515\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\c52215f32594de51c3cb6efe719a0515\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d91a80c2cbe6d8b82b8acf254d0d240e\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d91a80c2cbe6d8b82b8acf254d0d240e\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\da90ab4e24ed9bf80f68627ba8e2966c\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\da90ab4e24ed9bf80f68627ba8e2966c\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\1b3b20fc971a3a4b1f673d86cb812801\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\1b3b20fc971a3a4b1f673d86cb812801\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7ae61f9697f9c3d25b8097b2a37c8e66\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7ae61f9697f9c3d25b8097b2a37c8e66\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\4ecaa8f8b026aef37b3f7fcc8895ad99\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\4ecaa8f8b026aef37b3f7fcc8895ad99\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\78e7c8fbd9b70f529ec86b8acc2cd2b8\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\78e7c8fbd9b70f529ec86b8acc2cd2b8\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2d31c9c5989ad42222b6e2da76a9f51b\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2d31c9c5989ad42222b6e2da76a9f51b\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9e95732323200f7dd0b4c9faf6941f5d\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9e95732323200f7dd0b4c9faf6941f5d\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a6cfa7af81f3e37f1b147cbde38eecac\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\a6cfa7af81f3e37f1b147cbde38eecac\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\43bf4afe15a2a12471e8c312e08c307d\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\43bf4afe15a2a12471e8c312e08c307d\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\f7d29c88f7fd290ffbbee3a95cc2e5f5\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\f7d29c88f7fd290ffbbee3a95cc2e5f5\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d3ac4181f5cb575e4aa7fd69b8f87ae7\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d3ac4181f5cb575e4aa7fd69b8f87ae7\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ba666db4a41c4324065f6424e4a03ff3\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ba666db4a41c4324065f6424e4a03ff3\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\f62423f68cfb6548feaa2ba7fda583ce\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\f62423f68cfb6548feaa2ba7fda583ce\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7908f9b71f90ccc5baf5b9d6d63a4586\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7908f9b71f90ccc5baf5b9d6d63a4586\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\69a61ce86c778bbae23854f9e31e5eb0\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\69a61ce86c778bbae23854f9e31e5eb0\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\191dab1375b735307b2b6d64637a3912\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\191dab1375b735307b2b6d64637a3912\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\3a099f768b16f4ee4504c9f0cf559581\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\3a099f768b16f4ee4504c9f0cf559581\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b1b8179c4e142f5c9a8430a2c3ceea5\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b1b8179c4e142f5c9a8430a2c3ceea5\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fb2cadf6a9c20bd881ef628d816b94d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fb2cadf6a9c20bd881ef628d816b94d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\20c58bdba6dd9dd2b333886a6a2665a9\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\20c58bdba6dd9dd2b333886a6a2665a9\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4c902ae88e3fcb8414bdcdd7e15533a\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4c902ae88e3fcb8414bdcdd7e15533a\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bb76109e1cf50429309d8327a4459bf8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bb76109e1cf50429309d8327a4459bf8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\72b26c8feb6548678a270c4155c913a0\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\72b26c8feb6548678a270c4155c913a0\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad9ec581aef95c5b6622e3ad6be5416f\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad9ec581aef95c5b6622e3ad6be5416f\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\320a0deee2b6028ed42cfd28d5803bfa\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\320a0deee2b6028ed42cfd28d5803bfa\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc3ee36d4139769935bca4ef7f352ea2\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc3ee36d4139769935bca4ef7f352ea2\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a50754818a4ae1873f5d13bfd13b179\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a50754818a4ae1873f5d13bfd13b179\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d73eb0f4d2d5505102963fa57b0963c\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d73eb0f4d2d5505102963fa57b0963c\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\fb1f53f58221eda3661f250d3d0dfa9b\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\fb1f53f58221eda3661f250d3d0dfa9b\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\109dbf7799b57adacd695701d8a3fe4b\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\109dbf7799b57adacd695701d8a3fe4b\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\209d02d86ddb4afa54e00db4faec5192\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\209d02d86ddb4afa54e00db4faec5192\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\668cb6d9aa0438da7c23db0bfe5b8d11\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\668cb6d9aa0438da7c23db0bfe5b8d11\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc5999e2e4219e3a597f13f8afe64fed\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\fc5999e2e4219e3a597f13f8afe64fed\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6dac167f394d790876e2bd1776b58681\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6dac167f394d790876e2bd1776b58681\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\631285464bc3699b5e0c057776a78762\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\631285464bc3699b5e0c057776a78762\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b82e9ad9697de4fbf875105b7d514266\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b82e9ad9697de4fbf875105b7d514266\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8aaabc6fb5e0bd0ce127cf10c8667b5a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8aaabc6fb5e0bd0ce127cf10c8667b5a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3abd17ac5a9bc399eff2cd65e2d0b40\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3abd17ac5a9bc399eff2cd65e2d0b40\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5264e110e148ebb43f952125d09f8ee9\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5264e110e148ebb43f952125d09f8ee9\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\afc1062ae6311f9c0831264a65504776\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\afc1062ae6311f9c0831264a65504776\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b42f0099f4c41b34f15ef41dbea2ec\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b42f0099f4c41b34f15ef41dbea2ec\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f16a887bd3acc6bccbd27e3ed05e942a\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f16a887bd3acc6bccbd27e3ed05e942a\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\a76a40a9f945bc4c29d8f934fc62240b\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\a76a40a9f945bc4c29d8f934fc62240b\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e8ad4fd69f735c0e2b9d9ded7f214bc\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e8ad4fd69f735c0e2b9d9ded7f214bc\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e77266ad0ade30e0a75c5f15889e6b2\transformed\appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e77266ad0ade30e0a75c5f15889e6b2\transformed\appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d2c3b32cc5eb9616938597d93b4d64b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d2c3b32cc5eb9616938597d93b4d64b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef6672bca367874b7c100aeb1195e5de\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef6672bca367874b7c100aeb1195e5de\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\750325b8e3d20176c30f2d06d660611d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\750325b8e3d20176c30f2d06d660611d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2a2d5ed04723da4960fb0081ca52ce24\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2a2d5ed04723da4960fb0081ca52ce24\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ce8859fe52d24f21b4a4e84fa8be4040\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ce8859fe52d24f21b4a4e84fa8be4040\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\00b888c8506bc1c13efec462b0d4e10a\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\00b888c8506bc1c13efec462b0d4e10a\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\de295e408540d8cdec8093e86971a328\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\de295e408540d8cdec8093e86971a328\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e260cb7545749307c05255c37d7a556a\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e260cb7545749307c05255c37d7a556a\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\816a974bff35f9e39c6e664b6c114388\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\816a974bff35f9e39c6e664b6c114388\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8daae7748867931ad677aed9ea5a82b8\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8daae7748867931ad677aed9ea5a82b8\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f54a7aceaac3552e8cad92af6fb09c1\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f54a7aceaac3552e8cad92af6fb09c1\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\02040f5c1aa470d4d494d1f8ed926cf8\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\02040f5c1aa470d4d494d1f8ed926cf8\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e7ce8be7af99c7ca0299590a32ee5f41\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e7ce8be7af99c7ca0299590a32ee5f41\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\92f00cb8eb866dde276465585bf65b73\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\92f00cb8eb866dde276465585bf65b73\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c829988b99328910756e01a809ef42ba\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c829988b99328910756e01a809ef42ba\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d31dc1be7ac700a51a2810bc02aacfe0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d31dc1be7ac700a51a2810bc02aacfe0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\af5a3438dff377b9c029b7172c8a8811\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\af5a3438dff377b9c029b7172c8a8811\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\55edbade2287c9acfa01bffd0cb4cd15\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\55edbade2287c9acfa01bffd0cb4cd15\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7916ca1d00e2b0b0196fdf3965aa0dc2\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7916ca1d00e2b0b0196fdf3965aa0dc2\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a68aa31ffbd56c29a8ddadab1f1fcdf6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a68aa31ffbd56c29a8ddadab1f1fcdf6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d35fbb03a21a639f61973f09262463b2\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d35fbb03a21a639f61973f09262463b2\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d17164c2d8b0627ded1f9c50cec270b1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d17164c2d8b0627ded1f9c50cec270b1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\868e248ac7e73007b5c7fe0fc2dbe417\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\868e248ac7e73007b5c7fe0fc2dbe417\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:22-74
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\69a61ce86c778bbae23854f9e31e5eb0\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\69a61ce86c778bbae23854f9e31e5eb0\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\69a61ce86c778bbae23854f9e31e5eb0\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\191dab1375b735307b2b6d64637a3912\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\191dab1375b735307b2b6d64637a3912\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\191dab1375b735307b2b6d64637a3912\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b1b8179c4e142f5c9a8430a2c3ceea5\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b1b8179c4e142f5c9a8430a2c3ceea5\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b1b8179c4e142f5c9a8430a2c3ceea5\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fb2cadf6a9c20bd881ef628d816b94d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fb2cadf6a9c20bd881ef628d816b94d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fb2cadf6a9c20bd881ef628d816b94d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e8ad4fd69f735c0e2b9d9ded7f214bc\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e8ad4fd69f735c0e2b9d9ded7f214bc\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e8ad4fd69f735c0e2b9d9ded7f214bc\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e8ad4fd69f735c0e2b9d9ded7f214bc\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e8ad4fd69f735c0e2b9d9ded7f214bc\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e8ad4fd69f735c0e2b9d9ded7f214bc\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.workly.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.workly.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8daae7748867931ad677aed9ea5a82b8\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8daae7748867931ad677aed9ea5a82b8\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8daae7748867931ad677aed9ea5a82b8\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8daae7748867931ad677aed9ea5a82b8\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8daae7748867931ad677aed9ea5a82b8\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
