package com.workly.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.workly.app.business.ShiftValidator
import com.workly.app.data.model.Shift
import com.workly.app.data.repository.ShiftRepository
import com.workly.app.ui.shifts.AddEditShiftUiState
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

class AddEditShiftViewModel(
    private val shiftRepository: ShiftRepository
) : ViewModel() {
    
    private val shiftValidator = ShiftValidator()
    
    // State flows
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    private val _saveSuccess = MutableStateFlow(false)
    val saveSuccess: StateFlow<Boolean> = _saveSuccess.asStateFlow()
    
    private val _currentShiftId = MutableStateFlow<String?>(null)
    
    // UI State
    private val _uiState = MutableStateFlow(AddEditShiftUiState())
    val uiState: StateFlow<AddEditShiftUiState> = _uiState.asStateFlow()
    
    init {
        // Validate whenever UI state changes
        viewModelScope.launch {
            _uiState.collect { state ->
                validateCurrentState()
            }
        }
    }
    
    /**
     * Load existing shift for editing
     */
    fun loadShift(shiftId: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _currentShiftId.value = shiftId
                
                val shift = shiftRepository.getShiftById(shiftId)
                if (shift != null) {
                    _uiState.value = AddEditShiftUiState(
                        name = shift.name,
                        startTime = shift.startTime,
                        endTime = shift.endTime,
                        departureTime = shift.departureTime,
                        officeEndTime = shift.officeEndTime,
                        daysApplied = shift.daysApplied,
                        remindBeforeStart = shift.remindBeforeStart,
                        remindAfterEnd = shift.remindAfterEnd,
                        breakMinutes = shift.breakMinutes,
                        lateThresholdMinutes = shift.lateThresholdMinutes,
                        earlyThresholdMinutes = shift.earlyThresholdMinutes,
                        penaltyRoundingMinutes = shift.penaltyRoundingMinutes,
                        showPunch = shift.showPunch
                    )
                } else {
                    _error.value = "Không tìm thấy ca làm việc"
                }
            } catch (e: Exception) {
                _error.value = "Lỗi khi tải ca làm việc: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Save shift (create new or update existing)
     */
    fun saveShift() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                val currentState = _uiState.value
                if (!currentState.isValid) {
                    _error.value = "Vui lòng sửa các lỗi trước khi lưu"
                    return@launch
                }
                
                val shift = if (_currentShiftId.value != null) {
                    // Update existing shift
                    val existingShift = shiftRepository.getShiftById(_currentShiftId.value!!)
                    existingShift?.copy(
                        name = currentState.name,
                        startTime = currentState.startTime,
                        endTime = currentState.endTime,
                        departureTime = currentState.departureTime,
                        officeEndTime = currentState.officeEndTime,
                        daysApplied = currentState.daysApplied,
                        remindBeforeStart = currentState.remindBeforeStart,
                        remindAfterEnd = currentState.remindAfterEnd,
                        breakMinutes = currentState.breakMinutes,
                        lateThresholdMinutes = currentState.lateThresholdMinutes,
                        earlyThresholdMinutes = currentState.earlyThresholdMinutes,
                        penaltyRoundingMinutes = currentState.penaltyRoundingMinutes,
                        showPunch = currentState.showPunch,
                        updatedAt = kotlinx.datetime.Clock.System.now().toString()
                    )
                } else {
                    // Create new shift
                    Shift.createNew(
                        name = currentState.name,
                        startTime = currentState.startTime,
                        officeEndTime = currentState.officeEndTime,
                        endTime = currentState.endTime,
                        departureTime = currentState.departureTime,
                        daysApplied = currentState.daysApplied,
                        remindBeforeStart = currentState.remindBeforeStart,
                        remindAfterEnd = currentState.remindAfterEnd,
                        showPunch = currentState.showPunch,
                        breakMinutes = currentState.breakMinutes,
                        lateThresholdMinutes = currentState.lateThresholdMinutes,
                        earlyThresholdMinutes = currentState.earlyThresholdMinutes,
                        penaltyRoundingMinutes = currentState.penaltyRoundingMinutes
                    )
                }
                
                if (shift != null) {
                    if (_currentShiftId.value != null) {
                        shiftRepository.updateShift(shift)
                    } else {
                        shiftRepository.addShift(shift)
                    }
                    _saveSuccess.value = true
                }
                
            } catch (e: Exception) {
                _error.value = "Lỗi khi lưu ca làm việc: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    // Update methods for UI state
    fun updateName(name: String) {
        _uiState.value = _uiState.value.copy(name = name)
    }
    
    fun updateStartTime(startTime: String) {
        _uiState.value = _uiState.value.copy(startTime = startTime)
    }
    
    fun updateEndTime(endTime: String) {
        _uiState.value = _uiState.value.copy(endTime = endTime)
    }
    
    fun updateDepartureTime(departureTime: String) {
        _uiState.value = _uiState.value.copy(departureTime = departureTime)
    }
    
    fun updateOfficeEndTime(officeEndTime: String) {
        _uiState.value = _uiState.value.copy(officeEndTime = officeEndTime)
    }
    
    fun updateDaysApplied(daysApplied: List<String>) {
        _uiState.value = _uiState.value.copy(daysApplied = daysApplied)
    }
    
    fun updateRemindBeforeStart(minutes: Int) {
        _uiState.value = _uiState.value.copy(remindBeforeStart = minutes)
    }
    
    fun updateRemindAfterEnd(minutes: Int) {
        _uiState.value = _uiState.value.copy(remindAfterEnd = minutes)
    }
    
    fun updateBreakMinutes(minutes: Int) {
        _uiState.value = _uiState.value.copy(breakMinutes = minutes)
    }
    
    fun updateLateThresholdMinutes(minutes: Int) {
        _uiState.value = _uiState.value.copy(lateThresholdMinutes = minutes)
    }
    
    fun updateEarlyThresholdMinutes(minutes: Int) {
        _uiState.value = _uiState.value.copy(earlyThresholdMinutes = minutes)
    }
    
    fun updatePenaltyRoundingMinutes(minutes: Int) {
        _uiState.value = _uiState.value.copy(penaltyRoundingMinutes = minutes)
    }
    
    fun updateShowPunch(showPunch: Boolean) {
        _uiState.value = _uiState.value.copy(showPunch = showPunch)
    }
    
    private suspend fun validateCurrentState() {
        val currentState = _uiState.value
        
        // Create temporary shift for validation
        val tempShift = Shift.createNew(
            name = currentState.name,
            startTime = currentState.startTime,
            officeEndTime = currentState.officeEndTime,
            endTime = currentState.endTime,
            departureTime = currentState.departureTime,
            daysApplied = currentState.daysApplied,
            remindBeforeStart = currentState.remindBeforeStart,
            remindAfterEnd = currentState.remindAfterEnd,
            showPunch = currentState.showPunch,
            breakMinutes = currentState.breakMinutes,
            lateThresholdMinutes = currentState.lateThresholdMinutes,
            earlyThresholdMinutes = currentState.earlyThresholdMinutes,
            penaltyRoundingMinutes = currentState.penaltyRoundingMinutes
        )
        
        // Get existing shifts for validation
        val existingShifts = shiftRepository.getShifts()
        val isEditing = _currentShiftId.value != null
        
        val validationResult = shiftValidator.validateShift(
            shift = tempShift,
            existingShifts = existingShifts,
            isEditing = isEditing
        )
        
        _uiState.value = currentState.copy(
            validationErrors = validationResult.errors,
            isValid = validationResult.isValid
        )
    }
    
    fun clearError() {
        _error.value = null
    }
}
