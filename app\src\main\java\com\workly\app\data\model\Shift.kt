package com.workly.app.data.model

import kotlinx.serialization.Serializable

@Serializable
data class Shift(
    val id: String,
    val name: String,
    val startTime: String, // HH:mm format
    val officeEndTime: String, // HH:mm format
    val endTime: String, // HH:mm format
    val departureTime: String, // HH:mm format
    val daysApplied: List<String>, // ["Mon", "<PERSON>e", "Wed", "Thu", "Fri"]
    val remindBeforeStart: Int = 15, // minutes
    val remindAfterEnd: Int = 15, // minutes
    val showPunch: Boolean = false,
    val breakMinutes: Int = 60,
    val lateThresholdMinutes: Int = 5,
    val earlyThresholdMinutes: Int = 5,
    val penaltyRoundingMinutes: Int = 30,
    val createdAt: String,
    val updatedAt: String
) {
    companion object {
        val DAYS_OF_WEEK = listOf("<PERSON>", "<PERSON><PERSON>", "We<PERSON>", "<PERSON>hu", "<PERSON><PERSON>", "<PERSON>t", "<PERSON>")
        
        fun createNew(
            name: String,
            startTime: String,
            officeEndTime: String,
            endTime: String,
            departureTime: String,
            daysApplied: List<String>,
            remindBeforeStart: Int = 15,
            remindAfterEnd: Int = 15,
            showPunch: Boolean = false,
            breakMinutes: Int = 60,
            lateThresholdMinutes: Int = 5,
            earlyThresholdMinutes: Int = 5,
            penaltyRoundingMinutes: Int = 30
        ): Shift {
            val now = kotlinx.datetime.Clock.System.now().toString()
            return Shift(
                id = java.util.UUID.randomUUID().toString(),
                name = name,
                startTime = startTime,
                officeEndTime = officeEndTime,
                endTime = endTime,
                departureTime = departureTime,
                daysApplied = daysApplied,
                remindBeforeStart = remindBeforeStart,
                remindAfterEnd = remindAfterEnd,
                showPunch = showPunch,
                breakMinutes = breakMinutes,
                lateThresholdMinutes = lateThresholdMinutes,
                earlyThresholdMinutes = earlyThresholdMinutes,
                penaltyRoundingMinutes = penaltyRoundingMinutes,
                createdAt = now,
                updatedAt = now
            )
        }
    }
    
    fun isOvernightShift(): Boolean {
        val start = parseTime(startTime)
        val end = parseTime(endTime)
        return end < start
    }
    
    fun isApplicableToday(dayOfWeek: String): Boolean {
        return daysApplied.contains(dayOfWeek)
    }
    
    private fun parseTime(timeString: String): Int {
        val parts = timeString.split(":")
        return parts[0].toInt() * 60 + parts[1].toInt()
    }
}
