package com.workly.app.ui.settings.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.workly.app.R
import com.workly.app.data.model.UserSettings

@Composable
fun GeneralSettingsSection(
    settings: UserSettings,
    onLanguageChange: (String) -> Unit,
    onThemeChange: (String) -> Unit,
    onMultiButtonModeChange: (String) -> Unit,
    onTimeFormatChange: (String) -> Unit,
    onFirstDayOfWeekChange: (String) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = stringResource(R.string.general_settings),
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            // Language Setting
            SettingsDropdownItem(
                icon = Icons.Default.Language,
                title = stringResource(R.string.language),
                currentValue = getLanguageDisplayName(settings.language),
                options = mapOf(
                    "vi" to "Tiếng Việt",
                    "en" to "English"
                ),
                onValueChange = onLanguageChange,
                currentKey = settings.language
            )
            
            // Theme Setting
            SettingsDropdownItem(
                icon = Icons.Default.Palette,
                title = stringResource(R.string.theme),
                currentValue = getThemeDisplayName(settings.theme),
                options = mapOf(
                    "light" to "Sáng",
                    "dark" to "Tối",
                    "system" to "Theo hệ thống"
                ),
                onValueChange = onThemeChange,
                currentKey = settings.theme
            )
            
            // Multi-button Mode
            SettingsDropdownItem(
                icon = Icons.Default.TouchApp,
                title = "Chế độ nút đa năng",
                currentValue = getMultiButtonModeDisplayName(settings.multiButtonMode),
                options = mapOf(
                    "full" to "Đầy đủ",
                    "simple" to "Đơn giản"
                ),
                onValueChange = onMultiButtonModeChange,
                currentKey = settings.multiButtonMode
            )
            
            // Time Format
            SettingsDropdownItem(
                icon = Icons.Default.AccessTime,
                title = "Định dạng giờ",
                currentValue = getTimeFormatDisplayName(settings.timeFormat),
                options = mapOf(
                    "24h" to "24 giờ",
                    "12h" to "12 giờ"
                ),
                onValueChange = onTimeFormatChange,
                currentKey = settings.timeFormat
            )
            
            // First Day of Week
            SettingsDropdownItem(
                icon = Icons.Default.CalendarToday,
                title = "Ngày bắt đầu tuần",
                currentValue = getFirstDayDisplayName(settings.firstDayOfWeek),
                options = mapOf(
                    "Mon" to "Thứ 2",
                    "Sun" to "Chủ nhật"
                ),
                onValueChange = onFirstDayOfWeekChange,
                currentKey = settings.firstDayOfWeek
            )
        }
    }
}

@Composable
private fun SettingsDropdownItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    currentValue: String,
    options: Map<String, String>,
    onValueChange: (String) -> Unit,
    currentKey: String
) {
    var expanded by remember { mutableStateOf(false) }
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(24.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium
            )
            
            ExposedDropdownMenuBox(
                expanded = expanded,
                onExpandedChange = { expanded = !expanded }
            ) {
                OutlinedTextField(
                    value = currentValue,
                    onValueChange = { },
                    readOnly = true,
                    modifier = Modifier
                        .fillMaxWidth()
                        .menuAnchor(),
                    trailingIcon = {
                        ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
                    }
                )
                
                ExposedDropdownMenu(
                    expanded = expanded,
                    onDismissRequest = { expanded = false }
                ) {
                    options.forEach { (key, displayName) ->
                        DropdownMenuItem(
                            text = { Text(displayName) },
                            onClick = {
                                onValueChange(key)
                                expanded = false
                            },
                            leadingIcon = if (key == currentKey) {
                                { Icon(Icons.Default.Check, contentDescription = null) }
                            } else null
                        )
                    }
                }
            }
        }
    }
}

private fun getLanguageDisplayName(language: String): String {
    return when (language) {
        "vi" -> "Tiếng Việt"
        "en" -> "English"
        else -> "Tiếng Việt"
    }
}

private fun getThemeDisplayName(theme: String): String {
    return when (theme) {
        "light" -> "Sáng"
        "dark" -> "Tối"
        "system" -> "Theo hệ thống"
        else -> "Theo hệ thống"
    }
}

private fun getMultiButtonModeDisplayName(mode: String): String {
    return when (mode) {
        "full" -> "Đầy đủ"
        "simple" -> "Đơn giản"
        else -> "Đầy đủ"
    }
}

private fun getTimeFormatDisplayName(format: String): String {
    return when (format) {
        "24h" -> "24 giờ"
        "12h" -> "12 giờ"
        else -> "24 giờ"
    }
}

private fun getFirstDayDisplayName(day: String): String {
    return when (day) {
        "Mon" -> "Thứ 2"
        "Sun" -> "Chủ nhật"
        else -> "Thứ 2"
    }
}
