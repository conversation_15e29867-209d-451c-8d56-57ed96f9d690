package com.workly.app.data.model

import kotlinx.serialization.Serializable

@Serializable
data class AttendanceLog(
    val type: AttendanceType,
    val time: String // ISO 8601 timestamp
)

enum class AttendanceType {
    GO_WORK,      // <PERSON><PERSON> làm
    CHECK_IN,     // Chấm công vào
    PUNCH,        // <PERSON><PERSON> công
    CHECK_OUT,    // Chấm công ra
    COMPLETE      // Hoàn tất
}

@Serializable
data class DailyAttendanceLogs(
    val date: String, // YYYY-MM-DD
    val logs: List<AttendanceLog>
) {
    fun getLogByType(type: AttendanceType): AttendanceLog? {
        return logs.find { it.type == type }
    }
    
    fun hasLogType(type: AttendanceType): <PERSON><PERSON>an {
        return logs.any { it.type == type }
    }
    
    fun getFirstLog(): AttendanceLog? = logs.firstOrNull()
    fun getLastLog(): AttendanceLog? = logs.lastOrNull()
}
