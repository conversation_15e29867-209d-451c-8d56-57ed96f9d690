{"logs": [{"outputFile": "com.workly.app-mergeDebugResources-77:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9209685f1972143d9ab1eeffcca4ab41\\transformed\\core-1.12.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3655,3753,3856,3956,4059,4164,4267,14167", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "3748,3851,3951,4054,4159,4262,4381,14263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\631285464bc3699b5e0c057776a78762\\transformed\\ui-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,378,482,574,650,737,826,910,985,1057,1145,1235,1309,1386,1454", "endColumns": "90,80,100,103,91,75,86,88,83,74,71,87,89,73,76,67,119", "endOffsets": "191,272,373,477,569,645,732,821,905,980,1052,1140,1230,1304,1381,1449,1569"}, "to": {"startLines": "48,49,82,83,85,90,91,140,141,142,143,145,146,149,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4682,4773,8534,8635,8811,9214,9290,13260,13349,13433,13508,13663,13751,14009,14374,14451,14519", "endColumns": "90,80,100,103,91,75,86,88,83,74,71,87,89,73,76,67,119", "endOffsets": "4768,4849,8630,8734,8898,9285,9372,13344,13428,13503,13575,13746,13836,14078,14446,14514,14634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\870103ce12d4503c553c2f969e6a0d11\\transformed\\navigation-ui-2.7.5\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,159", "endColumns": "103,126", "endOffsets": "154,281"}, "to": {"startLines": "138,139", "startColumns": "4,4", "startOffsets": "13029,13133", "endColumns": "103,126", "endOffsets": "13128,13255"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\85666ebb484fc6a37ee9760c5f520352\\transformed\\appcompat-1.2.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "280,389,487,597,683,789,913,999,1080,1172,1266,1362,1456,1557,1651,1747,1844,1936,2029,2111,2220,2329,2428,2537,2644,2755,2926,13926", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "384,482,592,678,784,908,994,1075,1167,1261,1357,1451,1552,1646,1742,1839,1931,2024,2106,2215,2324,2423,2532,2639,2750,2921,3020,14004"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b5d7701da16b09423affca14e07f926c\\transformed\\material-1.4.0-beta01\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,230,310,408,523,606,673,772,840,901,989,1055,1119,1190,1253,1307,1416,1475,1538,1592,1666,1791,1881,1961,2076,2159,2241,2330,2397,2463,2534,2614,2700,2778,2856,2929,3004,3091,3178,3269,3362,3434,3510,3602,3653,3719,3803,3889,3951,4015,4078,4185,4290,4386,4492", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,79,97,114,82,66,98,67,60,87,65,63,70,62,53,108,58,62,53,73,124,89,79,114,82,81,88,66,65,70,79,85,77,77,72,74,86,86,90,92,71,75,91,50,65,83,85,61,63,62,106,104,95,105,82", "endOffsets": "225,305,403,518,601,668,767,835,896,984,1050,1114,1185,1248,1302,1411,1470,1533,1587,1661,1786,1876,1956,2071,2154,2236,2325,2392,2458,2529,2609,2695,2773,2851,2924,2999,3086,3173,3264,3357,3429,3505,3597,3648,3714,3798,3884,3946,4010,4073,4180,4285,4381,4487,4570"}, "to": {"startLines": "2,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3575,4386,4484,4599,8903,9047,9146,9377,9438,9526,9592,9656,9727,9790,9844,9953,10012,10075,10129,10203,10328,10418,10498,10613,10696,10778,10867,10934,11000,11071,11151,11237,11315,11393,11466,11541,11628,11715,11806,11899,11971,12047,12139,12190,12256,12340,12426,12488,12552,12615,12722,12827,12923,13580", "endLines": "5,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "endColumns": "12,79,97,114,82,66,98,67,60,87,65,63,70,62,53,108,58,62,53,73,124,89,79,114,82,81,88,66,65,70,79,85,77,77,72,74,86,86,90,92,71,75,91,50,65,83,85,61,63,62,106,104,95,105,82", "endOffsets": "275,3650,4479,4594,4677,8965,9141,9209,9433,9521,9587,9651,9722,9785,9839,9948,10007,10070,10124,10198,10323,10413,10493,10608,10691,10773,10862,10929,10995,11066,11146,11232,11310,11388,11461,11536,11623,11710,11801,11894,11966,12042,12134,12185,12251,12335,12421,12483,12547,12610,12717,12822,12918,13024,13658"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d56bc5ab2f06c8c057783f978bdff8f7\\transformed\\material3-1.1.2\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,198,341,464,605,683,777,889,1025,1140,1281,1361,1462,1554,1650,1765,1881,1987,2126,2266,2397,2584,2705,2823,2944,3063,3156,3249,3373,3504,3598,3695,3797,3939,4086,4190,4285,4357,4434,4519,4603,4709,4785,4867,4958,5057,5144,5239,5325,5429,5525,5626,5740,5816,5916", "endColumns": "142,142,122,140,77,93,111,135,114,140,79,100,91,95,114,115,105,138,139,130,186,120,117,120,118,92,92,123,130,93,96,101,141,146,103,94,71,76,84,83,105,75,81,90,98,86,94,85,103,95,100,113,75,99,90", "endOffsets": "193,336,459,600,678,772,884,1020,1135,1276,1356,1457,1549,1645,1760,1876,1982,2121,2261,2392,2579,2700,2818,2939,3058,3151,3244,3368,3499,3593,3690,3792,3934,4081,4185,4280,4352,4429,4514,4598,4704,4780,4862,4953,5052,5139,5234,5320,5424,5520,5621,5735,5811,5911,6002"}, "to": {"startLines": "33,34,35,36,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,84,87,147,150,152,156,157,158,159,160,161,162,163,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3025,3168,3311,3434,4854,4932,5026,5138,5274,5389,5530,5610,5711,5803,5899,6014,6130,6236,6375,6515,6646,6833,6954,7072,7193,7312,7405,7498,7622,7753,7847,7944,8046,8188,8335,8439,8739,8970,13841,14083,14268,14639,14715,14797,14888,14987,15074,15169,15255,15359,15455,15556,15670,15746,15846", "endColumns": "142,142,122,140,77,93,111,135,114,140,79,100,91,95,114,115,105,138,139,130,186,120,117,120,118,92,92,123,130,93,96,101,141,146,103,94,71,76,84,83,105,75,81,90,98,86,94,85,103,95,100,113,75,99,90", "endOffsets": "3163,3306,3429,3570,4927,5021,5133,5269,5384,5525,5605,5706,5798,5894,6009,6125,6231,6370,6510,6641,6828,6949,7067,7188,7307,7400,7493,7617,7748,7842,7939,8041,8183,8330,8434,8529,8806,9042,13921,14162,14369,14710,14792,14883,14982,15069,15164,15250,15354,15450,15551,15665,15741,15841,15932"}}]}]}