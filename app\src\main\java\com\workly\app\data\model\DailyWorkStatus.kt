package com.workly.app.data.model

import kotlinx.serialization.Serializable

@Serializable
data class DailyWorkStatus(
    val date: String, // YYYY-MM-DD
    val shiftId: String?,
    val status: WorkStatus,
    val remarks: String = "",
    val vaoLogTime: String? = null, // Check-in time
    val raLogTime: String? = null,  // Check-out time
    val totalHours: Double = 0.0,
    val penaltyMinutes: Int = 0,
    val otHours: Double = 0.0,
    val lateMinutes: Int = 0,
    val earlyMinutes: Int = 0,
    val createdAt: String,
    val updatedAt: String
) {
    companion object {
        fun createNew(
            date: String,
            shiftId: String?,
            status: WorkStatus,
            remarks: String = ""
        ): DailyWorkStatus {
            val now = kotlinx.datetime.Clock.System.now().toString()
            return DailyWorkStatus(
                date = date,
                shiftId = shiftId,
                status = status,
                remarks = remarks,
                createdAt = now,
                updatedAt = now
            )
        }
    }
}

enum class WorkStatus(val displayName: String, val shortCode: String) {
    DU_CONG("Đủ công", "✅"),
    THIEU_LOG("Thiếu log", "❗"),
    DI_MUON("Đi muộn", "RV"),
    VE_SOM("Về sớm", "RV"),
    DI_MUON_VE_SOM("Đi muộn, về sớm", "RV"),
    NGHI_PHEP("Nghỉ phép", "P"),
    NGHI_BENH("Nghỉ bệnh", "B"),
    NGHI_LE("Nghỉ lễ", "H"),
    VANG_MAT("Vắng mặt", "X"),
    UNKNOWN("Chưa cập nhật", "--");
    
    fun getIcon(): String {
        return when (this) {
            DU_CONG -> "✅"
            THIEU_LOG -> "❗"
            DI_MUON, VE_SOM, DI_MUON_VE_SOM -> "RV"
            NGHI_PHEP -> "📩"
            NGHI_BENH -> "🛌"
            NGHI_LE -> "🎌"
            VANG_MAT -> "❌"
            UNKNOWN -> "❓"
        }
    }
}
