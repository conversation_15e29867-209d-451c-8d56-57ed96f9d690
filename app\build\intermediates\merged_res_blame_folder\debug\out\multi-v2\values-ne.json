{"logs": [{"outputFile": "com.workly.app-mergeDebugResources-77:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\631285464bc3699b5e0c057776a78762\\transformed\\ui-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,294,388,485,571,653,749,836,922,988,1054,1144,1237,1314,1395,1463", "endColumns": "98,89,93,96,85,81,95,86,85,65,65,89,92,76,80,67,119", "endOffsets": "199,289,383,480,566,648,744,831,917,983,1049,1139,1232,1309,1390,1458,1578"}, "to": {"startLines": "48,49,82,83,85,90,91,140,141,142,143,145,146,149,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4627,4726,8575,8669,8837,9235,9317,13364,13451,13537,13603,13753,13843,14103,14470,14551,14619", "endColumns": "98,89,93,96,85,81,95,86,85,65,65,89,92,76,80,67,119", "endOffsets": "4721,4811,8664,8761,8918,9312,9408,13446,13532,13598,13664,13838,13931,14175,14546,14614,14734"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\870103ce12d4503c553c2f969e6a0d11\\transformed\\navigation-ui-2.7.5\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,172", "endColumns": "116,119", "endOffsets": "167,287"}, "to": {"startLines": "138,139", "startColumns": "4,4", "startOffsets": "13127,13244", "endColumns": "116,119", "endOffsets": "13239,13359"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9209685f1972143d9ab1eeffcca4ab41\\transformed\\core-1.12.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "38,39,40,41,42,43,44,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3608,3711,3814,3916,4022,4120,4220,14266", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "3706,3809,3911,4017,4115,4215,4323,14362"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d56bc5ab2f06c8c057783f978bdff8f7\\transformed\\material3-1.1.2\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,295,407,531,615,706,815,955,1072,1221,1301,1405,1499,1594,1702,1822,1931,2073,2212,2342,2503,2629,2777,2931,3057,3152,3243,3355,3475,3575,3691,3796,3937,4081,4187,4290,4361,4445,4532,4618,4721,4797,4878,4975,5080,5171,5270,5353,5460,5555,5655,5786,5862,5962", "endColumns": "124,114,111,123,83,90,108,139,116,148,79,103,93,94,107,119,108,141,138,129,160,125,147,153,125,94,90,111,119,99,115,104,140,143,105,102,70,83,86,85,102,75,80,96,104,90,98,82,106,94,99,130,75,99,89", "endOffsets": "175,290,402,526,610,701,810,950,1067,1216,1296,1400,1494,1589,1697,1817,1926,2068,2207,2337,2498,2624,2772,2926,3052,3147,3238,3350,3470,3570,3686,3791,3932,4076,4182,4285,4356,4440,4527,4613,4716,4792,4873,4970,5075,5166,5265,5348,5455,5550,5650,5781,5857,5957,6047"}, "to": {"startLines": "33,34,35,36,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,84,87,147,150,152,156,157,158,159,160,161,162,163,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3050,3175,3290,3402,4816,4900,4991,5100,5240,5357,5506,5586,5690,5784,5879,5987,6107,6216,6358,6497,6627,6788,6914,7062,7216,7342,7437,7528,7640,7760,7860,7976,8081,8222,8366,8472,8766,8989,13936,14180,14367,14739,14815,14896,14993,15098,15189,15288,15371,15478,15573,15673,15804,15880,15980", "endColumns": "124,114,111,123,83,90,108,139,116,148,79,103,93,94,107,119,108,141,138,129,160,125,147,153,125,94,90,111,119,99,115,104,140,143,105,102,70,83,86,85,102,75,80,96,104,90,98,82,106,94,99,130,75,99,89", "endOffsets": "3170,3285,3397,3521,4895,4986,5095,5235,5352,5501,5581,5685,5779,5874,5982,6102,6211,6353,6492,6622,6783,6909,7057,7211,7337,7432,7523,7635,7755,7855,7971,8076,8217,8361,8467,8570,8832,9068,14018,14261,14465,14810,14891,14988,15093,15184,15283,15366,15473,15568,15668,15799,15875,15975,16065"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b5d7701da16b09423affca14e07f926c\\transformed\\material-1.4.0-beta01\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,217,299,401,511,598,664,760,826,887,992,1064,1122,1196,1258,1312,1425,1485,1546,1605,1683,1807,1888,1973,2079,2160,2243,2326,2393,2459,2536,2615,2703,2772,2848,2929,2997,3088,3166,3259,3356,3430,3509,3607,3667,3733,3821,3909,3971,4039,4102,4207,4325,4420,4540", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,101,109,86,65,95,65,60,104,71,57,73,61,53,112,59,60,58,77,123,80,84,105,80,82,82,66,65,76,78,87,68,75,80,67,90,77,92,96,73,78,97,59,65,87,87,61,67,62,104,117,94,119,83", "endOffsets": "212,294,396,506,593,659,755,821,882,987,1059,1117,1191,1253,1307,1420,1480,1541,1600,1678,1802,1883,1968,2074,2155,2238,2321,2388,2454,2531,2610,2698,2767,2843,2924,2992,3083,3161,3254,3351,3425,3504,3602,3662,3728,3816,3904,3966,4034,4097,4202,4320,4415,4535,4619"}, "to": {"startLines": "2,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3526,4328,4430,4540,8923,9073,9169,9413,9474,9579,9651,9709,9783,9845,9899,10012,10072,10133,10192,10270,10394,10475,10560,10666,10747,10830,10913,10980,11046,11123,11202,11290,11359,11435,11516,11584,11675,11753,11846,11943,12017,12096,12194,12254,12320,12408,12496,12558,12626,12689,12794,12912,13007,13669", "endLines": "5,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "endColumns": "12,81,101,109,86,65,95,65,60,104,71,57,73,61,53,112,59,60,58,77,123,80,84,105,80,82,82,66,65,76,78,87,68,75,80,67,90,77,92,96,73,78,97,59,65,87,87,61,67,62,104,117,94,119,83", "endOffsets": "262,3603,4425,4535,4622,8984,9164,9230,9469,9574,9646,9704,9778,9840,9894,10007,10067,10128,10187,10265,10389,10470,10555,10661,10742,10825,10908,10975,11041,11118,11197,11285,11354,11430,11511,11579,11670,11748,11841,11938,12012,12091,12189,12249,12315,12403,12491,12553,12621,12684,12789,12907,13002,13122,13748"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\85666ebb484fc6a37ee9760c5f520352\\transformed\\appcompat-1.2.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,327,435,526,633,760,844,923,1014,1107,1202,1296,1396,1489,1584,1678,1769,1860,1946,2059,2160,2256,2369,2479,2603,2777,2888", "endColumns": "110,110,107,90,106,126,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,123,173,110,79", "endOffsets": "211,322,430,521,628,755,839,918,1009,1102,1197,1291,1391,1484,1579,1673,1764,1855,1941,2054,2155,2251,2364,2474,2598,2772,2883,2963"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "267,378,489,597,688,795,922,1006,1085,1176,1269,1364,1458,1558,1651,1746,1840,1931,2022,2108,2221,2322,2418,2531,2641,2765,2939,14023", "endColumns": "110,110,107,90,106,126,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,123,173,110,79", "endOffsets": "373,484,592,683,790,917,1001,1080,1171,1264,1359,1453,1553,1646,1741,1835,1926,2017,2103,2216,2317,2413,2526,2636,2760,2934,3045,14098"}}]}]}