package com.workly.app.data.storage

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.first

class DataStoreManager(private val context: Context) {
    
    private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "workly_preferences")
    private val gson = Gson()
    
    // Keys for different data types
    companion object {
        val USER_SETTINGS_KEY = stringPreferencesKey("user_settings")
        val SHIFT_LIST_KEY = stringPreferencesKey("shift_list")
        val ACTIVE_SHIFT_ID_KEY = stringPreferencesKey("active_shift_id")
        val ATTENDANCE_LOGS_KEY = stringPreferencesKey("attendance_logs")
        val DAILY_WORK_STATUS_KEY = stringPreferencesKey("daily_work_status")
        val NOTES_KEY = stringPreferencesKey("notes")
        val LAST_AUTO_RESET_TIME_KEY = stringPreferencesKey("last_auto_reset_time")
    }
    
    // Generic methods for storing and retrieving JSON data
    suspend fun <T> storeData(key: Preferences.Key<String>, data: T) {
        try {
            val jsonString = gson.toJson(data)
            context.dataStore.edit { preferences ->
                preferences[key] = jsonString
            }
        } catch (e: Exception) {
            throw DataStoreException("Failed to store data for key: ${key.name}", e)
        }
    }
    
    suspend fun <T> getData(key: Preferences.Key<String>, type: TypeToken<T>, defaultValue: T): T {
        return try {
            val jsonString = context.dataStore.data.first()[key]
            if (jsonString != null) {
                gson.fromJson(jsonString, type.type) ?: defaultValue
            } else {
                defaultValue
            }
        } catch (e: Exception) {
            throw DataStoreException("Failed to retrieve data for key: ${key.name}", e)
        }
    }
    
    fun <T> getDataFlow(key: Preferences.Key<String>, type: TypeToken<T>, defaultValue: T): Flow<T> {
        return context.dataStore.data.map { preferences ->
            try {
                val jsonString = preferences[key]
                if (jsonString != null) {
                    gson.fromJson(jsonString, type.type) ?: defaultValue
                } else {
                    defaultValue
                }
            } catch (e: Exception) {
                defaultValue
            }
        }
    }
    
    // String-specific methods
    suspend fun storeString(key: Preferences.Key<String>, value: String) {
        try {
            context.dataStore.edit { preferences ->
                preferences[key] = value
            }
        } catch (e: Exception) {
            throw DataStoreException("Failed to store string for key: ${key.name}", e)
        }
    }
    
    suspend fun getString(key: Preferences.Key<String>, defaultValue: String = ""): String {
        return try {
            context.dataStore.data.first()[key] ?: defaultValue
        } catch (e: Exception) {
            throw DataStoreException("Failed to retrieve string for key: ${key.name}", e)
        }
    }
    
    fun getStringFlow(key: Preferences.Key<String>, defaultValue: String = ""): Flow<String> {
        return context.dataStore.data.map { preferences ->
            preferences[key] ?: defaultValue
        }
    }
    
    // Clear specific key
    suspend fun clearKey(key: Preferences.Key<String>) {
        try {
            context.dataStore.edit { preferences ->
                preferences.remove(key)
            }
        } catch (e: Exception) {
            throw DataStoreException("Failed to clear key: ${key.name}", e)
        }
    }
    
    // Clear all data
    suspend fun clearAll() {
        try {
            context.dataStore.edit { preferences ->
                preferences.clear()
            }
        } catch (e: Exception) {
            throw DataStoreException("Failed to clear all data", e)
        }
    }
    
    // Export all data for backup
    suspend fun exportAllData(): Map<String, String> {
        return try {
            val preferences = context.dataStore.data.first()
            preferences.asMap().mapKeys { it.key.name }.mapValues { it.value.toString() }
        } catch (e: Exception) {
            throw DataStoreException("Failed to export data", e)
        }
    }
    
    // Import data from backup
    suspend fun importAllData(data: Map<String, String>) {
        try {
            context.dataStore.edit { preferences ->
                preferences.clear()
                data.forEach { (keyName, value) ->
                    val key = stringPreferencesKey(keyName)
                    preferences[key] = value
                }
            }
        } catch (e: Exception) {
            throw DataStoreException("Failed to import data", e)
        }
    }
}

class DataStoreException(message: String, cause: Throwable? = null) : Exception(message, cause)
