<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.Workly" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Customize your light theme here. -->
        <item name="colorPrimary">@color/workly_primary</item>
        <item name="colorPrimaryVariant">@color/workly_primary_variant</item>
        <item name="colorSecondary">@color/workly_secondary</item>
        <item name="colorSecondaryVariant">@color/workly_secondary_variant</item>
    </style>
    
    <!-- Alarm activity theme -->
    <style name="Theme.Workly.Alarm" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="android:windowShowWallpaper">false</item>
        <item name="android:windowBackground">@color/workly_primary</item>
        <item name="android:windowTurnScreenOn">true</item>
        <item name="android:showOnLockScreen">true</item>
        <item name="android:windowDisablePreview">true</item>
    </style>
</resources>
