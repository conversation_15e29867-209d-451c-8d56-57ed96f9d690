package com.workly.app.ui.settings.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp

@Composable
fun DataManagementSection(
    onExportData: ((String) -> Unit) -> Unit,
    onImportData: (String) -> Unit,
    onResetSettings: () -> Unit,
    onClearAllData: () -> Unit
) {
    var showResetDialog by remember { mutableStateOf(false) }
    var showClearDataDialog by remember { mutableStateOf(false) }
    val context = LocalContext.current
    
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Quản lý dữ liệu",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            // Export Data
            DataActionItem(
                icon = Icons.Default.Upload,
                title = "Xuất dữ liệu",
                subtitle = "Sao lưu toàn bộ dữ liệu ứng dụng",
                onClick = {
                    onExportData { jsonData ->
                        // TODO: Share or save the exported data
                        // This would typically open a file picker or sharing dialog
                    }
                }
            )
            
            // Import Data
            DataActionItem(
                icon = Icons.Default.Download,
                title = "Nhập dữ liệu",
                subtitle = "Khôi phục dữ liệu từ file sao lưu",
                onClick = {
                    // TODO: Open file picker to select backup file
                    // For now, this is a placeholder
                }
            )
            
            Divider()
            
            // Reset Settings
            DataActionItem(
                icon = Icons.Default.RestartAlt,
                title = "Khôi phục cài đặt",
                subtitle = "Đặt lại tất cả cài đặt về mặc định",
                onClick = { showResetDialog = true },
                isDestructive = false
            )
            
            // Clear All Data
            DataActionItem(
                icon = Icons.Default.DeleteForever,
                title = "Xóa toàn bộ dữ liệu",
                subtitle = "Xóa tất cả dữ liệu và cài đặt",
                onClick = { showClearDataDialog = true },
                isDestructive = true
            )
        }
    }
    
    // Reset Settings Dialog
    if (showResetDialog) {
        AlertDialog(
            onDismissRequest = { showResetDialog = false },
            title = { Text("Khôi phục cài đặt") },
            text = { 
                Text("Bạn có chắc chắn muốn đặt lại tất cả cài đặt về mặc định? Dữ liệu ca làm việc và ghi chú sẽ được giữ nguyên.")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showResetDialog = false
                        onResetSettings()
                    }
                ) {
                    Text("Khôi phục")
                }
            },
            dismissButton = {
                TextButton(onClick = { showResetDialog = false }) {
                    Text("Hủy")
                }
            }
        )
    }
    
    // Clear All Data Dialog
    if (showClearDataDialog) {
        AlertDialog(
            onDismissRequest = { showClearDataDialog = false },
            title = { Text("Xóa toàn bộ dữ liệu") },
            text = { 
                Text("CẢNH BÁO: Thao tác này sẽ xóa vĩnh viễn tất cả dữ liệu bao gồm ca làm việc, ghi chú, lịch sử chấm công và cài đặt. Bạn có chắc chắn muốn tiếp tục?")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showClearDataDialog = false
                        onClearAllData()
                    }
                ) {
                    Text("Xóa tất cả", color = MaterialTheme.colorScheme.error)
                }
            },
            dismissButton = {
                TextButton(onClick = { showClearDataDialog = false }) {
                    Text("Hủy")
                }
            }
        )
    }
}

@Composable
private fun DataActionItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    subtitle: String,
    onClick: () -> Unit,
    isDestructive: Boolean = false
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(24.dp),
            tint = if (isDestructive) {
                MaterialTheme.colorScheme.error
            } else {
                MaterialTheme.colorScheme.onSurfaceVariant
            }
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium,
                color = if (isDestructive) {
                    MaterialTheme.colorScheme.error
                } else {
                    MaterialTheme.colorScheme.onSurface
                }
            )
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Icon(
            imageVector = Icons.Default.ChevronRight,
            contentDescription = null,
            modifier = Modifier.size(20.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}
