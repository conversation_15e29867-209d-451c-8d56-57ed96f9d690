package com.workly.app.data.initializer

import android.content.Context
import com.workly.app.data.model.ShiftTemplates
import com.workly.app.data.repository.ShiftRepository
import com.workly.app.data.storage.DataStoreManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Initializes default shift data when the app is first launched
 */
class ShiftDataInitializer(
    private val context: Context,
    private val shiftRepository: ShiftRepository
) {
    
    companion object {
        private const val PREF_KEY_SHIFTS_INITIALIZED = "shifts_initialized"
    }
    
    private val dataStoreManager = DataStoreManager(context)
    
    /**
     * Initialize default shifts if not already done
     */
    suspend fun initializeDefaultShifts() {
        try {
            // Check if shifts have already been initialized
            val isInitialized = dataStoreManager.getString(PREF_KEY_SHIFTS_INITIALIZED)
            if (isInitialized == "true") {
                return
            }
            
            // Check if there are already existing shifts
            val existingShifts = shiftRepository.getShifts()
            if (existingShifts.isNotEmpty()) {
                // Mark as initialized if there are existing shifts
                markAsInitialized()
                return
            }
            
            // Initialize with default shift templates
            val defaultShifts = ShiftTemplates.getAllTemplates()
            
            // Add each shift to repository
            defaultShifts.forEach { shift ->
                shiftRepository.addShift(shift)
            }
            
            // Set the administrative shift as default active shift
            val adminShift = defaultShifts.find { it.name == "Ca hành chính" }
            adminShift?.let { 
                shiftRepository.setActiveShift(it.id)
            }
            
            // Mark initialization as complete
            markAsInitialized()
            
        } catch (e: Exception) {
            // Log error but don't crash the app
            e.printStackTrace()
        }
    }
    
    /**
     * Initialize shifts asynchronously
     */
    fun initializeAsync() {
        CoroutineScope(Dispatchers.IO).launch {
            initializeDefaultShifts()
        }
    }
    
    /**
     * Force re-initialization of default shifts
     * This will add default shifts even if they already exist
     */
    suspend fun forceReinitialize() {
        try {
            val defaultShifts = ShiftTemplates.getAllTemplates()
            val existingShifts = shiftRepository.getShifts()
            
            // Only add shifts that don't already exist (by name)
            val existingNames = existingShifts.map { it.name }.toSet()
            val newShifts = defaultShifts.filter { it.name !in existingNames }
            
            newShifts.forEach { shift ->
                shiftRepository.addShift(shift)
            }
            
            markAsInitialized()
            
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * Reset all shifts to default templates
     * WARNING: This will delete all existing shifts
     */
    suspend fun resetToDefaults() {
        try {
            // Clear all existing shifts
            val existingShifts = shiftRepository.getShifts()
            existingShifts.forEach { shift ->
                shiftRepository.deleteShift(shift.id)
            }
            
            // Clear initialization flag
            dataStoreManager.clearKey(PREF_KEY_SHIFTS_INITIALIZED)
            
            // Re-initialize with defaults
            initializeDefaultShifts()
            
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * Check if default shifts have been initialized
     */
    suspend fun isInitialized(): Boolean {
        return try {
            dataStoreManager.getString(PREF_KEY_SHIFTS_INITIALIZED) == "true"
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Get available shift templates that are not yet added
     */
    suspend fun getAvailableTemplates(): List<String> {
        return try {
            val existingShifts = shiftRepository.getShifts()
            val existingNames = existingShifts.map { it.name }.toSet()
            val allTemplateNames = ShiftTemplates.getTemplateNames()
            
            allTemplateNames.filter { it !in existingNames }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * Add a specific template by name
     */
    suspend fun addTemplate(templateName: String): Boolean {
        return try {
            val template = ShiftTemplates.getTemplateByName(templateName)
            if (template != null) {
                // Check if shift with this name already exists
                val exists = shiftRepository.isShiftNameExists(templateName)
                if (!exists) {
                    shiftRepository.addShift(template)
                    true
                } else {
                    false // Already exists
                }
            } else {
                false // Template not found
            }
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    private suspend fun markAsInitialized() {
        dataStoreManager.storeString(PREF_KEY_SHIFTS_INITIALIZED, "true")
    }
}
