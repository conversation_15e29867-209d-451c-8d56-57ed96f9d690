{"logs": [{"outputFile": "com.workly.app-mergeDebugResources-77:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9209685f1972143d9ab1eeffcca4ab41\\transformed\\core-1.12.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "38,39,40,41,42,43,44,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3510,3609,3711,3809,3906,4014,4125,14059", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "3604,3706,3804,3901,4009,4120,4242,14155"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b5d7701da16b09423affca14e07f926c\\transformed\\material-1.4.0-beta01\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,222,308,409,535,618,683,783,853,912,1010,1074,1133,1205,1268,1322,1439,1496,1558,1612,1684,1819,1902,1980,2091,2175,2257,2347,2414,2480,2551,2630,2718,2794,2872,2944,3017,3106,3178,3272,3371,3445,3517,3618,3668,3734,3824,3913,3975,4039,4102,4218,4326,4435,4544", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,85,100,125,82,64,99,69,58,97,63,58,71,62,53,116,56,61,53,71,134,82,77,110,83,81,89,66,65,70,78,87,75,77,71,72,88,71,93,98,73,71,100,49,65,89,88,61,63,62,115,107,108,108,82", "endOffsets": "217,303,404,530,613,678,778,848,907,1005,1069,1128,1200,1263,1317,1434,1491,1553,1607,1679,1814,1897,1975,2086,2170,2252,2342,2409,2475,2546,2625,2713,2789,2867,2939,3012,3101,3173,3267,3366,3440,3512,3613,3663,3729,3819,3908,3970,4034,4097,4213,4321,4430,4539,4622"}, "to": {"startLines": "2,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3424,4247,4348,4474,8762,8904,9004,9248,9307,9405,9469,9528,9600,9663,9717,9834,9891,9953,10007,10079,10214,10297,10375,10486,10570,10652,10742,10809,10875,10946,11025,11113,11189,11267,11339,11412,11501,11573,11667,11766,11840,11912,12013,12063,12129,12219,12308,12370,12434,12497,12613,12721,12830,13482", "endLines": "5,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "endColumns": "12,85,100,125,82,64,99,69,58,97,63,58,71,62,53,116,56,61,53,71,134,82,77,110,83,81,89,66,65,70,78,87,75,77,71,72,88,71,93,98,73,71,100,49,65,89,88,61,63,62,115,107,108,108,82", "endOffsets": "267,3505,4343,4469,4552,8822,8999,9069,9302,9400,9464,9523,9595,9658,9712,9829,9886,9948,10002,10074,10209,10292,10370,10481,10565,10647,10737,10804,10870,10941,11020,11108,11184,11262,11334,11407,11496,11568,11662,11761,11835,11907,12008,12058,12124,12214,12303,12365,12429,12492,12608,12716,12825,12934,13560"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d56bc5ab2f06c8c057783f978bdff8f7\\transformed\\material3-1.1.2\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,282,397,511,587,678,787,921,1033,1175,1255,1351,1439,1534,1648,1768,1869,2002,2132,2272,2457,2591,2710,2831,2954,3043,3135,3256,3393,3484,3587,3692,3826,3967,4074,4168,4241,4318,4400,4479,4580,4656,4735,4830,4927,5018,5112,5196,5301,5397,5495,5619,5695,5805", "endColumns": "114,111,114,113,75,90,108,133,111,141,79,95,87,94,113,119,100,132,129,139,184,133,118,120,122,88,91,120,136,90,102,104,133,140,106,93,72,76,81,78,100,75,78,94,96,90,93,83,104,95,97,123,75,109,102", "endOffsets": "165,277,392,506,582,673,782,916,1028,1170,1250,1346,1434,1529,1643,1763,1864,1997,2127,2267,2452,2586,2705,2826,2949,3038,3130,3251,3388,3479,3582,3687,3821,3962,4069,4163,4236,4313,4395,4474,4575,4651,4730,4825,4922,5013,5107,5191,5296,5392,5490,5614,5690,5800,5903"}, "to": {"startLines": "33,34,35,36,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,84,87,147,150,152,156,157,158,159,160,161,162,163,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2968,3083,3195,3310,4735,4811,4902,5011,5145,5257,5399,5479,5575,5663,5758,5872,5992,6093,6226,6356,6496,6681,6815,6934,7055,7178,7267,7359,7480,7617,7708,7811,7916,8050,8191,8298,8593,8827,13743,13980,14160,14525,14601,14680,14775,14872,14963,15057,15141,15246,15342,15440,15564,15640,15750", "endColumns": "114,111,114,113,75,90,108,133,111,141,79,95,87,94,113,119,100,132,129,139,184,133,118,120,122,88,91,120,136,90,102,104,133,140,106,93,72,76,81,78,100,75,78,94,96,90,93,83,104,95,97,123,75,109,102", "endOffsets": "3078,3190,3305,3419,4806,4897,5006,5140,5252,5394,5474,5570,5658,5753,5867,5987,6088,6221,6351,6491,6676,6810,6929,7050,7173,7262,7354,7475,7612,7703,7806,7911,8045,8186,8293,8387,8661,8899,13820,14054,14256,14596,14675,14770,14867,14958,15052,15136,15241,15337,15435,15559,15635,15745,15848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\85666ebb484fc6a37ee9760c5f520352\\transformed\\appcompat-1.2.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "272,386,486,598,684,790,913,995,1073,1164,1257,1352,1446,1547,1640,1735,1832,1923,2016,2097,2203,2307,2405,2511,2615,2717,2871,13825", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "381,481,593,679,785,908,990,1068,1159,1252,1347,1441,1542,1635,1730,1827,1918,2011,2092,2198,2302,2400,2506,2610,2712,2866,2963,13902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\870103ce12d4503c553c2f969e6a0d11\\transformed\\navigation-ui-2.7.5\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,118", "endOffsets": "156,275"}, "to": {"startLines": "138,139", "startColumns": "4,4", "startOffsets": "12939,13045", "endColumns": "105,118", "endOffsets": "13040,13159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\631285464bc3699b5e0c057776a78762\\transformed\\ui-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,382,484,580,661,754,846,936,1005,1072,1159,1250,1323,1400,1466", "endColumns": "94,82,98,101,95,80,92,91,89,68,66,86,90,72,76,65,120", "endOffsets": "195,278,377,479,575,656,749,841,931,1000,1067,1154,1245,1318,1395,1461,1582"}, "to": {"startLines": "48,49,82,83,85,90,91,140,141,142,143,145,146,149,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4557,4652,8392,8491,8666,9074,9155,13164,13256,13346,13415,13565,13652,13907,14261,14338,14404", "endColumns": "94,82,98,101,95,80,92,91,89,68,66,86,90,72,76,65,120", "endOffsets": "4647,4730,8486,8588,8757,9150,9243,13251,13341,13410,13477,13647,13738,13975,14333,14399,14520"}}]}]}