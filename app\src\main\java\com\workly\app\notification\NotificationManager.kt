package com.workly.app.notification

import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import androidx.core.app.NotificationCompat
import com.workly.app.R
import com.workly.app.WorklyApplication
import com.workly.app.ui.MainActivity

class NotificationManager(private val context: Context) {
    
    private val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    
    companion object {
        private const val WEATHER_WARNING_ID = 1001
        private const val SHIFT_REMINDER_ID = 1002
        private const val GENERAL_NOTIFICATION_ID = 1003
        private const val AUTO_RESET_ID = 1004
    }
    
    /**
     * Show weather warning notification
     */
    fun showWeatherWarning(
        title: String,
        message: String,
        weatherType: String
    ) {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, WorklyApplication.WEATHER_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle(title)
            .setContentText(message)
            .setStyle(NotificationCompat.BigTextStyle().bigText(message))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .addAction(
                R.drawable.ic_launcher_foreground,
                "Đã biết",
                createDismissIntent(WEATHER_WARNING_ID)
            )
            .build()
        
        notificationManager.notify(WEATHER_WARNING_ID, notification)
    }
    
    /**
     * Show shift change reminder notification
     */
    fun showShiftChangeReminder(
        currentShiftName: String,
        nextWeekStart: String
    ) {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            putExtra("navigate_to", "shifts")
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val title = "Nhắc nhở đổi ca tuần"
        val message = "Ca hiện tại: $currentShiftName. Bạn có muốn thay đổi ca cho tuần tới ($nextWeekStart)?"
        
        val notification = NotificationCompat.Builder(context, WorklyApplication.GENERAL_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle(title)
            .setContentText(message)
            .setStyle(NotificationCompat.BigTextStyle().bigText(message))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .addAction(
                R.drawable.ic_launcher_foreground,
                "Thay đổi ca",
                pendingIntent
            )
            .addAction(
                R.drawable.ic_launcher_foreground,
                "Giữ nguyên",
                createDismissIntent(SHIFT_REMINDER_ID)
            )
            .build()
        
        notificationManager.notify(SHIFT_REMINDER_ID, notification)
    }
    
    /**
     * Show auto reset notification
     */
    fun showAutoResetNotification() {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val title = "Dữ liệu đã được reset"
        val message = "Dữ liệu chấm công hôm nay đã được reset tự động để chuẩn bị cho ca làm việc mới."
        
        val notification = NotificationCompat.Builder(context, WorklyApplication.GENERAL_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle(title)
            .setContentText(message)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .build()
        
        notificationManager.notify(AUTO_RESET_ID, notification)
    }
    
    /**
     * Show general notification
     */
    fun showGeneralNotification(
        title: String,
        message: String,
        priority: Int = NotificationCompat.PRIORITY_DEFAULT
    ) {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, WorklyApplication.GENERAL_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle(title)
            .setContentText(message)
            .setStyle(NotificationCompat.BigTextStyle().bigText(message))
            .setPriority(priority)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .build()
        
        notificationManager.notify(GENERAL_NOTIFICATION_ID, notification)
    }
    
    /**
     * Dismiss weather warning
     */
    fun dismissWeatherWarning() {
        notificationManager.cancel(WEATHER_WARNING_ID)
    }
    
    /**
     * Dismiss shift reminder
     */
    fun dismissShiftReminder() {
        notificationManager.cancel(SHIFT_REMINDER_ID)
    }
    
    /**
     * Dismiss all notifications
     */
    fun dismissAll() {
        notificationManager.cancelAll()
    }
    
    private fun createDismissIntent(notificationId: Int): PendingIntent {
        val intent = Intent(context, NotificationDismissReceiver::class.java).apply {
            putExtra("notification_id", notificationId)
        }
        
        return PendingIntent.getBroadcast(
            context,
            notificationId,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }
    
    /**
     * Check if notifications are enabled
     */
    fun areNotificationsEnabled(): Boolean {
        return notificationManager.areNotificationsEnabled()
    }
    
    /**
     * Get notification settings info
     */
    fun getNotificationInfo(): NotificationInfo {
        val enabled = notificationManager.areNotificationsEnabled()
        
        val channelInfo = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            val alarmChannel = notificationManager.getNotificationChannel(WorklyApplication.ALARM_CHANNEL_ID)
            val weatherChannel = notificationManager.getNotificationChannel(WorklyApplication.WEATHER_CHANNEL_ID)
            val generalChannel = notificationManager.getNotificationChannel(WorklyApplication.GENERAL_CHANNEL_ID)
            
            NotificationChannelInfo(
                alarmEnabled = alarmChannel?.importance != NotificationManager.IMPORTANCE_NONE,
                weatherEnabled = weatherChannel?.importance != NotificationManager.IMPORTANCE_NONE,
                generalEnabled = generalChannel?.importance != NotificationManager.IMPORTANCE_NONE
            )
        } else {
            NotificationChannelInfo(true, true, true)
        }
        
        return NotificationInfo(
            notificationsEnabled = enabled,
            channelInfo = channelInfo
        )
    }
}

data class NotificationInfo(
    val notificationsEnabled: Boolean,
    val channelInfo: NotificationChannelInfo
)

data class NotificationChannelInfo(
    val alarmEnabled: Boolean,
    val weatherEnabled: Boolean,
    val generalEnabled: Boolean
)
