package com.workly.app.data.model

/**
 * Predefined shift templates for common work schedules in Vietnamese enterprises
 */
object ShiftTemplates {
    
    /**
     * Get all predefined shift templates
     */
    fun getAllTemplates(): List<Shift> {
        return listOf(
            getAdministrativeShift(),
            getDayShift(),
            getNightShift(),
            getShift1(),
            getShift2(),
            getShift3(),
            getRotatingShift()
        )
    }
    
    /**
     * Ca hành chính - Administrative shift (8:00-17:00)
     * Standard office hours with 1-hour lunch break
     */
    fun getAdministrativeShift(): Shift {
        return Shift.createNew(
            name = "Ca hành chính",
            startTime = "08:00",
            officeEndTime = "17:00",
            endTime = "17:30",
            departureTime = "07:30",
            daysApplied = listOf("Mon", "Tue", "Wed", "Thu", "Fri"),
            remindBeforeStart = 30,
            remindAfterEnd = 15,
            showPunch = true,
            breakMinutes = 60,
            lateThresholdMinutes = 10,
            earlyThresholdMinutes = 10,
            penaltyRoundingMinutes = 30
        )
    }
    
    /**
     * Ca ngày - Day shift (6:00-14:00)
     * Early morning to afternoon shift
     */
    fun getDayShift(): Shift {
        return Shift.createNew(
            name = "Ca ngày",
            startTime = "06:00",
            officeEndTime = "14:00",
            endTime = "14:30",
            departureTime = "05:30",
            daysApplied = listOf("Mon", "Tue", "Wed", "Thu", "Fri", "Sat"),
            remindBeforeStart = 45,
            remindAfterEnd = 15,
            showPunch = true,
            breakMinutes = 30,
            lateThresholdMinutes = 5,
            earlyThresholdMinutes = 5,
            penaltyRoundingMinutes = 15
        )
    }
    
    /**
     * Ca đêm - Night shift (22:00-06:00)
     * Night shift with overnight work
     */
    fun getNightShift(): Shift {
        return Shift.createNew(
            name = "Ca đêm",
            startTime = "22:00",
            officeEndTime = "06:00",
            endTime = "06:30",
            departureTime = "21:30",
            daysApplied = listOf("Mon", "Tue", "Wed", "Thu", "Fri", "Sat"),
            remindBeforeStart = 60,
            remindAfterEnd = 30,
            showPunch = true,
            breakMinutes = 60,
            lateThresholdMinutes = 10,
            earlyThresholdMinutes = 10,
            penaltyRoundingMinutes = 30
        )
    }
    
    /**
     * Ca 1 - Shift 1 (6:00-14:00)
     * First shift in 3-shift rotation
     */
    fun getShift1(): Shift {
        return Shift.createNew(
            name = "Ca 1",
            startTime = "06:00",
            officeEndTime = "14:00",
            endTime = "14:30",
            departureTime = "05:30",
            daysApplied = listOf("Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"),
            remindBeforeStart = 45,
            remindAfterEnd = 15,
            showPunch = true,
            breakMinutes = 30,
            lateThresholdMinutes = 5,
            earlyThresholdMinutes = 5,
            penaltyRoundingMinutes = 15
        )
    }
    
    /**
     * Ca 2 - Shift 2 (14:00-22:00)
     * Second shift in 3-shift rotation
     */
    fun getShift2(): Shift {
        return Shift.createNew(
            name = "Ca 2",
            startTime = "14:00",
            officeEndTime = "22:00",
            endTime = "22:30",
            departureTime = "13:30",
            daysApplied = listOf("Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"),
            remindBeforeStart = 30,
            remindAfterEnd = 15,
            showPunch = true,
            breakMinutes = 30,
            lateThresholdMinutes = 5,
            earlyThresholdMinutes = 5,
            penaltyRoundingMinutes = 15
        )
    }
    
    /**
     * Ca 3 - Shift 3 (22:00-06:00)
     * Third shift in 3-shift rotation (night shift)
     */
    fun getShift3(): Shift {
        return Shift.createNew(
            name = "Ca 3",
            startTime = "22:00",
            officeEndTime = "06:00",
            endTime = "06:30",
            departureTime = "21:30",
            daysApplied = listOf("Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"),
            remindBeforeStart = 60,
            remindAfterEnd = 30,
            showPunch = true,
            breakMinutes = 60,
            lateThresholdMinutes = 10,
            earlyThresholdMinutes = 10,
            penaltyRoundingMinutes = 30
        )
    }
    
    /**
     * Ca kíp - Rotating shift (12-hour shifts)
     * 12-hour rotating shift pattern
     */
    fun getRotatingShift(): Shift {
        return Shift.createNew(
            name = "Ca kíp",
            startTime = "07:00",
            officeEndTime = "19:00",
            endTime = "19:30",
            departureTime = "06:30",
            daysApplied = listOf("Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"),
            remindBeforeStart = 60,
            remindAfterEnd = 30,
            showPunch = true,
            breakMinutes = 90, // Longer break for 12-hour shift
            lateThresholdMinutes = 15,
            earlyThresholdMinutes = 15,
            penaltyRoundingMinutes = 30
        )
    }
    
    /**
     * Get shift template by name
     */
    fun getTemplateByName(name: String): Shift? {
        return getAllTemplates().find { it.name == name }
    }
    
    /**
     * Get template names for UI selection
     */
    fun getTemplateNames(): List<String> {
        return getAllTemplates().map { it.name }
    }
    
    /**
     * Check if a shift name is a predefined template
     */
    fun isTemplate(name: String): Boolean {
        return getTemplateNames().contains(name)
    }
}
