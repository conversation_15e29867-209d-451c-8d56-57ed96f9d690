{"logs": [{"outputFile": "com.workly.app-mergeDebugResources-77:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9209685f1972143d9ab1eeffcca4ab41\\transformed\\core-1.12.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3540,3642,3744,3844,3944,4051,4155,14004", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "3637,3739,3839,3939,4046,4150,4269,14100"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\631285464bc3699b5e0c057776a78762\\transformed\\ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,280,377,476,561,637,733,820,909,974,1039,1120,1203,1280,1364,1434", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "192,275,372,471,556,632,728,815,904,969,1034,1115,1198,1275,1359,1429,1549"}, "to": {"startLines": "48,49,82,83,85,90,91,140,141,142,143,145,146,149,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4574,4666,8385,8482,8661,9061,9137,13123,13210,13299,13364,13514,13595,13844,14206,14290,14360", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "4661,4744,8477,8576,8741,9132,9228,13205,13294,13359,13424,13590,13673,13916,14285,14355,14475"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\85666ebb484fc6a37ee9760c5f520352\\transformed\\appcompat-1.2.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,521,629,749,827,903,995,1089,1184,1278,1378,1472,1568,1663,1755,1847,1929,2040,2143,2242,2357,2471,2574,2729,2832", "endColumns": "117,104,106,85,107,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,516,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827,2910"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,392,497,604,690,798,918,996,1072,1164,1258,1353,1447,1547,1641,1737,1832,1924,2016,2098,2209,2312,2411,2526,2640,2743,2898,13761", "endColumns": "117,104,106,85,107,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "387,492,599,685,793,913,991,1067,1159,1253,1348,1442,1542,1636,1732,1827,1919,2011,2093,2204,2307,2406,2521,2635,2738,2893,2996,13839"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b5d7701da16b09423affca14e07f926c\\transformed\\material-1.4.0-beta01\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,301,398,520,601,665,758,837,900,993,1059,1117,1190,1254,1310,1432,1489,1551,1607,1683,1817,1902,1988,2096,2177,2256,2346,2413,2479,2557,2640,2728,2803,2882,2955,3026,3120,3198,3287,3377,3451,3532,3619,3672,3739,3820,3904,3966,4030,4093,4201,4302,4404,4507", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,96,121,80,63,92,78,62,92,65,57,72,63,55,121,56,61,55,75,133,84,85,107,80,78,89,66,65,77,82,87,74,78,72,70,93,77,88,89,73,80,86,52,66,80,83,61,63,62,107,100,101,102,84", "endOffsets": "219,296,393,515,596,660,753,832,895,988,1054,1112,1185,1249,1305,1427,1484,1546,1602,1678,1812,1897,1983,2091,2172,2251,2341,2408,2474,2552,2635,2723,2798,2877,2950,3021,3115,3193,3282,3372,3446,3527,3614,3667,3734,3815,3899,3961,4025,4088,4196,4297,4399,4502,4587"}, "to": {"startLines": "2,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3463,4274,4371,4493,8746,8889,8982,9233,9296,9389,9455,9513,9586,9650,9706,9828,9885,9947,10003,10079,10213,10298,10384,10492,10573,10652,10742,10809,10875,10953,11036,11124,11199,11278,11351,11422,11516,11594,11683,11773,11847,11928,12015,12068,12135,12216,12300,12362,12426,12489,12597,12698,12800,13429", "endLines": "5,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "endColumns": "12,76,96,121,80,63,92,78,62,92,65,57,72,63,55,121,56,61,55,75,133,84,85,107,80,78,89,66,65,77,82,87,74,78,72,70,93,77,88,89,73,80,86,52,66,80,83,61,63,62,107,100,101,102,84", "endOffsets": "269,3535,4366,4488,4569,8805,8977,9056,9291,9384,9450,9508,9581,9645,9701,9823,9880,9942,9998,10074,10208,10293,10379,10487,10568,10647,10737,10804,10870,10948,11031,11119,11194,11273,11346,11417,11511,11589,11678,11768,11842,11923,12010,12063,12130,12211,12295,12357,12421,12484,12592,12693,12795,12898,13509"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d56bc5ab2f06c8c057783f978bdff8f7\\transformed\\material3-1.1.2\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,286,403,517,599,693,805,953,1070,1208,1289,1384,1476,1575,1689,1806,1906,2033,2157,2289,2463,2590,2706,2824,2956,3049,3145,3261,3386,3482,3585,3684,3816,3952,4054,4153,4233,4312,4395,4478,4579,4655,4734,4829,4929,5020,5115,5199,5305,5402,5502,5618,5694,5791", "endColumns": "117,112,116,113,81,93,111,147,116,137,80,94,91,98,113,116,99,126,123,131,173,126,115,117,131,92,95,115,124,95,102,98,131,135,101,98,79,78,82,82,100,75,78,94,99,90,94,83,105,96,99,115,75,96,90", "endOffsets": "168,281,398,512,594,688,800,948,1065,1203,1284,1379,1471,1570,1684,1801,1901,2028,2152,2284,2458,2585,2701,2819,2951,3044,3140,3256,3381,3477,3580,3679,3811,3947,4049,4148,4228,4307,4390,4473,4574,4650,4729,4824,4924,5015,5110,5194,5300,5397,5497,5613,5689,5786,5877"}, "to": {"startLines": "33,34,35,36,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,84,87,147,150,152,156,157,158,159,160,161,162,163,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3001,3119,3232,3349,4749,4831,4925,5037,5185,5302,5440,5521,5616,5708,5807,5921,6038,6138,6265,6389,6521,6695,6822,6938,7056,7188,7281,7377,7493,7618,7714,7817,7916,8048,8184,8286,8581,8810,13678,13921,14105,14480,14556,14635,14730,14830,14921,15016,15100,15206,15303,15403,15519,15595,15692", "endColumns": "117,112,116,113,81,93,111,147,116,137,80,94,91,98,113,116,99,126,123,131,173,126,115,117,131,92,95,115,124,95,102,98,131,135,101,98,79,78,82,82,100,75,78,94,99,90,94,83,105,96,99,115,75,96,90", "endOffsets": "3114,3227,3344,3458,4826,4920,5032,5180,5297,5435,5516,5611,5703,5802,5916,6033,6133,6260,6384,6516,6690,6817,6933,7051,7183,7276,7372,7488,7613,7709,7812,7911,8043,8179,8281,8380,8656,8884,13756,13999,14201,14551,14630,14725,14825,14916,15011,15095,15201,15298,15398,15514,15590,15687,15778"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\870103ce12d4503c553c2f969e6a0d11\\transformed\\navigation-ui-2.7.5\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,108", "endOffsets": "161,270"}, "to": {"startLines": "138,139", "startColumns": "4,4", "startOffsets": "12903,13014", "endColumns": "110,108", "endOffsets": "13009,13118"}}]}]}