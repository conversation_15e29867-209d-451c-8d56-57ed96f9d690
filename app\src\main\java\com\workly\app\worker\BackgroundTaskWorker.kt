package com.workly.app.worker

import android.content.Context
import androidx.work.*
import com.workly.app.WorklyApplication
import com.workly.app.business.AutoResetManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit

class BackgroundTaskWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    companion object {
        const val WORK_NAME = "workly_background_tasks"
        const val TASK_TYPE_KEY = "task_type"
        
        const val TASK_AUTO_RESET_CHECK = "auto_reset_check"
        const val TASK_WEATHER_CHECK = "weather_check"
        const val TASK_SHIFT_REMINDER = "shift_reminder"
        
        fun schedulePeriodicTasks(context: Context) {
            val workManager = WorkManager.getInstance(context)
            
            // Schedule auto-reset check (runs every hour)
            val autoResetRequest = PeriodicWorkRequestBuilder<BackgroundTaskWorker>(1, TimeUnit.HOURS)
                .setInputData(workDataOf(TASK_TYPE_KEY to TASK_AUTO_RESET_CHECK))
                .setConstraints(
                    Constraints.Builder()
                        .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                        .setRequiresBatteryNotLow(false)
                        .build()
                )
                .build()
            
            workManager.enqueueUniquePeriodicWork(
                "${WORK_NAME}_auto_reset",
                ExistingPeriodicWorkPolicy.KEEP,
                autoResetRequest
            )
            
            // Schedule weather check (runs every 6 hours)
            val weatherRequest = PeriodicWorkRequestBuilder<BackgroundTaskWorker>(6, TimeUnit.HOURS)
                .setInputData(workDataOf(TASK_TYPE_KEY to TASK_WEATHER_CHECK))
                .setConstraints(
                    Constraints.Builder()
                        .setRequiredNetworkType(NetworkType.CONNECTED)
                        .setRequiresBatteryNotLow(true)
                        .build()
                )
                .build()
            
            workManager.enqueueUniquePeriodicWork(
                "${WORK_NAME}_weather",
                ExistingPeriodicWorkPolicy.KEEP,
                weatherRequest
            )
            
            // Schedule shift reminder check (runs daily)
            val shiftReminderRequest = PeriodicWorkRequestBuilder<BackgroundTaskWorker>(1, TimeUnit.DAYS)
                .setInputData(workDataOf(TASK_TYPE_KEY to TASK_SHIFT_REMINDER))
                .setConstraints(
                    Constraints.Builder()
                        .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                        .setRequiresBatteryNotLow(false)
                        .build()
                )
                .build()
            
            workManager.enqueueUniquePeriodicWork(
                "${WORK_NAME}_shift_reminder",
                ExistingPeriodicWorkPolicy.KEEP,
                shiftReminderRequest
            )
        }
        
        fun cancelAllTasks(context: Context) {
            val workManager = WorkManager.getInstance(context)
            workManager.cancelUniqueWork("${WORK_NAME}_auto_reset")
            workManager.cancelUniqueWork("${WORK_NAME}_weather")
            workManager.cancelUniqueWork("${WORK_NAME}_shift_reminder")
        }
    }
    
    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        try {
            val application = applicationContext as WorklyApplication
            val taskType = inputData.getString(TASK_TYPE_KEY)
            
            when (taskType) {
                TASK_AUTO_RESET_CHECK -> handleAutoResetCheck(application)
                TASK_WEATHER_CHECK -> handleWeatherCheck(application)
                TASK_SHIFT_REMINDER -> handleShiftReminder(application)
                else -> Result.failure()
            }
        } catch (e: Exception) {
            Result.retry()
        }
    }
    
    private suspend fun handleAutoResetCheck(application: WorklyApplication): Result {
        return try {
            val autoResetManager = AutoResetManager(
                application.attendanceRepository,
                application.shiftRepository
            )
            
            val resetResult = autoResetManager.checkAutoReset()
            if (resetResult.shouldReset) {
                val success = autoResetManager.performAutoReset()
                if (success) {
                    // Show notification about auto reset
                    application.notificationManager.showAutoResetNotification()
                    
                    // Reschedule alarms for the new day
                    application.alarmManager.scheduleActiveShiftAlarms()
                }
            }
            
            Result.success()
        } catch (e: Exception) {
            Result.retry()
        }
    }
    
    private suspend fun handleWeatherCheck(application: WorklyApplication): Result {
        return try {
            // TODO: Implement weather checking logic
            // This would:
            // 1. Get user's location from settings
            // 2. Fetch weather data from API
            // 3. Check for extreme weather conditions
            // 4. Show warning notification if needed
            
            val settings = application.settingsRepository.getSettings()
            if (!settings.weatherWarningEnabled) {
                return Result.success()
            }
            
            // Placeholder for weather check
            // In real implementation, you would:
            // - Call weather API
            // - Analyze weather conditions
            // - Show notifications for extreme weather
            
            Result.success()
        } catch (e: Exception) {
            Result.retry()
        }
    }
    
    private suspend fun handleShiftReminder(application: WorklyApplication): Result {
        return try {
            val settings = application.settingsRepository.getSettings()
            
            // Check if shift reminder is enabled
            if (settings.changeShiftReminderMode == "disabled") {
                return Result.success()
            }
            
            val activeShift = application.shiftRepository.getActiveShift()
            if (activeShift != null) {
                // Check if it's time to remind about shift change
                val today = kotlinx.datetime.Clock.System.todayIn(kotlinx.datetime.TimeZone.currentSystemDefault())
                val dayOfWeek = today.dayOfWeek
                
                // Show reminder on Friday for weekly shift changes
                if (dayOfWeek == kotlinx.datetime.DayOfWeek.FRIDAY) {
                    val nextWeekStart = today.plus(3, kotlinx.datetime.DateTimeUnit.DAY).toString()
                    application.notificationManager.showShiftChangeReminder(
                        activeShift.name,
                        nextWeekStart
                    )
                }
            }
            
            Result.success()
        } catch (e: Exception) {
            Result.retry()
        }
    }
}

/**
 * Extension function to schedule one-time weather check
 */
fun scheduleWeatherCheck(context: Context, delayMinutes: Long = 60) {
    val workManager = WorkManager.getInstance(context)
    
    val weatherRequest = OneTimeWorkRequestBuilder<BackgroundTaskWorker>()
        .setInputData(workDataOf(BackgroundTaskWorker.TASK_TYPE_KEY to BackgroundTaskWorker.TASK_WEATHER_CHECK))
        .setInitialDelay(delayMinutes, TimeUnit.MINUTES)
        .setConstraints(
            Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build()
        )
        .build()
    
    workManager.enqueueUniqueWork(
        "weather_check_one_time",
        ExistingWorkPolicy.REPLACE,
        weatherRequest
    )
}
