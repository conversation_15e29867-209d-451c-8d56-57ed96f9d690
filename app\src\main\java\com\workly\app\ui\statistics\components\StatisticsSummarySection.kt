package com.workly.app.ui.statistics.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.workly.app.ui.viewmodel.StatisticsData

@Composable
fun StatisticsSummarySection(data: StatisticsData) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = "Tổng quan",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Total Hours Card
            SummaryCard(
                icon = Icons.Default.Schedule,
                title = "Tổng giờ",
                value = "${String.format("%.1f", data.totalHours)}h",
                subtitle = "${data.totalDays} ngày",
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.weight(1f)
            )
            
            // Average Hours Card
            SummaryCard(
                icon = Icons.Default.TrendingUp,
                title = "TB/ngày",
                value = "${String.format("%.1f", data.averageHoursPerDay)}h",
                subtitle = "Trung bình",
                color = MaterialTheme.colorScheme.secondary,
                modifier = Modifier.weight(1f)
            )
        }
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Overtime Hours Card
            SummaryCard(
                icon = Icons.Default.AccessTime,
                title = "Tăng ca",
                value = "${String.format("%.1f", data.totalOtHours)}h",
                subtitle = "Overtime",
                color = Color(0xFFFF9800), // Orange
                modifier = Modifier.weight(1f)
            )
            
            // Penalty Minutes Card
            SummaryCard(
                icon = Icons.Default.Warning,
                title = "Phạt",
                value = "${data.totalPenaltyMinutes}p",
                subtitle = "Phút phạt",
                color = MaterialTheme.colorScheme.error,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
private fun SummaryCard(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    value: String,
    subtitle: String,
    color: Color,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = color.copy(alpha = 0.1f)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(32.dp)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = value,
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                color = color
            )
            
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
