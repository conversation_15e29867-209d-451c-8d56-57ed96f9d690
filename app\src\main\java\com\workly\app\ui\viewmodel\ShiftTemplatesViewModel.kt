package com.workly.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.workly.app.data.repository.ShiftRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

class ShiftTemplatesViewModel(
    private val shiftRepository: ShiftRepository
) : ViewModel() {

    // State flows
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    private val _successMessage = MutableStateFlow<String?>(null)
    val successMessage: StateFlow<String?> = _successMessage.asStateFlow()

    // Available templates (templates not yet added)
    val availableTemplates = shiftRepository.getShiftsFlow()
        .map { existingShifts ->
            shiftRepository.getAvailableTemplates()
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    /**
     * Add a template to the shift list
     */
    fun addTemplate(templateName: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val success = shiftRepository.addShiftFromTemplate(templateName)
                
                if (success) {
                    _successMessage.value = "Đã thêm ca '$templateName'"
                } else {
                    _error.value = "Không thể thêm ca '$templateName'. Ca này có thể đã tồn tại."
                }
            } catch (e: Exception) {
                _error.value = "Lỗi khi thêm ca: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Add all available templates
     */
    fun addAllTemplates() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val templates = availableTemplates.value
                var addedCount = 0
                
                templates.forEach { templateName ->
                    val success = shiftRepository.addShiftFromTemplate(templateName)
                    if (success) {
                        addedCount++
                    }
                }
                
                if (addedCount > 0) {
                    _successMessage.value = "Đã thêm $addedCount ca làm việc"
                } else {
                    _error.value = "Không có ca nào được thêm"
                }
            } catch (e: Exception) {
                _error.value = "Lỗi khi thêm ca: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun clearError() {
        _error.value = null
    }

    fun clearSuccessMessage() {
        _successMessage.value = null
    }
}
