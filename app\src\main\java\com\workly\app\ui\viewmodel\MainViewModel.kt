package com.workly.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.workly.app.data.repository.AttendanceRepository
import com.workly.app.data.repository.NotesRepository
import com.workly.app.data.repository.SettingsRepository
import com.workly.app.data.repository.ShiftRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch

class MainViewModel(
    private val settingsRepository: SettingsRepository,
    private val shiftRepository: ShiftRepository,
    private val attendanceRepository: AttendanceRepository,
    private val notesRepository: NotesRepository
) : ViewModel() {
    
    private val _isLoading = MutableStateFlow(true)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    // Theme state
    val isDarkTheme = settingsRepository.getSettingsFlow().combine(
        MutableStateFlow(false) // System theme state - would need to be updated based on system
    ) { settings, systemIsDark ->
        when (settings.theme) {
            "dark" -> true
            "light" -> false
            "system" -> systemIsDark
            else -> systemIsDark
        }
    }
    
    // Current language
    val currentLanguage = settingsRepository.getSettingsFlow().combine(
        MutableStateFlow("vi")
    ) { settings, _ ->
        settings.language
    }
    
    init {
        initializeApp()
    }
    
    private fun initializeApp() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                // Initialize default settings if needed
                val settings = settingsRepository.getSettings()
                if (settings.createdAt.isEmpty()) {
                    settingsRepository.saveSettings(
                        settings.copy(
                            createdAt = kotlinx.datetime.Clock.System.now().toString(),
                            updatedAt = kotlinx.datetime.Clock.System.now().toString()
                        )
                    )
                }
                
                // Check for auto-reset if needed
                checkAutoReset()
                
                _isLoading.value = false
            } catch (e: Exception) {
                _error.value = "Failed to initialize app: ${e.message}"
                _isLoading.value = false
            }
        }
    }
    
    private suspend fun checkAutoReset() {
        try {
            val lastResetTime = attendanceRepository.getLastAutoResetTime()
            val now = kotlinx.datetime.Clock.System.now()
            val today = kotlinx.datetime.LocalDate.fromEpochDays(now.epochSeconds.toInt() / 86400)
            
            // Check if we need to auto-reset (6 hours before shift start)
            // This is a simplified version - full implementation would check actual shift times
            val shouldReset = lastResetTime == null || 
                !lastResetTime.startsWith(today.toString())
            
            if (shouldReset) {
                // Perform auto-reset logic here
                attendanceRepository.setLastAutoResetTime(now.toString())
            }
        } catch (e: Exception) {
            // Log error but don't fail initialization
        }
    }
    
    fun clearError() {
        _error.value = null
    }
    
    fun refreshData() {
        initializeApp()
    }
}
