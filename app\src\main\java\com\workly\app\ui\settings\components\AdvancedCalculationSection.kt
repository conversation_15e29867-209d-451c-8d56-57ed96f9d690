package com.workly.app.ui.settings.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Calculate
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.workly.app.R
import com.workly.app.data.model.UserSettings

@Composable
fun AdvancedCalculationSection(
    settings: UserSettings,
    onAdvancedCalculationChange: (Int, Float, Float, Float, Float) -> Unit
) {
    var lateThreshold by remember { mutableStateOf(settings.lateThresholdMinutes.toString()) }
    var otWeekdayRate by remember { mutableStateOf(settings.otWeekdayRate.toString()) }
    var otSaturdayRate by remember { mutableStateOf(settings.otSaturdayRate.toString()) }
    var otSundayRate by remember { mutableStateOf(settings.otSundayRate.toString()) }
    var otHolidayRate by remember { mutableStateOf(settings.otHolidayRate.toString()) }
    
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Calculate,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = stringResource(R.string.advanced_calculation),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
            }
            
            // Late threshold
            OutlinedTextField(
                value = lateThreshold,
                onValueChange = { 
                    lateThreshold = it
                    val threshold = it.toIntOrNull() ?: settings.lateThresholdMinutes
                    onAdvancedCalculationChange(
                        threshold,
                        settings.otWeekdayRate,
                        settings.otSaturdayRate,
                        settings.otSundayRate,
                        settings.otHolidayRate
                    )
                },
                label = { Text(stringResource(R.string.late_threshold_minutes)) },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                suffix = { Text("phút") }
            )
            
            // OT Rates
            Text(
                text = "Tỷ lệ làm thêm giờ",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            
            OutlinedTextField(
                value = otWeekdayRate,
                onValueChange = { 
                    otWeekdayRate = it
                    val rate = it.toFloatOrNull() ?: settings.otWeekdayRate
                    onAdvancedCalculationChange(
                        settings.lateThresholdMinutes,
                        rate,
                        settings.otSaturdayRate,
                        settings.otSundayRate,
                        settings.otHolidayRate
                    )
                },
                label = { Text(stringResource(R.string.ot_weekday_rate)) },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                suffix = { Text("%") }
            )
            
            OutlinedTextField(
                value = otSaturdayRate,
                onValueChange = { 
                    otSaturdayRate = it
                    val rate = it.toFloatOrNull() ?: settings.otSaturdayRate
                    onAdvancedCalculationChange(
                        settings.lateThresholdMinutes,
                        settings.otWeekdayRate,
                        rate,
                        settings.otSundayRate,
                        settings.otHolidayRate
                    )
                },
                label = { Text(stringResource(R.string.ot_saturday_rate)) },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                suffix = { Text("%") }
            )
            
            OutlinedTextField(
                value = otSundayRate,
                onValueChange = { 
                    otSundayRate = it
                    val rate = it.toFloatOrNull() ?: settings.otSundayRate
                    onAdvancedCalculationChange(
                        settings.lateThresholdMinutes,
                        settings.otWeekdayRate,
                        settings.otSaturdayRate,
                        rate,
                        settings.otHolidayRate
                    )
                },
                label = { Text(stringResource(R.string.ot_sunday_rate)) },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                suffix = { Text("%") }
            )
            
            OutlinedTextField(
                value = otHolidayRate,
                onValueChange = { 
                    otHolidayRate = it
                    val rate = it.toFloatOrNull() ?: settings.otHolidayRate
                    onAdvancedCalculationChange(
                        settings.lateThresholdMinutes,
                        settings.otWeekdayRate,
                        settings.otSaturdayRate,
                        settings.otSundayRate,
                        rate
                    )
                },
                label = { Text(stringResource(R.string.ot_holiday_rate)) },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                suffix = { Text("%") }
            )
            
            // Info card
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
                )
            ) {
                Row(
                    modifier = Modifier.padding(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "💡",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Column {
                        Text(
                            text = "Mẹo:",
                            style = MaterialTheme.typography.labelMedium,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onSecondaryContainer
                        )
                        Text(
                            text = "Tỷ lệ OT thông thường: Ngày thường 150%, Thứ 7 200%, Chủ nhật 300%, Ngày lễ 400%",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSecondaryContainer
                        )
                    }
                }
            }
        }
    }
}
