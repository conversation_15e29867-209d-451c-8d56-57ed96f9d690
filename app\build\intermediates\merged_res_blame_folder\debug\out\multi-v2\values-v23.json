{"logs": [{"outputFile": "com.workly.app-mergeDebugResources-77:/values-v23/values-v23.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b5d7701da16b09423affca14e07f926c\\transformed\\material-1.4.0-beta01\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,6", "startColumns": "4,4", "startOffsets": "55,320", "endLines": "5,9", "endColumns": "10,10", "endOffsets": "315,588"}, "to": {"startLines": "55,59", "startColumns": "4,4", "startOffsets": "3511,3776", "endLines": "58,62", "endColumns": "10,10", "endOffsets": "3771,4044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\55edbade2287c9acfa01bffd0cb4cd15\\transformed\\cardview-1.0.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "12", "endOffsets": "200"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "3361", "endLines": "54", "endColumns": "12", "endOffsets": "3506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68376da0eeb9b4f595296a0b33c6e15b\\transformed\\work-runtime-2.8.1\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,121", "endColumns": "65,62", "endOffsets": "116,179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\85666ebb484fc6a37ee9760c5f520352\\transformed\\appcompat-1.2.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3,4,5,6,20,34,35,36,39,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1371,2267,2386,2513,2735,2959,3074,3181,3294", "endLines": "2,3,4,5,19,33,34,35,38,42,43,44,45,49", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "185,320,395,482,1366,2262,2381,2508,2730,2954,3069,3176,3289,3519"}, "to": {"startLines": "4,5,6,7,8,22,36,37,38,41,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "184,319,454,529,616,1354,2104,2223,2350,2572,2796,2911,3018,3131", "endLines": "4,5,6,7,21,35,36,37,40,44,45,46,47,51", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "314,449,524,611,1349,2099,2218,2345,2567,2791,2906,3013,3126,3356"}}]}]}