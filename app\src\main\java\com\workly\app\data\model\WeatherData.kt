package com.workly.app.data.model

import kotlinx.serialization.Serializable

@Serializable
data class WeatherData(
    val location: String,
    val temperature: Double,
    val feelsLike: Double,
    val condition: String,
    val humidity: Int,
    val windSpeed: Double,
    val pressure: Double,
    val visibility: Double,
    val uvIndex: Int,
    val warnings: List<String> = emptyList(),
    val forecast: List<ForecastItem>? = null,
    val lastUpdated: String
) {
    @Serializable
    data class ForecastItem(
        val day: String,
        val condition: String,
        val maxTemp: Int,
        val minTemp: Int,
        val chanceOfRain: Int = 0
    )
    
    /**
     * Check if there are any weather warnings
     */
    fun hasWarnings(): Boolean = warnings.isNotEmpty()
    
    /**
     * Check if it's likely to rain
     */
    fun isRainy(): Boolean {
        val rainyConditions = listOf("rain", "shower", "storm", "drizzle")
        return rainyConditions.any { condition.lowercase().contains(it) }
    }
    
    /**
     * Check if weather is severe
     */
    fun isSevereWeather(): Boolean {
        return hasWarnings() || 
               windSpeed > 50 || 
               temperature < 0 || 
               temperature > 40 ||
               condition.lowercase().contains("storm")
    }
    
    /**
     * Get weather emoji
     */
    fun getWeatherEmoji(): String {
        return when {
            condition.lowercase().contains("sunny") || condition.lowercase().contains("clear") -> "☀️"
            condition.lowercase().contains("cloud") -> "☁️"
            condition.lowercase().contains("rain") -> "🌧️"
            condition.lowercase().contains("storm") -> "⛈️"
            condition.lowercase().contains("snow") -> "❄️"
            condition.lowercase().contains("fog") -> "🌫️"
            else -> "🌤️"
        }
    }
    
    /**
     * Get temperature color based on value
     */
    fun getTemperatureLevel(): TemperatureLevel {
        return when {
            temperature < 10 -> TemperatureLevel.COLD
            temperature < 25 -> TemperatureLevel.COOL
            temperature < 30 -> TemperatureLevel.WARM
            else -> TemperatureLevel.HOT
        }
    }
    
    enum class TemperatureLevel {
        COLD, COOL, WARM, HOT
    }
    
    companion object {
        /**
         * Create sample weather data for testing
         */
        fun createSample(location: String = "Hà Nội"): WeatherData {
            return WeatherData(
                location = location,
                temperature = 25.0,
                feelsLike = 27.0,
                condition = "Partly Cloudy",
                humidity = 65,
                windSpeed = 15.0,
                pressure = 1013.0,
                visibility = 10.0,
                uvIndex = 6,
                warnings = emptyList(),
                forecast = listOf(
                    ForecastItem("Hôm nay", "Partly Cloudy", 28, 22, 20),
                    ForecastItem("Ngày mai", "Sunny", 30, 24, 10),
                    ForecastItem("Thứ 3", "Rain", 26, 20, 80),
                    ForecastItem("Thứ 4", "Cloudy", 24, 19, 30),
                    ForecastItem("Thứ 5", "Sunny", 29, 23, 5)
                ),
                lastUpdated = kotlinx.datetime.Clock.System.now().toString()
            )
        }
        
        /**
         * Create weather data with warnings
         */
        fun createWithWarnings(location: String = "TP.HCM"): WeatherData {
            return WeatherData(
                location = location,
                temperature = 35.0,
                feelsLike = 42.0,
                condition = "Hot and Humid",
                humidity = 85,
                windSpeed = 25.0,
                pressure = 1008.0,
                visibility = 8.0,
                uvIndex = 10,
                warnings = listOf(
                    "Cảnh báo nắng nóng gay gắt",
                    "Chỉ số UV rất cao",
                    "Khuyến cáo hạn chế ra ngoài từ 10h-16h"
                ),
                forecast = listOf(
                    ForecastItem("Hôm nay", "Very Hot", 36, 28, 10),
                    ForecastItem("Ngày mai", "Hot", 34, 27, 20),
                    ForecastItem("Thứ 3", "Thunderstorm", 30, 24, 90)
                ),
                lastUpdated = kotlinx.datetime.Clock.System.now().toString()
            )
        }
    }
}
