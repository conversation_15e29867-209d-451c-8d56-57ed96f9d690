package com.workly.app.ui.statistics.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.workly.app.data.model.Shift
import com.workly.app.ui.viewmodel.StatisticsPeriod
import kotlinx.datetime.LocalDate

@Composable
fun StatisticsFiltersSection(
    selectedPeriod: StatisticsPeriod,
    availableShifts: List<Shift>,
    selectedShiftId: String?,
    onPeriodChange: (StatisticsPeriod) -> Unit,
    onShiftChange: (String?) -> Unit,
    onCustomDateRangeChange: (LocalDate, LocalDate) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Bộ lọc",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            // Period Filter
            PeriodFilterSection(
                selectedPeriod = selectedPeriod,
                onPeriodChange = onPeriodChange,
                onCustomDateRangeChange = onCustomDateRangeChange
            )
            
            // Shift Filter
            if (availableShifts.isNotEmpty()) {
                Divider()
                ShiftFilterSection(
                    availableShifts = availableShifts,
                    selectedShiftId = selectedShiftId,
                    onShiftChange = onShiftChange
                )
            }
        }
    }
}

@Composable
private fun PeriodFilterSection(
    selectedPeriod: StatisticsPeriod,
    onPeriodChange: (StatisticsPeriod) -> Unit,
    onCustomDateRangeChange: (LocalDate, LocalDate) -> Unit
) {
    Column {
        Text(
            text = "Khoảng thời gian",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // Period chips
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            PeriodChip(
                text = "Tuần này",
                selected = selectedPeriod == StatisticsPeriod.THIS_WEEK,
                onClick = { onPeriodChange(StatisticsPeriod.THIS_WEEK) }
            )
            
            PeriodChip(
                text = "Tháng này",
                selected = selectedPeriod == StatisticsPeriod.THIS_MONTH,
                onClick = { onPeriodChange(StatisticsPeriod.THIS_MONTH) }
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            PeriodChip(
                text = "Tháng trước",
                selected = selectedPeriod == StatisticsPeriod.LAST_MONTH,
                onClick = { onPeriodChange(StatisticsPeriod.LAST_MONTH) }
            )
            
            PeriodChip(
                text = "Năm này",
                selected = selectedPeriod == StatisticsPeriod.THIS_YEAR,
                onClick = { onPeriodChange(StatisticsPeriod.THIS_YEAR) }
            )
            
            PeriodChip(
                text = "Tùy chọn",
                selected = selectedPeriod == StatisticsPeriod.CUSTOM,
                onClick = { 
                    // TODO: Open date range picker
                    onPeriodChange(StatisticsPeriod.CUSTOM)
                }
            )
        }
    }
}

@Composable
private fun PeriodChip(
    text: String,
    selected: Boolean,
    onClick: () -> Unit
) {
    FilterChip(
        selected = selected,
        onClick = onClick,
        label = { Text(text) }
    )
}

@Composable
private fun ShiftFilterSection(
    availableShifts: List<Shift>,
    selectedShiftId: String?,
    onShiftChange: (String?) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }
    
    Column {
        Text(
            text = "Ca làm việc",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        ExposedDropdownMenuBox(
            expanded = expanded,
            onExpandedChange = { expanded = !expanded }
        ) {
            OutlinedTextField(
                value = getShiftDisplayName(availableShifts, selectedShiftId),
                onValueChange = { },
                readOnly = true,
                modifier = Modifier
                    .fillMaxWidth()
                    .menuAnchor(),
                label = { Text("Chọn ca làm việc") },
                trailingIcon = {
                    ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
                }
            )
            
            ExposedDropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                // All shifts option
                DropdownMenuItem(
                    text = { Text("Tất cả ca") },
                    onClick = {
                        onShiftChange(null)
                        expanded = false
                    },
                    leadingIcon = if (selectedShiftId == null) {
                        { Icon(Icons.Default.Check, contentDescription = null) }
                    } else null
                )
                
                // Individual shifts
                availableShifts.forEach { shift ->
                    DropdownMenuItem(
                        text = { Text(shift.name) },
                        onClick = {
                            onShiftChange(shift.id)
                            expanded = false
                        },
                        leadingIcon = if (selectedShiftId == shift.id) {
                            { Icon(Icons.Default.Check, contentDescription = null) }
                        } else null
                    )
                }
            }
        }
    }
}

private fun getShiftDisplayName(shifts: List<Shift>, selectedShiftId: String?): String {
    return if (selectedShiftId == null) {
        "Tất cả ca"
    } else {
        shifts.find { it.id == selectedShiftId }?.name ?: "Không xác định"
    }
}
