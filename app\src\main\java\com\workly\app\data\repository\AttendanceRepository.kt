package com.workly.app.data.repository

import com.google.gson.reflect.TypeToken
import com.workly.app.data.model.AttendanceLog
import com.workly.app.data.model.AttendanceType
import com.workly.app.data.model.DailyAttendanceLogs
import com.workly.app.data.model.DailyWorkStatus
import com.workly.app.data.storage.DataStoreManager
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.Clock
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.todayIn

class AttendanceRepository(private val dataStoreManager: DataStoreManager) {
    
    private val attendanceLogsType = object : TypeToken<Map<String, List<AttendanceLog>>>() {}
    private val dailyWorkStatusType = object : TypeToken<Map<String, DailyWorkStatus>>() {}
    
    // Attendance Logs Management
    suspend fun saveAttendanceLogs(logs: Map<String, List<AttendanceLog>>) {
        dataStoreManager.storeData(DataStoreManager.ATTENDANCE_LOGS_KEY, logs)
    }
    
    suspend fun getAttendanceLogs(): Map<String, List<AttendanceLog>> {
        return dataStoreManager.getData(
            DataStoreManager.ATTENDANCE_LOGS_KEY,
            attendanceLogsType,
            emptyMap()
        )
    }
    
    fun getAttendanceLogsFlow(): Flow<Map<String, List<AttendanceLog>>> {
        return dataStoreManager.getDataFlow(
            DataStoreManager.ATTENDANCE_LOGS_KEY,
            attendanceLogsType,
            emptyMap()
        )
    }
    
    suspend fun addAttendanceLog(date: String, log: AttendanceLog) {
        val currentLogs = getAttendanceLogs().toMutableMap()
        val dailyLogs = currentLogs[date]?.toMutableList() ?: mutableListOf()
        dailyLogs.add(log)
        currentLogs[date] = dailyLogs
        saveAttendanceLogs(currentLogs)
    }
    
    suspend fun getLogsForDate(date: String): DailyAttendanceLogs {
        val allLogs = getAttendanceLogs()
        return DailyAttendanceLogs(date, allLogs[date] ?: emptyList())
    }
    
    suspend fun clearLogsForDate(date: String) {
        val currentLogs = getAttendanceLogs().toMutableMap()
        currentLogs.remove(date)
        saveAttendanceLogs(currentLogs)
    }
    
    suspend fun getTodayLogs(): DailyAttendanceLogs {
        val today = Clock.System.todayIn(TimeZone.currentSystemDefault()).toString()
        return getLogsForDate(today)
    }
    
    // Daily Work Status Management
    suspend fun saveDailyWorkStatuses(statuses: Map<String, DailyWorkStatus>) {
        dataStoreManager.storeData(DataStoreManager.DAILY_WORK_STATUS_KEY, statuses)
    }
    
    suspend fun getDailyWorkStatuses(): Map<String, DailyWorkStatus> {
        return dataStoreManager.getData(
            DataStoreManager.DAILY_WORK_STATUS_KEY,
            dailyWorkStatusType,
            emptyMap()
        )
    }
    
    fun getDailyWorkStatusesFlow(): Flow<Map<String, DailyWorkStatus>> {
        return dataStoreManager.getDataFlow(
            DataStoreManager.DAILY_WORK_STATUS_KEY,
            dailyWorkStatusType,
            emptyMap()
        )
    }
    
    suspend fun saveDailyWorkStatus(status: DailyWorkStatus) {
        val currentStatuses = getDailyWorkStatuses().toMutableMap()
        currentStatuses[status.date] = status.copy(
            updatedAt = Clock.System.now().toString()
        )
        saveDailyWorkStatuses(currentStatuses)
    }
    
    suspend fun getWorkStatusForDate(date: String): DailyWorkStatus? {
        return getDailyWorkStatuses()[date]
    }
    
    suspend fun clearWorkStatusForDate(date: String) {
        val currentStatuses = getDailyWorkStatuses().toMutableMap()
        currentStatuses.remove(date)
        saveDailyWorkStatuses(currentStatuses)
    }
    
    suspend fun getTodayWorkStatus(): DailyWorkStatus? {
        val today = Clock.System.todayIn(TimeZone.currentSystemDefault()).toString()
        return getWorkStatusForDate(today)
    }
    
    // Utility methods
    suspend fun getWorkStatusesForWeek(startDate: LocalDate): List<DailyWorkStatus?> {
        val statuses = getDailyWorkStatuses()
        return (0..6).map { dayOffset ->
            val date = startDate.plusDays(dayOffset).toString()
            statuses[date]
        }
    }
    
    suspend fun getWorkStatusesForMonth(year: Int, month: Int): Map<String, DailyWorkStatus> {
        val allStatuses = getDailyWorkStatuses()
        val monthPrefix = String.format("%04d-%02d", year, month)
        return allStatuses.filterKeys { it.startsWith(monthPrefix) }
    }
    
    suspend fun getWorkStatusesForDateRange(startDate: String, endDate: String): Map<String, DailyWorkStatus> {
        val allStatuses = getDailyWorkStatuses()
        return allStatuses.filterKeys { date ->
            date >= startDate && date <= endDate
        }
    }
    
    // Reset functionality
    suspend fun resetTodayData() {
        val today = Clock.System.todayIn(TimeZone.currentSystemDefault()).toString()
        clearLogsForDate(today)
        clearWorkStatusForDate(today)
    }
    
    // Last auto reset time
    suspend fun setLastAutoResetTime(time: String) {
        dataStoreManager.storeString(DataStoreManager.LAST_AUTO_RESET_TIME_KEY, time)
    }
    
    suspend fun getLastAutoResetTime(): String? {
        val time = dataStoreManager.getString(DataStoreManager.LAST_AUTO_RESET_TIME_KEY)
        return if (time.isEmpty()) null else time
    }
}
