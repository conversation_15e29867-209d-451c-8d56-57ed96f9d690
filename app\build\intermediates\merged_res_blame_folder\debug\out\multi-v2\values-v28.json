{"logs": [{"outputFile": "com.workly.app-mergeDebugResources-77:/values-v28/values-v28.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\85666ebb484fc6a37ee9760c5f520352\\transformed\\appcompat-1.2.0\\res\\values-v28\\values-v28.xml", "from": {"startLines": "2,3,4,8", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,447", "endLines": "2,3,7,11", "endColumns": "74,86,12,12", "endOffsets": "125,212,442,684"}, "to": {"startLines": "10,11,12,16", "startColumns": "4,4,4,4", "startOffsets": "755,830,917,1097", "endLines": "10,11,15,19", "endColumns": "74,86,12,12", "endOffsets": "825,912,1092,1284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b5d7701da16b09423affca14e07f926c\\transformed\\material-1.4.0-beta01\\res\\values-v28\\values-v28.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,143,231,319,407,494,581,668", "endColumns": "87,87,87,87,86,86,86,86", "endOffsets": "138,226,314,402,489,576,663,750"}}]}]}