[{"merged": "com.workly.app-merged_res-79:/xml_backup_rules.xml.flat", "source": "com.workly.app-main-81:/xml/backup_rules.xml"}, {"merged": "com.workly.app-merged_res-79:/drawable_ic_launcher_foreground.xml.flat", "source": "com.workly.app-main-81:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.workly.app-merged_res-79:/drawable_ic_launcher_background.xml.flat", "source": "com.workly.app-main-81:/drawable/ic_launcher_background.xml"}, {"merged": "com.workly.app-merged_res-79:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.workly.app-main-81:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.workly.app-merged_res-79:/xml_data_extraction_rules.xml.flat", "source": "com.workly.app-main-81:/xml/data_extraction_rules.xml"}, {"merged": "com.workly.app-merged_res-79:/mipmap-hdpi_ic_launcher_round.png.flat", "source": "com.workly.app-main-81:/mipmap-hdpi/ic_launcher_round.png"}, {"merged": "com.workly.app-merged_res-79:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.workly.app-main-81:/mipmap-hdpi/ic_launcher.png"}, {"merged": "com.workly.app-merged_res-79:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.workly.app-main-81:/mipmap-anydpi-v26/ic_launcher_round.xml"}]