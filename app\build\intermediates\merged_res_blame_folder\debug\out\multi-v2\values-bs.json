{"logs": [{"outputFile": "com.workly.app-mergeDebugResources-77:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\631285464bc3699b5e0c057776a78762\\transformed\\ui-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,206,294,388,487,573,650,742,834,919,991,1062,1143,1229,1302,1382,1452", "endColumns": "100,87,93,98,85,76,91,91,84,71,70,80,85,72,79,69,117", "endOffsets": "201,289,383,482,568,645,737,829,914,986,1057,1138,1224,1297,1377,1447,1565"}, "to": {"startLines": "49,50,83,84,86,91,92,141,142,143,144,146,147,150,154,155,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4631,4732,8453,8547,8727,9125,9202,13198,13290,13375,13447,13598,13679,13942,14308,14388,14458", "endColumns": "100,87,93,98,85,76,91,91,84,71,70,80,85,72,79,69,117", "endOffsets": "4727,4815,8542,8641,8808,9197,9289,13285,13370,13442,13513,13674,13760,14010,14383,14453,14571"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\85666ebb484fc6a37ee9760c5f520352\\transformed\\appcompat-1.2.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2243,2348,2462,2565,2734,2830", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2238,2343,2457,2560,2729,2825,2912"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "332,453,550,657,743,847,969,1054,1136,1227,1320,1415,1509,1609,1702,1797,1892,1983,2074,2162,2265,2369,2470,2575,2689,2792,2961,13855", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "448,545,652,738,842,964,1049,1131,1222,1315,1410,1504,1604,1697,1792,1887,1978,2069,2157,2260,2364,2465,2570,2684,2787,2956,3052,13937"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b5d7701da16b09423affca14e07f926c\\transformed\\material-1.4.0-beta01\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,282,364,460,586,667,733,825,902,965,1073,1139,1195,1266,1326,1380,1499,1556,1618,1672,1747,1871,1959,2042,2157,2242,2328,2416,2483,2549,2623,2701,2788,2860,2937,3010,3080,3173,3245,3337,3433,3507,3583,3679,3732,3799,3886,3973,4035,4099,4162,4270,4372,4473,4578", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,81,95,125,80,65,91,76,62,107,65,55,70,59,53,118,56,61,53,74,123,87,82,114,84,85,87,66,65,73,77,86,71,76,72,69,92,71,91,95,73,75,95,52,66,86,86,61,63,62,107,101,100,104,79", "endOffsets": "277,359,455,581,662,728,820,897,960,1068,1134,1190,1261,1321,1375,1494,1551,1613,1667,1742,1866,1954,2037,2152,2237,2323,2411,2478,2544,2618,2696,2783,2855,2932,3005,3075,3168,3240,3332,3428,3502,3578,3674,3727,3794,3881,3968,4030,4094,4157,4265,4367,4468,4573,4653"}, "to": {"startLines": "2,38,46,47,48,87,89,90,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3521,4328,4424,4550,8813,8956,9048,9294,9357,9465,9531,9587,9658,9718,9772,9891,9948,10010,10064,10139,10263,10351,10434,10549,10634,10720,10808,10875,10941,11015,11093,11180,11252,11329,11402,11472,11565,11637,11729,11825,11899,11975,12071,12124,12191,12278,12365,12427,12491,12554,12662,12764,12865,13518", "endLines": "6,38,46,47,48,87,89,90,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,145", "endColumns": "12,81,95,125,80,65,91,76,62,107,65,55,70,59,53,118,56,61,53,74,123,87,82,114,84,85,87,66,65,73,77,86,71,76,72,69,92,71,91,95,73,75,95,52,66,86,86,61,63,62,107,101,100,104,79", "endOffsets": "327,3598,4419,4545,4626,8874,9043,9120,9352,9460,9526,9582,9653,9713,9767,9886,9943,10005,10059,10134,10258,10346,10429,10544,10629,10715,10803,10870,10936,11010,11088,11175,11247,11324,11397,11467,11560,11632,11724,11820,11894,11970,12066,12119,12186,12273,12360,12422,12486,12549,12657,12759,12860,12965,13593"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9209685f1972143d9ab1eeffcca4ab41\\transformed\\core-1.12.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "39,40,41,42,43,44,45,152", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3603,3701,3803,3901,4005,4109,4211,14102", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "3696,3798,3896,4000,4104,4206,4323,14198"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\870103ce12d4503c553c2f969e6a0d11\\transformed\\navigation-ui-2.7.5\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,158", "endColumns": "102,124", "endOffsets": "153,278"}, "to": {"startLines": "139,140", "startColumns": "4,4", "startOffsets": "12970,13073", "endColumns": "102,124", "endOffsets": "13068,13193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d56bc5ab2f06c8c057783f978bdff8f7\\transformed\\material3-1.1.2\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,286,403,519,594,688,800,941,1057,1191,1272,1368,1459,1553,1666,1789,1890,2021,2151,2288,2454,2585,2705,2830,2950,3040,3134,3251,3375,3471,3569,3674,3808,3949,4054,4152,4233,4310,4400,4487,4592,4671,4750,4843,4943,5032,5125,5209,5313,5406,5503,5637,5723,5826", "endColumns": "114,115,116,115,74,93,111,140,115,133,80,95,90,93,112,122,100,130,129,136,165,130,119,124,119,89,93,116,123,95,97,104,133,140,104,97,80,76,89,86,104,78,78,92,99,88,92,83,103,92,96,133,85,102,94", "endOffsets": "165,281,398,514,589,683,795,936,1052,1186,1267,1363,1454,1548,1661,1784,1885,2016,2146,2283,2449,2580,2700,2825,2945,3035,3129,3246,3370,3466,3564,3669,3803,3944,4049,4147,4228,4305,4395,4482,4587,4666,4745,4838,4938,5027,5120,5204,5308,5401,5498,5632,5718,5821,5916"}, "to": {"startLines": "34,35,36,37,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,85,88,148,151,153,157,158,159,160,161,162,163,164,165,166,167,168,169,170", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3057,3172,3288,3405,4820,4895,4989,5101,5242,5358,5492,5573,5669,5760,5854,5967,6090,6191,6322,6452,6589,6755,6886,7006,7131,7251,7341,7435,7552,7676,7772,7870,7975,8109,8250,8355,8646,8879,13765,14015,14203,14576,14655,14734,14827,14927,15016,15109,15193,15297,15390,15487,15621,15707,15810", "endColumns": "114,115,116,115,74,93,111,140,115,133,80,95,90,93,112,122,100,130,129,136,165,130,119,124,119,89,93,116,123,95,97,104,133,140,104,97,80,76,89,86,104,78,78,92,99,88,92,83,103,92,96,133,85,102,94", "endOffsets": "3167,3283,3400,3516,4890,4984,5096,5237,5353,5487,5568,5664,5755,5849,5962,6085,6186,6317,6447,6584,6750,6881,7001,7126,7246,7336,7430,7547,7671,7767,7865,7970,8104,8245,8350,8448,8722,8951,13850,14097,14303,14650,14729,14822,14922,15011,15104,15188,15292,15385,15482,15616,15702,15805,15900"}}]}]}