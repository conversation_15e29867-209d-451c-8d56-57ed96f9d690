package com.workly.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.workly.app.data.model.UserSettings
import com.workly.app.data.model.WeatherLocation
import com.workly.app.data.repository.SettingsRepository
import com.workly.app.data.repository.ShiftRepository
import com.workly.app.data.repository.AttendanceRepository
import com.workly.app.data.repository.NotesRepository
import com.workly.app.data.storage.DataStoreManager
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

class SettingsViewModel(
    private val settingsRepository: SettingsRepository,
    private val shiftRepository: ShiftRepository? = null,
    private val attendanceRepository: AttendanceRepository? = null,
    private val notesRepository: NotesRepository? = null,
    private val dataStoreManager: DataStoreManager? = null,
    private val alarmManager: com.workly.app.business.AlarmManager? = null
) : ViewModel() {

    // State flows
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    private val _successMessage = MutableStateFlow<String?>(null)
    val successMessage: StateFlow<String?> = _successMessage.asStateFlow()

    // Settings data
    val settings = settingsRepository.getSettingsFlow()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = UserSettings()
        )

    /**
     * Update language setting
     */
    fun updateLanguage(language: String) {
        viewModelScope.launch {
            try {
                settingsRepository.updateLanguage(language)
                _successMessage.value = "Đã cập nhật ngôn ngữ"
            } catch (e: Exception) {
                _error.value = "Lỗi khi cập nhật ngôn ngữ: ${e.message}"
            }
        }
    }

    /**
     * Update theme setting
     */
    fun updateTheme(theme: String) {
        viewModelScope.launch {
            try {
                settingsRepository.updateTheme(theme)
                _successMessage.value = "Đã cập nhật giao diện"
            } catch (e: Exception) {
                _error.value = "Lỗi khi cập nhật giao diện: ${e.message}"
            }
        }
    }

    /**
     * Update multi-button mode
     */
    fun updateMultiButtonMode(mode: String) {
        viewModelScope.launch {
            try {
                settingsRepository.updateMultiButtonMode(mode)
                _successMessage.value = "Đã cập nhật chế độ nút đa năng"
            } catch (e: Exception) {
                _error.value = "Lỗi khi cập nhật chế độ nút: ${e.message}"
            }
        }
    }

    /**
     * Update alarm settings
     */
    fun updateAlarmSettings(soundEnabled: Boolean, vibrationEnabled: Boolean) {
        viewModelScope.launch {
            try {
                settingsRepository.updateAlarmSettings(soundEnabled, vibrationEnabled)

                // Update alarm manager
                alarmManager?.onSettingsChanged()

                _successMessage.value = "Đã cập nhật cài đặt báo thức"
            } catch (e: Exception) {
                _error.value = "Lỗi khi cập nhật báo thức: ${e.message}"
            }
        }
    }

    /**
     * Update weather settings
     */
    fun updateWeatherSettings(enabled: Boolean, location: WeatherLocation? = null) {
        viewModelScope.launch {
            try {
                settingsRepository.updateWeatherSettings(enabled, location)
                _successMessage.value = "Đã cập nhật cài đặt thời tiết"
            } catch (e: Exception) {
                _error.value = "Lỗi khi cập nhật thời tiết: ${e.message}"
            }
        }
    }

    /**
     * Update shift reminder mode
     */
    fun updateShiftReminderMode(mode: String) {
        viewModelScope.launch {
            try {
                settingsRepository.updateChangeShiftReminderMode(mode)
                _successMessage.value = "Đã cập nhật nhắc đổi ca"
            } catch (e: Exception) {
                _error.value = "Lỗi khi cập nhật nhắc đổi ca: ${e.message}"
            }
        }
    }

    /**
     * Update time format
     */
    fun updateTimeFormat(format: String) {
        viewModelScope.launch {
            try {
                settingsRepository.updateTimeFormat(format)
                _successMessage.value = "Đã cập nhật định dạng giờ"
            } catch (e: Exception) {
                _error.value = "Lỗi khi cập nhật định dạng giờ: ${e.message}"
            }
        }
    }

    /**
     * Update first day of week
     */
    fun updateFirstDayOfWeek(day: String) {
        viewModelScope.launch {
            try {
                settingsRepository.updateFirstDayOfWeek(day)
                _successMessage.value = "Đã cập nhật ngày bắt đầu tuần"
            } catch (e: Exception) {
                _error.value = "Lỗi khi cập nhật ngày bắt đầu tuần: ${e.message}"
            }
        }
    }

    /**
     * Update notes display limit
     */
    fun updateNotesDisplayLimit(limit: Int) {
        viewModelScope.launch {
            try {
                settingsRepository.updateNotesDisplayLimit(limit)
                _successMessage.value = "Đã cập nhật số lượng ghi chú hiển thị"
            } catch (e: Exception) {
                _error.value = "Lỗi khi cập nhật số lượng ghi chú: ${e.message}"
            }
        }
    }

    /**
     * Update advanced calculation settings
     */
    fun updateAdvancedCalculationSettings(
        lateThresholdMinutes: Int,
        otWeekdayRate: Float,
        otSaturdayRate: Float,
        otSundayRate: Float,
        otHolidayRate: Float
    ) {
        viewModelScope.launch {
            try {
                settingsRepository.updateAdvancedCalculationSettings(
                    lateThresholdMinutes,
                    otWeekdayRate,
                    otSaturdayRate,
                    otSundayRate,
                    otHolidayRate
                )
                _successMessage.value = "Đã cập nhật cài đặt tính công"
            } catch (e: Exception) {
                _error.value = "Lỗi khi cập nhật cài đặt tính công: ${e.message}"
            }
        }
    }

    /**
     * Update rotation settings
     */
    fun updateRotationSettings(shiftIds: List<String>, frequency: String) {
        viewModelScope.launch {
            try {
                settingsRepository.updateRotationSettings(shiftIds, frequency)
                _successMessage.value = "Đã cập nhật cài đặt xoay ca"
            } catch (e: Exception) {
                _error.value = "Lỗi khi cập nhật cài đặt xoay ca: ${e.message}"
            }
        }
    }

    /**
     * Export data for backup
     */
    fun exportData(onSuccess: (String) -> Unit) {
        viewModelScope.launch {
            try {
                _isLoading.value = true

                if (dataStoreManager == null) {
                    _error.value = "Không thể xuất dữ liệu"
                    return@launch
                }

                val allData = dataStoreManager.exportAllData()
                val jsonData = com.google.gson.Gson().toJson(allData)

                onSuccess(jsonData)
                _successMessage.value = "Đã xuất dữ liệu thành công"

            } catch (e: Exception) {
                _error.value = "Lỗi khi xuất dữ liệu: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Import data from backup
     */
    fun importData(jsonData: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true

                if (dataStoreManager == null) {
                    _error.value = "Không thể nhập dữ liệu"
                    return@launch
                }

                val data = com.google.gson.Gson().fromJson<Map<String, String>>(
                    jsonData,
                    object : com.google.gson.reflect.TypeToken<Map<String, String>>() {}.type
                )

                dataStoreManager.importAllData(data)

                // Reschedule alarms after import
                alarmManager?.initializeAlarms()

                _successMessage.value = "Đã nhập dữ liệu thành công"

            } catch (e: Exception) {
                _error.value = "Lỗi khi nhập dữ liệu: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Reset all settings to default
     */
    fun resetToDefaults() {
        viewModelScope.launch {
            try {
                _isLoading.value = true

                settingsRepository.resetToDefaults()

                // Update alarm manager
                alarmManager?.onSettingsChanged()

                _successMessage.value = "Đã khôi phục cài đặt mặc định"

            } catch (e: Exception) {
                _error.value = "Lỗi khi khôi phục cài đặt: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Clear all app data
     */
    fun clearAllData() {
        viewModelScope.launch {
            try {
                _isLoading.value = true

                if (dataStoreManager == null) {
                    _error.value = "Không thể xóa dữ liệu"
                    return@launch
                }

                // Cancel all alarms first
                alarmManager?.cancelActiveShiftAlarms()
                alarmManager?.cancelAllNoteReminders()

                // Clear all data
                dataStoreManager.clearAll()

                _successMessage.value = "Đã xóa toàn bộ dữ liệu"

            } catch (e: Exception) {
                _error.value = "Lỗi khi xóa dữ liệu: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun clearError() {
        _error.value = null
    }

    fun clearSuccessMessage() {
        _successMessage.value = null
    }
}
