# Tóm tắt Khu vực Ghi Chú Sắp Tới

## Tổng quan
Đã hoàn thành việc tạo khu vực "Ghi Chú Sắp Tới" mới với đầy đủ các tính năng theo yêu cầu, bao gồm logic ưu tiên thông minh, <PERSON><PERSON> thu gọn/mở rộng, và tùy chọn cài đặt.

## Các tính năng chính

### 1. <PERSON>i<PERSON><PERSON> đ<PERSON> "Ghi Chú Sắp Tới"
- ✅ Hiển thị tiêu đề với icon ghi chú
- ✅ Nút "Xem Tất Cả" (→) điều hướng đến NotesScreen
- ✅ Nút "+" thêm ghi chú mới

### 2. Logic "Slot Đảm bảo + Ưu tiên Sắp xếp"
- ✅ **Slot 1**: <PERSON>hi chú có nhắc nhở sắp tới hôm nay (sắp xếp theo thời gian)
- ✅ **Slot 2**: <PERSON><PERSON> chú ưu tiên cao (sắp xếp theo thời gian tạo)
- ✅ **Slot 3**: <PERSON><PERSON> chú có nhắc nhở ngày khác (sắp xếp theo ưu tiên)
- ✅ **Slot 4**: Ghi chú thường (sắp xếp theo thời gian cập nhật)

### 3. Hiển thị Top N ghi chú
- ✅ Người dùng có thể chọn N = 2, 3, hoặc 5 trong Settings
- ✅ Mặc định hiển thị 3 ghi chú
- ✅ Hiển thị số lượng ghi chú còn lại nếu có

### 4. Item ghi chú thu gọn
- ✅ **Icon ưu tiên**: ⭐ cho ưu tiên cao, ⚪ cho bình thường, 🔵 cho thấp
- ✅ **Tiêu đề**: Rút gọn tối đa 25 ký tự
- ✅ **Nội dung**: Rút gọn tối đa 35 ký tự (chỉ hiện khi thu gọn)
- ✅ **Nhắc lúc tiếp theo**: Hiển thị thời gian nhắc với icon đồng hồ
- ✅ **Nút Sửa**: ✏️ điều hướng đến màn hình chỉnh sửa
- ✅ **Nút Xóa**: 🗑️ xóa ghi chú
- ✅ **Mở rộng/Thu gọn**: Bấm để xem chi tiết đầy đủ

### 5. Chế độ mở rộng
- ✅ Hiển thị nội dung đầy đủ
- ✅ Thông tin chi tiết: ưu tiên, ngày nhắc, ca liên kết
- ✅ Thời gian tạo/cập nhật
- ✅ Animation mượt mà khi mở/đóng

## Các file đã tạo/cập nhật

### 1. Note.kt (Cập nhật)
- Thêm thuộc tính `priority: NotePriority`
- Thêm enum `NotePriority` (LOW, NORMAL, HIGH)
- Thêm các phương thức tiện ích:
  - `getTruncatedTitle()`: Rút gọn tiêu đề
  - `getTruncatedContent()`: Rút gọn nội dung
  - `getNextReminderTime()`: Lấy thời gian nhắc tiếp theo
  - `isHighPriority()`: Kiểm tra ưu tiên cao

### 2. NotePriorityManager.kt (Mới)
- Class quản lý logic ưu tiên và sắp xếp ghi chú
- Thuật toán "Slot Đảm bảo + Ưu tiên Sắp xếp"
- Các phương thức:
  - `sortNotesWithPriority()`: Sắp xếp theo ưu tiên
  - `getTopNotesForDisplay()`: Lấy top N ghi chú
  - `getNotesForToday()`: Ghi chú cần nhắc hôm nay
  - `getUpcomingReminderNotes()`: Ghi chú sắp đến giờ nhắc

### 3. NoteItemCompact.kt (Mới)
- Component hiển thị ghi chú dạng thu gọn
- Hỗ trợ mở rộng/thu gọn với animation
- Hiển thị đầy đủ thông tin theo yêu cầu
- Responsive design với các nút action

### 4. UpcomingNotesSection.kt (Mới)
- Component chính cho khu vực ghi chú
- Tích hợp NotePriorityManager
- Header với tiêu đề và các nút action
- Xử lý trạng thái empty state
- Hiển thị số lượng ghi chú còn lại

### 5. UserSettings.kt (Cập nhật)
- Thêm `notesDisplayLimit: Int = 3`
- Hỗ trợ tùy chọn 2, 3, hoặc 5 ghi chú

### 6. SettingsRepository.kt (Cập nhật)
- Thêm `updateNotesDisplayLimit(limit: Int)`

### 7. SettingsViewModel.kt (Cập nhật)
- Thêm `updateNotesDisplayLimit(limit: Int)`

### 8. NotesSettingsSection.kt (Mới)
- Component settings cho ghi chú
- Radio buttons cho chọn số lượng hiển thị
- Mô tả chi tiết cho từng tùy chọn
- Card thông tin về logic sắp xếp

### 9. SettingsScreen.kt (Cập nhật)
- Thêm NotesSettingsSection vào danh sách settings

### 10. HomeScreen.kt (Cập nhật)
- Thay thế WorkNotesSection bằng UpcomingNotesSection
- Tích hợp với settings để lấy displayLimit
- Kết nối với navigation cho edit/delete

### 11. HomeViewModel.kt (Cập nhật)
- Cập nhật logic lấy ghi chú
- Thêm phương thức `deleteNote()`
- Tích hợp với settings flow

### 12. Các component hỗ trợ (Mới)
- `TopBarSection.kt`: Header với thời gian và settings
- `WeatherWarningSection.kt`: Cảnh báo thời tiết
- `CurrentShiftSection.kt`: Thông tin ca hiện tại

## Cách sử dụng

### 1. Xem ghi chú
- Ghi chú được hiển thị theo thứ tự ưu tiên
- Bấm vào item để mở rộng/thu gọn
- Ghi chú ưu tiên cao có icon ⭐

### 2. Thao tác với ghi chú
- **Thêm mới**: Bấm nút "+" hoặc "Thêm ghi chú đầu tiên"
- **Chỉnh sửa**: Bấm nút ✏️ trên item
- **Xóa**: Bấm nút 🗑️ trên item
- **Xem tất cả**: Bấm "Xem Tất Cả" để đến NotesScreen

### 3. Cài đặt
- Vào Settings → Cài đặt ghi chú
- Chọn số lượng hiển thị: 2, 3, hoặc 5
- Thay đổi áp dụng ngay lập tức

## Logic ưu tiên chi tiết

### Thuật toán sắp xếp:
1. **Nhắc nhở sắp tới hôm nay** (trong vòng 2 giờ tới)
   - Sắp xếp theo: thời gian nhắc → ưu tiên
   
2. **Ghi chú ưu tiên cao** (không phụ thuộc thời gian)
   - Sắp xếp theo: thời gian tạo (cũ nhất trước)
   
3. **Ghi chú có nhắc nhở ngày khác**
   - Sắp xếp theo: ưu tiên → thời gian tạo
   
4. **Ghi chú thường**
   - Sắp xếp theo: thời gian cập nhật (mới nhất trước)

### Ví dụ thứ tự hiển thị:
1. Ghi chú nhắc lúc 14:00 hôm nay (hiện tại 13:30)
2. Ghi chú ưu tiên cao tạo sáng nay
3. Ghi chú nhắc ngày mai, ưu tiên cao
4. Ghi chú thường cập nhật gần đây

## Lợi ích

### 1. Cho người dùng
- **Thông minh**: Ghi chú quan trọng luôn hiển thị trước
- **Tiện lợi**: Thao tác nhanh ngay trên màn hình chính
- **Linh hoạt**: Tùy chỉnh số lượng hiển thị theo nhu cầu
- **Trực quan**: UI rõ ràng với icon và màu sắc phân biệt

### 2. Cho trải nghiệm
- **Không gian tối ưu**: Thu gọn khi không cần, mở rộng khi cần chi tiết
- **Phản hồi nhanh**: Animation mượt mà, thao tác responsive
- **Thông tin đầy đủ**: Hiển thị đủ thông tin cần thiết
- **Dễ sử dụng**: Logic sắp xếp tự động, không cần can thiệp

### 3. Cho hệ thống
- **Hiệu suất**: Chỉ hiển thị số lượng cần thiết
- **Mở rộng**: Dễ dàng thêm tính năng mới
- **Bảo trì**: Code có cấu trúc rõ ràng, dễ debug

## Kết luận
Khu vực "Ghi Chú Sắp Tới" đã được hoàn thành với đầy đủ tính năng theo yêu cầu. Hệ thống ưu tiên thông minh đảm bảo ghi chú quan trọng luôn được hiển thị trước, trong khi UI thu gọn/mở rộng giúp tối ưu không gian màn hình. Người dùng có thể tùy chỉnh số lượng hiển thị và thao tác dễ dàng với ghi chú ngay trên màn hình chính.
