package com.workly.app.data.repository

import com.google.gson.reflect.TypeToken
import com.workly.app.data.model.Note
import com.workly.app.data.model.Shift
import com.workly.app.data.storage.DataStoreManager
import kotlinx.coroutines.flow.Flow

class NotesRepository(private val dataStoreManager: DataStoreManager) {
    
    private val notesType = object : TypeToken<List<Note>>() {}
    
    suspend fun saveNotes(notes: List<Note>) {
        dataStoreManager.storeData(DataStoreManager.NOTES_KEY, notes)
    }
    
    suspend fun getNotes(): List<Note> {
        return dataStoreManager.getData(
            DataStoreManager.NOTES_KEY,
            notesType,
            emptyList()
        )
    }
    
    fun getNotesFlow(): Flow<List<Note>> {
        return dataStoreManager.getDataFlow(
            DataStoreManager.NOTES_KEY,
            notesType,
            emptyList()
        )
    }
    
    suspend fun addNote(note: Note) {
        val currentNotes = getNotes().toMutableList()
        currentNotes.add(note)
        saveNotes(currentNotes)
    }
    
    suspend fun updateNote(updatedNote: Note) {
        val currentNotes = getNotes().toMutableList()
        val index = currentNotes.indexOfFirst { it.id == updatedNote.id }
        if (index != -1) {
            currentNotes[index] = updatedNote.copy(
                updatedAt = kotlinx.datetime.Clock.System.now().toString()
            )
            saveNotes(currentNotes)
        }
    }
    
    suspend fun deleteNote(noteId: String) {
        val currentNotes = getNotes().toMutableList()
        currentNotes.removeAll { it.id == noteId }
        saveNotes(currentNotes)
    }
    
    suspend fun getNoteById(noteId: String): Note? {
        return getNotes().find { it.id == noteId }
    }
    
    suspend fun getActiveNotes(): List<Note> {
        return getNotes().filter { it.isActive }
    }
    
    suspend fun getNotesWithReminders(): List<Note> {
        return getNotes().filter { it.isActive && it.reminderTime != null }
    }
    
    suspend fun getNotesForShift(shiftId: String): List<Note> {
        return getNotes().filter { 
            it.isActive && it.associatedShiftIds.contains(shiftId) 
        }
    }
    
    suspend fun getNotesForToday(dayOfWeek: String, activeShift: Shift?): List<Note> {
        return getNotes().filter { note ->
            note.shouldRemindToday(dayOfWeek, activeShift)
        }
    }
    
    suspend fun getRecentNotes(limit: Int = 3): List<Note> {
        return getNotes()
            .filter { it.isActive }
            .sortedByDescending { it.updatedAt }
            .take(limit)
    }
    
    suspend fun searchNotes(query: String): List<Note> {
        val lowercaseQuery = query.lowercase()
        return getNotes().filter { note ->
            note.isActive && (
                note.title.lowercase().contains(lowercaseQuery) ||
                note.content.lowercase().contains(lowercaseQuery)
            )
        }
    }
    
    suspend fun toggleNoteActive(noteId: String) {
        val note = getNoteById(noteId)
        if (note != null) {
            updateNote(note.copy(isActive = !note.isActive))
        }
    }
    
    // Cleanup methods
    suspend fun deleteNotesForShift(shiftId: String) {
        val currentNotes = getNotes().toMutableList()
        currentNotes.removeAll { it.associatedShiftIds.contains(shiftId) }
        saveNotes(currentNotes)
    }
    
    suspend fun updateNotesForShiftDeletion(deletedShiftId: String) {
        val currentNotes = getNotes().toMutableList()
        val updatedNotes = currentNotes.map { note ->
            if (note.associatedShiftIds.contains(deletedShiftId)) {
                note.copy(
                    associatedShiftIds = note.associatedShiftIds.filter { it != deletedShiftId },
                    updatedAt = kotlinx.datetime.Clock.System.now().toString()
                )
            } else {
                note
            }
        }
        saveNotes(updatedNotes)
    }
}
