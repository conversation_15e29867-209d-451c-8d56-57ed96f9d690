int anim abc_fade_in 0x7f010000
int anim abc_fade_out 0x7f010001
int anim abc_grow_fade_in_from_bottom 0x7f010002
int anim abc_popup_enter 0x7f010003
int anim abc_popup_exit 0x7f010004
int anim abc_shrink_fade_out_from_bottom 0x7f010005
int anim abc_slide_in_bottom 0x7f010006
int anim abc_slide_in_top 0x7f010007
int anim abc_slide_out_bottom 0x7f010008
int anim abc_slide_out_top 0x7f010009
int anim abc_tooltip_enter 0x7f01000a
int anim abc_tooltip_exit 0x7f01000b
int anim btn_checkbox_to_checked_box_inner_merged_animation 0x7f01000c
int anim btn_checkbox_to_checked_box_outer_merged_animation 0x7f01000d
int anim btn_checkbox_to_checked_icon_null_animation 0x7f01000e
int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x7f01000f
int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x7f010010
int anim btn_checkbox_to_unchecked_icon_null_animation 0x7f010011
int anim btn_radio_to_off_mtrl_dot_group_animation 0x7f010012
int anim btn_radio_to_off_mtrl_ring_outer_animation 0x7f010013
int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x7f010014
int anim btn_radio_to_on_mtrl_dot_group_animation 0x7f010015
int anim btn_radio_to_on_mtrl_ring_outer_animation 0x7f010016
int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x7f010017
int anim design_bottom_sheet_slide_in 0x7f010018
int anim design_bottom_sheet_slide_out 0x7f010019
int anim design_snackbar_in 0x7f01001a
int anim design_snackbar_out 0x7f01001b
int anim fragment_fast_out_extra_slow_in 0x7f01001c
int anim mtrl_bottom_sheet_slide_in 0x7f01001d
int anim mtrl_bottom_sheet_slide_out 0x7f01001e
int anim mtrl_card_lowers_interpolator 0x7f01001f
int anim nav_default_enter_anim 0x7f010020
int anim nav_default_exit_anim 0x7f010021
int anim nav_default_pop_enter_anim 0x7f010022
int anim nav_default_pop_exit_anim 0x7f010023
int animator design_appbar_state_list_animator 0x7f020000
int animator design_fab_hide_motion_spec 0x7f020001
int animator design_fab_show_motion_spec 0x7f020002
int animator fragment_close_enter 0x7f020003
int animator fragment_close_exit 0x7f020004
int animator fragment_fade_enter 0x7f020005
int animator fragment_fade_exit 0x7f020006
int animator fragment_open_enter 0x7f020007
int animator fragment_open_exit 0x7f020008
int animator linear_indeterminate_line1_head_interpolator 0x7f020009
int animator linear_indeterminate_line1_tail_interpolator 0x7f02000a
int animator linear_indeterminate_line2_head_interpolator 0x7f02000b
int animator linear_indeterminate_line2_tail_interpolator 0x7f02000c
int animator mtrl_btn_state_list_anim 0x7f02000d
int animator mtrl_btn_unelevated_state_list_anim 0x7f02000e
int animator mtrl_card_state_list_anim 0x7f02000f
int animator mtrl_chip_state_list_anim 0x7f020010
int animator mtrl_extended_fab_change_size_collapse_motion_spec 0x7f020011
int animator mtrl_extended_fab_change_size_expand_motion_spec 0x7f020012
int animator mtrl_extended_fab_hide_motion_spec 0x7f020013
int animator mtrl_extended_fab_show_motion_spec 0x7f020014
int animator mtrl_extended_fab_state_list_animator 0x7f020015
int animator mtrl_fab_hide_motion_spec 0x7f020016
int animator mtrl_fab_show_motion_spec 0x7f020017
int animator mtrl_fab_transformation_sheet_collapse_spec 0x7f020018
int animator mtrl_fab_transformation_sheet_expand_spec 0x7f020019
int animator nav_default_enter_anim 0x7f02001a
int animator nav_default_exit_anim 0x7f02001b
int animator nav_default_pop_enter_anim 0x7f02001c
int animator nav_default_pop_exit_anim 0x7f02001d
int attr action 0x7f030000
int attr actionBarDivider 0x7f030001
int attr actionBarItemBackground 0x7f030002
int attr actionBarPopupTheme 0x7f030003
int attr actionBarSize 0x7f030004
int attr actionBarSplitStyle 0x7f030005
int attr actionBarStyle 0x7f030006
int attr actionBarTabBarStyle 0x7f030007
int attr actionBarTabStyle 0x7f030008
int attr actionBarTabTextStyle 0x7f030009
int attr actionBarTheme 0x7f03000a
int attr actionBarWidgetTheme 0x7f03000b
int attr actionButtonStyle 0x7f03000c
int attr actionDropDownStyle 0x7f03000d
int attr actionLayout 0x7f03000e
int attr actionMenuTextAppearance 0x7f03000f
int attr actionMenuTextColor 0x7f030010
int attr actionModeBackground 0x7f030011
int attr actionModeCloseButtonStyle 0x7f030012
int attr actionModeCloseDrawable 0x7f030013
int attr actionModeCopyDrawable 0x7f030014
int attr actionModeCutDrawable 0x7f030015
int attr actionModeFindDrawable 0x7f030016
int attr actionModePasteDrawable 0x7f030017
int attr actionModePopupWindowStyle 0x7f030018
int attr actionModeSelectAllDrawable 0x7f030019
int attr actionModeShareDrawable 0x7f03001a
int attr actionModeSplitBackground 0x7f03001b
int attr actionModeStyle 0x7f03001c
int attr actionModeWebSearchDrawable 0x7f03001d
int attr actionOverflowButtonStyle 0x7f03001e
int attr actionOverflowMenuStyle 0x7f03001f
int attr actionProviderClass 0x7f030020
int attr actionTextColorAlpha 0x7f030021
int attr actionViewClass 0x7f030022
int attr activityAction 0x7f030023
int attr activityChooserViewStyle 0x7f030024
int attr activityName 0x7f030025
int attr alertDialogButtonGroupStyle 0x7f030026
int attr alertDialogCenterButtons 0x7f030027
int attr alertDialogStyle 0x7f030028
int attr alertDialogTheme 0x7f030029
int attr allowStacking 0x7f03002a
int attr alpha 0x7f03002b
int attr alphabeticModifiers 0x7f03002c
int attr altSrc 0x7f03002d
int attr alwaysExpand 0x7f03002e
int attr animate_relativeTo 0x7f03002f
int attr animationMode 0x7f030030
int attr appBarLayoutStyle 0x7f030031
int attr applyMotionScene 0x7f030032
int attr arcMode 0x7f030033
int attr argType 0x7f030034
int attr arrowHeadLength 0x7f030035
int attr arrowShaftLength 0x7f030036
int attr attributeName 0x7f030037
int attr autoCompleteTextViewStyle 0x7f030038
int attr autoSizeMaxTextSize 0x7f030039
int attr autoSizeMinTextSize 0x7f03003a
int attr autoSizePresetSizes 0x7f03003b
int attr autoSizeStepGranularity 0x7f03003c
int attr autoSizeTextType 0x7f03003d
int attr autoTransition 0x7f03003e
int attr background 0x7f03003f
int attr backgroundColor 0x7f030040
int attr backgroundInsetBottom 0x7f030041
int attr backgroundInsetEnd 0x7f030042
int attr backgroundInsetStart 0x7f030043
int attr backgroundInsetTop 0x7f030044
int attr backgroundOverlayColorAlpha 0x7f030045
int attr backgroundSplit 0x7f030046
int attr backgroundStacked 0x7f030047
int attr backgroundTint 0x7f030048
int attr backgroundTintMode 0x7f030049
int attr badgeGravity 0x7f03004a
int attr badgeStyle 0x7f03004b
int attr badgeTextColor 0x7f03004c
int attr barLength 0x7f03004d
int attr barrierAllowsGoneWidgets 0x7f03004e
int attr barrierDirection 0x7f03004f
int attr barrierMargin 0x7f030050
int attr behavior_autoHide 0x7f030051
int attr behavior_autoShrink 0x7f030052
int attr behavior_draggable 0x7f030053
int attr behavior_expandedOffset 0x7f030054
int attr behavior_fitToContents 0x7f030055
int attr behavior_halfExpandedRatio 0x7f030056
int attr behavior_hideable 0x7f030057
int attr behavior_overlapTop 0x7f030058
int attr behavior_peekHeight 0x7f030059
int attr behavior_saveFlags 0x7f03005a
int attr behavior_skipCollapsed 0x7f03005b
int attr borderWidth 0x7f03005c
int attr borderlessButtonStyle 0x7f03005d
int attr bottomAppBarStyle 0x7f03005e
int attr bottomNavigationStyle 0x7f03005f
int attr bottomSheetDialogTheme 0x7f030060
int attr bottomSheetStyle 0x7f030061
int attr boxBackgroundColor 0x7f030062
int attr boxBackgroundMode 0x7f030063
int attr boxCollapsedPaddingTop 0x7f030064
int attr boxCornerRadiusBottomEnd 0x7f030065
int attr boxCornerRadiusBottomStart 0x7f030066
int attr boxCornerRadiusTopEnd 0x7f030067
int attr boxCornerRadiusTopStart 0x7f030068
int attr boxStrokeColor 0x7f030069
int attr boxStrokeErrorColor 0x7f03006a
int attr boxStrokeWidth 0x7f03006b
int attr boxStrokeWidthFocused 0x7f03006c
int attr brightness 0x7f03006d
int attr buttonBarButtonStyle 0x7f03006e
int attr buttonBarNegativeButtonStyle 0x7f03006f
int attr buttonBarNeutralButtonStyle 0x7f030070
int attr buttonBarPositiveButtonStyle 0x7f030071
int attr buttonBarStyle 0x7f030072
int attr buttonCompat 0x7f030073
int attr buttonGravity 0x7f030074
int attr buttonIconDimen 0x7f030075
int attr buttonPanelSideLayout 0x7f030076
int attr buttonStyle 0x7f030077
int attr buttonStyleSmall 0x7f030078
int attr buttonTint 0x7f030079
int attr buttonTintMode 0x7f03007a
int attr cardBackgroundColor 0x7f03007b
int attr cardCornerRadius 0x7f03007c
int attr cardElevation 0x7f03007d
int attr cardForegroundColor 0x7f03007e
int attr cardMaxElevation 0x7f03007f
int attr cardPreventCornerOverlap 0x7f030080
int attr cardUseCompatPadding 0x7f030081
int attr cardViewStyle 0x7f030082
int attr chainUseRtl 0x7f030083
int attr checkboxStyle 0x7f030084
int attr checkedButton 0x7f030085
int attr checkedChip 0x7f030086
int attr checkedIcon 0x7f030087
int attr checkedIconEnabled 0x7f030088
int attr checkedIconMargin 0x7f030089
int attr checkedIconSize 0x7f03008a
int attr checkedIconTint 0x7f03008b
int attr checkedIconVisible 0x7f03008c
int attr checkedTextViewStyle 0x7f03008d
int attr chipBackgroundColor 0x7f03008e
int attr chipCornerRadius 0x7f03008f
int attr chipEndPadding 0x7f030090
int attr chipGroupStyle 0x7f030091
int attr chipIcon 0x7f030092
int attr chipIconEnabled 0x7f030093
int attr chipIconSize 0x7f030094
int attr chipIconTint 0x7f030095
int attr chipIconVisible 0x7f030096
int attr chipMinHeight 0x7f030097
int attr chipMinTouchTargetSize 0x7f030098
int attr chipSpacing 0x7f030099
int attr chipSpacingHorizontal 0x7f03009a
int attr chipSpacingVertical 0x7f03009b
int attr chipStandaloneStyle 0x7f03009c
int attr chipStartPadding 0x7f03009d
int attr chipStrokeColor 0x7f03009e
int attr chipStrokeWidth 0x7f03009f
int attr chipStyle 0x7f0300a0
int attr chipSurfaceColor 0x7f0300a1
int attr circleRadius 0x7f0300a2
int attr circularProgressIndicatorStyle 0x7f0300a3
int attr clearTop 0x7f0300a4
int attr clickAction 0x7f0300a5
int attr clockFaceBackgroundColor 0x7f0300a6
int attr clockHandColor 0x7f0300a7
int attr clockIcon 0x7f0300a8
int attr clockNumberTextColor 0x7f0300a9
int attr closeIcon 0x7f0300aa
int attr closeIconEnabled 0x7f0300ab
int attr closeIconEndPadding 0x7f0300ac
int attr closeIconSize 0x7f0300ad
int attr closeIconStartPadding 0x7f0300ae
int attr closeIconTint 0x7f0300af
int attr closeIconVisible 0x7f0300b0
int attr closeItemLayout 0x7f0300b1
int attr collapseContentDescription 0x7f0300b2
int attr collapseIcon 0x7f0300b3
int attr collapsedSize 0x7f0300b4
int attr collapsedTitleGravity 0x7f0300b5
int attr collapsedTitleTextAppearance 0x7f0300b6
int attr collapsingToolbarLayoutStyle 0x7f0300b7
int attr color 0x7f0300b8
int attr colorAccent 0x7f0300b9
int attr colorBackgroundFloating 0x7f0300ba
int attr colorButtonNormal 0x7f0300bb
int attr colorControlActivated 0x7f0300bc
int attr colorControlHighlight 0x7f0300bd
int attr colorControlNormal 0x7f0300be
int attr colorError 0x7f0300bf
int attr colorOnBackground 0x7f0300c0
int attr colorOnError 0x7f0300c1
int attr colorOnPrimary 0x7f0300c2
int attr colorOnPrimarySurface 0x7f0300c3
int attr colorOnSecondary 0x7f0300c4
int attr colorOnSurface 0x7f0300c5
int attr colorPrimary 0x7f0300c6
int attr colorPrimaryDark 0x7f0300c7
int attr colorPrimarySurface 0x7f0300c8
int attr colorPrimaryVariant 0x7f0300c9
int attr colorSecondary 0x7f0300ca
int attr colorSecondaryVariant 0x7f0300cb
int attr colorSurface 0x7f0300cc
int attr colorSwitchThumbNormal 0x7f0300cd
int attr commitIcon 0x7f0300ce
int attr constraintSet 0x7f0300cf
int attr constraintSetEnd 0x7f0300d0
int attr constraintSetStart 0x7f0300d1
int attr constraint_referenced_ids 0x7f0300d2
int attr constraints 0x7f0300d3
int attr content 0x7f0300d4
int attr contentDescription 0x7f0300d5
int attr contentInsetEnd 0x7f0300d6
int attr contentInsetEndWithActions 0x7f0300d7
int attr contentInsetLeft 0x7f0300d8
int attr contentInsetRight 0x7f0300d9
int attr contentInsetStart 0x7f0300da
int attr contentInsetStartWithNavigation 0x7f0300db
int attr contentPadding 0x7f0300dc
int attr contentPaddingBottom 0x7f0300dd
int attr contentPaddingEnd 0x7f0300de
int attr contentPaddingLeft 0x7f0300df
int attr contentPaddingRight 0x7f0300e0
int attr contentPaddingStart 0x7f0300e1
int attr contentPaddingTop 0x7f0300e2
int attr contentScrim 0x7f0300e3
int attr contrast 0x7f0300e4
int attr controlBackground 0x7f0300e5
int attr coordinatorLayoutStyle 0x7f0300e6
int attr cornerFamily 0x7f0300e7
int attr cornerFamilyBottomLeft 0x7f0300e8
int attr cornerFamilyBottomRight 0x7f0300e9
int attr cornerFamilyTopLeft 0x7f0300ea
int attr cornerFamilyTopRight 0x7f0300eb
int attr cornerRadius 0x7f0300ec
int attr cornerSize 0x7f0300ed
int attr cornerSizeBottomLeft 0x7f0300ee
int attr cornerSizeBottomRight 0x7f0300ef
int attr cornerSizeTopLeft 0x7f0300f0
int attr cornerSizeTopRight 0x7f0300f1
int attr counterEnabled 0x7f0300f2
int attr counterMaxLength 0x7f0300f3
int attr counterOverflowTextAppearance 0x7f0300f4
int attr counterOverflowTextColor 0x7f0300f5
int attr counterTextAppearance 0x7f0300f6
int attr counterTextColor 0x7f0300f7
int attr crossfade 0x7f0300f8
int attr currentState 0x7f0300f9
int attr curveFit 0x7f0300fa
int attr customBoolean 0x7f0300fb
int attr customColorDrawableValue 0x7f0300fc
int attr customColorValue 0x7f0300fd
int attr customDimension 0x7f0300fe
int attr customFloatValue 0x7f0300ff
int attr customIntegerValue 0x7f030100
int attr customNavigationLayout 0x7f030101
int attr customPixelDimension 0x7f030102
int attr customStringValue 0x7f030103
int attr data 0x7f030104
int attr dataPattern 0x7f030105
int attr dayInvalidStyle 0x7f030106
int attr daySelectedStyle 0x7f030107
int attr dayStyle 0x7f030108
int attr dayTodayStyle 0x7f030109
int attr defaultDuration 0x7f03010a
int attr defaultNavHost 0x7f03010b
int attr defaultQueryHint 0x7f03010c
int attr defaultState 0x7f03010d
int attr deltaPolarAngle 0x7f03010e
int attr deltaPolarRadius 0x7f03010f
int attr deriveConstraintsFrom 0x7f030110
int attr destination 0x7f030111
int attr dialogCornerRadius 0x7f030112
int attr dialogPreferredPadding 0x7f030113
int attr dialogTheme 0x7f030114
int attr displayOptions 0x7f030115
int attr divider 0x7f030116
int attr dividerHorizontal 0x7f030117
int attr dividerPadding 0x7f030118
int attr dividerVertical 0x7f030119
int attr dragDirection 0x7f03011a
int attr dragScale 0x7f03011b
int attr dragThreshold 0x7f03011c
int attr drawPath 0x7f03011d
int attr drawableBottomCompat 0x7f03011e
int attr drawableEndCompat 0x7f03011f
int attr drawableLeftCompat 0x7f030120
int attr drawableRightCompat 0x7f030121
int attr drawableSize 0x7f030122
int attr drawableStartCompat 0x7f030123
int attr drawableTint 0x7f030124
int attr drawableTintMode 0x7f030125
int attr drawableTopCompat 0x7f030126
int attr drawerArrowStyle 0x7f030127
int attr drawerLayoutStyle 0x7f030128
int attr dropDownListViewStyle 0x7f030129
int attr dropdownListPreferredItemHeight 0x7f03012a
int attr duration 0x7f03012b
int attr editTextBackground 0x7f03012c
int attr editTextColor 0x7f03012d
int attr editTextStyle 0x7f03012e
int attr elevation 0x7f03012f
int attr elevationOverlayColor 0x7f030130
int attr elevationOverlayEnabled 0x7f030131
int attr enableEdgeToEdge 0x7f030132
int attr endIconCheckable 0x7f030133
int attr endIconContentDescription 0x7f030134
int attr endIconDrawable 0x7f030135
int attr endIconMode 0x7f030136
int attr endIconTint 0x7f030137
int attr endIconTintMode 0x7f030138
int attr enforceMaterialTheme 0x7f030139
int attr enforceTextAppearance 0x7f03013a
int attr ensureMinTouchTargetSize 0x7f03013b
int attr enterAnim 0x7f03013c
int attr errorContentDescription 0x7f03013d
int attr errorEnabled 0x7f03013e
int attr errorIconDrawable 0x7f03013f
int attr errorIconTint 0x7f030140
int attr errorIconTintMode 0x7f030141
int attr errorTextAppearance 0x7f030142
int attr errorTextColor 0x7f030143
int attr exitAnim 0x7f030144
int attr expandActivityOverflowButtonDrawable 0x7f030145
int attr expanded 0x7f030146
int attr expandedHintEnabled 0x7f030147
int attr expandedTitleGravity 0x7f030148
int attr expandedTitleMargin 0x7f030149
int attr expandedTitleMarginBottom 0x7f03014a
int attr expandedTitleMarginEnd 0x7f03014b
int attr expandedTitleMarginStart 0x7f03014c
int attr expandedTitleMarginTop 0x7f03014d
int attr expandedTitleTextAppearance 0x7f03014e
int attr extendMotionSpec 0x7f03014f
int attr extendedFloatingActionButtonStyle 0x7f030150
int attr fabAlignmentMode 0x7f030151
int attr fabAnimationMode 0x7f030152
int attr fabCradleMargin 0x7f030153
int attr fabCradleRoundedCornerRadius 0x7f030154
int attr fabCradleVerticalOffset 0x7f030155
int attr fabCustomSize 0x7f030156
int attr fabSize 0x7f030157
int attr fastScrollEnabled 0x7f030158
int attr fastScrollHorizontalThumbDrawable 0x7f030159
int attr fastScrollHorizontalTrackDrawable 0x7f03015a
int attr fastScrollVerticalThumbDrawable 0x7f03015b
int attr fastScrollVerticalTrackDrawable 0x7f03015c
int attr finishPrimaryWithSecondary 0x7f03015d
int attr finishSecondaryWithPrimary 0x7f03015e
int attr firstBaselineToTopHeight 0x7f03015f
int attr floatingActionButtonStyle 0x7f030160
int attr flow_firstHorizontalBias 0x7f030161
int attr flow_firstHorizontalStyle 0x7f030162
int attr flow_firstVerticalBias 0x7f030163
int attr flow_firstVerticalStyle 0x7f030164
int attr flow_horizontalAlign 0x7f030165
int attr flow_horizontalBias 0x7f030166
int attr flow_horizontalGap 0x7f030167
int attr flow_horizontalStyle 0x7f030168
int attr flow_lastHorizontalBias 0x7f030169
int attr flow_lastHorizontalStyle 0x7f03016a
int attr flow_lastVerticalBias 0x7f03016b
int attr flow_lastVerticalStyle 0x7f03016c
int attr flow_maxElementsWrap 0x7f03016d
int attr flow_padding 0x7f03016e
int attr flow_verticalAlign 0x7f03016f
int attr flow_verticalBias 0x7f030170
int attr flow_verticalGap 0x7f030171
int attr flow_verticalStyle 0x7f030172
int attr flow_wrapMode 0x7f030173
int attr font 0x7f030174
int attr fontFamily 0x7f030175
int attr fontProviderAuthority 0x7f030176
int attr fontProviderCerts 0x7f030177
int attr fontProviderFetchStrategy 0x7f030178
int attr fontProviderFetchTimeout 0x7f030179
int attr fontProviderPackage 0x7f03017a
int attr fontProviderQuery 0x7f03017b
int attr fontProviderSystemFontFamily 0x7f03017c
int attr fontStyle 0x7f03017d
int attr fontVariationSettings 0x7f03017e
int attr fontWeight 0x7f03017f
int attr foregroundInsidePadding 0x7f030180
int attr framePosition 0x7f030181
int attr gapBetweenBars 0x7f030182
int attr gestureInsetBottomIgnored 0x7f030183
int attr goIcon 0x7f030184
int attr graph 0x7f030185
int attr haloColor 0x7f030186
int attr haloRadius 0x7f030187
int attr headerLayout 0x7f030188
int attr height 0x7f030189
int attr helperText 0x7f03018a
int attr helperTextEnabled 0x7f03018b
int attr helperTextTextAppearance 0x7f03018c
int attr helperTextTextColor 0x7f03018d
int attr hideAnimationBehavior 0x7f03018e
int attr hideMotionSpec 0x7f03018f
int attr hideOnContentScroll 0x7f030190
int attr hideOnScroll 0x7f030191
int attr hintAnimationEnabled 0x7f030192
int attr hintEnabled 0x7f030193
int attr hintTextAppearance 0x7f030194
int attr hintTextColor 0x7f030195
int attr homeAsUpIndicator 0x7f030196
int attr homeLayout 0x7f030197
int attr horizontalOffset 0x7f030198
int attr hoveredFocusedTranslationZ 0x7f030199
int attr icon 0x7f03019a
int attr iconEndPadding 0x7f03019b
int attr iconGravity 0x7f03019c
int attr iconPadding 0x7f03019d
int attr iconSize 0x7f03019e
int attr iconStartPadding 0x7f03019f
int attr iconTint 0x7f0301a0
int attr iconTintMode 0x7f0301a1
int attr iconifiedByDefault 0x7f0301a2
int attr imageButtonStyle 0x7f0301a3
int attr indeterminateAnimationType 0x7f0301a4
int attr indeterminateProgressStyle 0x7f0301a5
int attr indicatorColor 0x7f0301a6
int attr indicatorDirectionCircular 0x7f0301a7
int attr indicatorDirectionLinear 0x7f0301a8
int attr indicatorInset 0x7f0301a9
int attr indicatorSize 0x7f0301aa
int attr initialActivityCount 0x7f0301ab
int attr insetForeground 0x7f0301ac
int attr isLightTheme 0x7f0301ad
int attr isMaterialTheme 0x7f0301ae
int attr itemBackground 0x7f0301af
int attr itemFillColor 0x7f0301b0
int attr itemHorizontalPadding 0x7f0301b1
int attr itemHorizontalTranslationEnabled 0x7f0301b2
int attr itemIconPadding 0x7f0301b3
int attr itemIconSize 0x7f0301b4
int attr itemIconTint 0x7f0301b5
int attr itemMaxLines 0x7f0301b6
int attr itemPadding 0x7f0301b7
int attr itemRippleColor 0x7f0301b8
int attr itemShapeAppearance 0x7f0301b9
int attr itemShapeAppearanceOverlay 0x7f0301ba
int attr itemShapeFillColor 0x7f0301bb
int attr itemShapeInsetBottom 0x7f0301bc
int attr itemShapeInsetEnd 0x7f0301bd
int attr itemShapeInsetStart 0x7f0301be
int attr itemShapeInsetTop 0x7f0301bf
int attr itemSpacing 0x7f0301c0
int attr itemStrokeColor 0x7f0301c1
int attr itemStrokeWidth 0x7f0301c2
int attr itemTextAppearance 0x7f0301c3
int attr itemTextAppearanceActive 0x7f0301c4
int attr itemTextAppearanceInactive 0x7f0301c5
int attr itemTextColor 0x7f0301c6
int attr keyPositionType 0x7f0301c7
int attr keyboardIcon 0x7f0301c8
int attr keylines 0x7f0301c9
int attr lStar 0x7f0301ca
int attr labelBehavior 0x7f0301cb
int attr labelStyle 0x7f0301cc
int attr labelVisibilityMode 0x7f0301cd
int attr lastBaselineToBottomHeight 0x7f0301ce
int attr launchSingleTop 0x7f0301cf
int attr layout 0x7f0301d0
int attr layoutDescription 0x7f0301d1
int attr layoutDuringTransition 0x7f0301d2
int attr layoutManager 0x7f0301d3
int attr layout_anchor 0x7f0301d4
int attr layout_anchorGravity 0x7f0301d5
int attr layout_behavior 0x7f0301d6
int attr layout_collapseMode 0x7f0301d7
int attr layout_collapseParallaxMultiplier 0x7f0301d8
int attr layout_constrainedHeight 0x7f0301d9
int attr layout_constrainedWidth 0x7f0301da
int attr layout_constraintBaseline_creator 0x7f0301db
int attr layout_constraintBaseline_toBaselineOf 0x7f0301dc
int attr layout_constraintBottom_creator 0x7f0301dd
int attr layout_constraintBottom_toBottomOf 0x7f0301de
int attr layout_constraintBottom_toTopOf 0x7f0301df
int attr layout_constraintCircle 0x7f0301e0
int attr layout_constraintCircleAngle 0x7f0301e1
int attr layout_constraintCircleRadius 0x7f0301e2
int attr layout_constraintDimensionRatio 0x7f0301e3
int attr layout_constraintEnd_toEndOf 0x7f0301e4
int attr layout_constraintEnd_toStartOf 0x7f0301e5
int attr layout_constraintGuide_begin 0x7f0301e6
int attr layout_constraintGuide_end 0x7f0301e7
int attr layout_constraintGuide_percent 0x7f0301e8
int attr layout_constraintHeight_default 0x7f0301e9
int attr layout_constraintHeight_max 0x7f0301ea
int attr layout_constraintHeight_min 0x7f0301eb
int attr layout_constraintHeight_percent 0x7f0301ec
int attr layout_constraintHorizontal_bias 0x7f0301ed
int attr layout_constraintHorizontal_chainStyle 0x7f0301ee
int attr layout_constraintHorizontal_weight 0x7f0301ef
int attr layout_constraintLeft_creator 0x7f0301f0
int attr layout_constraintLeft_toLeftOf 0x7f0301f1
int attr layout_constraintLeft_toRightOf 0x7f0301f2
int attr layout_constraintRight_creator 0x7f0301f3
int attr layout_constraintRight_toLeftOf 0x7f0301f4
int attr layout_constraintRight_toRightOf 0x7f0301f5
int attr layout_constraintStart_toEndOf 0x7f0301f6
int attr layout_constraintStart_toStartOf 0x7f0301f7
int attr layout_constraintTag 0x7f0301f8
int attr layout_constraintTop_creator 0x7f0301f9
int attr layout_constraintTop_toBottomOf 0x7f0301fa
int attr layout_constraintTop_toTopOf 0x7f0301fb
int attr layout_constraintVertical_bias 0x7f0301fc
int attr layout_constraintVertical_chainStyle 0x7f0301fd
int attr layout_constraintVertical_weight 0x7f0301fe
int attr layout_constraintWidth_default 0x7f0301ff
int attr layout_constraintWidth_max 0x7f030200
int attr layout_constraintWidth_min 0x7f030201
int attr layout_constraintWidth_percent 0x7f030202
int attr layout_dodgeInsetEdges 0x7f030203
int attr layout_editor_absoluteX 0x7f030204
int attr layout_editor_absoluteY 0x7f030205
int attr layout_goneMarginBottom 0x7f030206
int attr layout_goneMarginEnd 0x7f030207
int attr layout_goneMarginLeft 0x7f030208
int attr layout_goneMarginRight 0x7f030209
int attr layout_goneMarginStart 0x7f03020a
int attr layout_goneMarginTop 0x7f03020b
int attr layout_insetEdge 0x7f03020c
int attr layout_keyline 0x7f03020d
int attr layout_optimizationLevel 0x7f03020e
int attr layout_scrollFlags 0x7f03020f
int attr layout_scrollInterpolator 0x7f030210
int attr liftOnScroll 0x7f030211
int attr liftOnScrollTargetViewId 0x7f030212
int attr limitBoundsTo 0x7f030213
int attr lineHeight 0x7f030214
int attr lineSpacing 0x7f030215
int attr linearProgressIndicatorStyle 0x7f030216
int attr listChoiceBackgroundIndicator 0x7f030217
int attr listChoiceIndicatorMultipleAnimated 0x7f030218
int attr listChoiceIndicatorSingleAnimated 0x7f030219
int attr listDividerAlertDialog 0x7f03021a
int attr listItemLayout 0x7f03021b
int attr listLayout 0x7f03021c
int attr listMenuViewStyle 0x7f03021d
int attr listPopupWindowStyle 0x7f03021e
int attr listPreferredItemHeight 0x7f03021f
int attr listPreferredItemHeightLarge 0x7f030220
int attr listPreferredItemHeightSmall 0x7f030221
int attr listPreferredItemPaddingEnd 0x7f030222
int attr listPreferredItemPaddingLeft 0x7f030223
int attr listPreferredItemPaddingRight 0x7f030224
int attr listPreferredItemPaddingStart 0x7f030225
int attr logo 0x7f030226
int attr logoDescription 0x7f030227
int attr materialAlertDialogBodyTextStyle 0x7f030228
int attr materialAlertDialogTheme 0x7f030229
int attr materialAlertDialogTitleIconStyle 0x7f03022a
int attr materialAlertDialogTitlePanelStyle 0x7f03022b
int attr materialAlertDialogTitleTextStyle 0x7f03022c
int attr materialButtonOutlinedStyle 0x7f03022d
int attr materialButtonStyle 0x7f03022e
int attr materialButtonToggleGroupStyle 0x7f03022f
int attr materialCalendarDay 0x7f030230
int attr materialCalendarFullscreenTheme 0x7f030231
int attr materialCalendarHeaderCancelButton 0x7f030232
int attr materialCalendarHeaderConfirmButton 0x7f030233
int attr materialCalendarHeaderDivider 0x7f030234
int attr materialCalendarHeaderLayout 0x7f030235
int attr materialCalendarHeaderSelection 0x7f030236
int attr materialCalendarHeaderTitle 0x7f030237
int attr materialCalendarHeaderToggleButton 0x7f030238
int attr materialCalendarMonth 0x7f030239
int attr materialCalendarMonthNavigationButton 0x7f03023a
int attr materialCalendarStyle 0x7f03023b
int attr materialCalendarTheme 0x7f03023c
int attr materialCalendarYearNavigationButton 0x7f03023d
int attr materialCardViewStyle 0x7f03023e
int attr materialCircleRadius 0x7f03023f
int attr materialClockStyle 0x7f030240
int attr materialThemeOverlay 0x7f030241
int attr materialTimePickerStyle 0x7f030242
int attr materialTimePickerTheme 0x7f030243
int attr maxAcceleration 0x7f030244
int attr maxActionInlineWidth 0x7f030245
int attr maxButtonHeight 0x7f030246
int attr maxCharacterCount 0x7f030247
int attr maxHeight 0x7f030248
int attr maxImageSize 0x7f030249
int attr maxLines 0x7f03024a
int attr maxVelocity 0x7f03024b
int attr maxWidth 0x7f03024c
int attr measureWithLargestChild 0x7f03024d
int attr menu 0x7f03024e
int attr menuGravity 0x7f03024f
int attr mimeType 0x7f030250
int attr minHeight 0x7f030251
int attr minHideDelay 0x7f030252
int attr minSeparation 0x7f030253
int attr minTouchTargetSize 0x7f030254
int attr minWidth 0x7f030255
int attr mock_diagonalsColor 0x7f030256
int attr mock_label 0x7f030257
int attr mock_labelBackgroundColor 0x7f030258
int attr mock_labelColor 0x7f030259
int attr mock_showDiagonals 0x7f03025a
int attr mock_showLabel 0x7f03025b
int attr motionDebug 0x7f03025c
int attr motionDurationLong1 0x7f03025d
int attr motionDurationLong2 0x7f03025e
int attr motionDurationMedium1 0x7f03025f
int attr motionDurationMedium2 0x7f030260
int attr motionDurationShort1 0x7f030261
int attr motionDurationShort2 0x7f030262
int attr motionEasingAccelerated 0x7f030263
int attr motionEasingDecelerated 0x7f030264
int attr motionEasingEmphasized 0x7f030265
int attr motionEasingLinear 0x7f030266
int attr motionEasingStandard 0x7f030267
int attr motionInterpolator 0x7f030268
int attr motionPath 0x7f030269
int attr motionPathRotate 0x7f03026a
int attr motionProgress 0x7f03026b
int attr motionStagger 0x7f03026c
int attr motionTarget 0x7f03026d
int attr motion_postLayoutCollision 0x7f03026e
int attr motion_triggerOnCollision 0x7f03026f
int attr moveWhenScrollAtTop 0x7f030270
int attr multiChoiceItemLayout 0x7f030271
int attr navGraph 0x7f030272
int attr navigationContentDescription 0x7f030273
int attr navigationIcon 0x7f030274
int attr navigationIconTint 0x7f030275
int attr navigationMode 0x7f030276
int attr navigationRailStyle 0x7f030277
int attr navigationViewStyle 0x7f030278
int attr nestedScrollFlags 0x7f030279
int attr nestedScrollViewStyle 0x7f03027a
int attr nestedScrollable 0x7f03027b
int attr nullable 0x7f03027c
int attr number 0x7f03027d
int attr numericModifiers 0x7f03027e
int attr onCross 0x7f03027f
int attr onHide 0x7f030280
int attr onNegativeCross 0x7f030281
int attr onPositiveCross 0x7f030282
int attr onShow 0x7f030283
int attr onTouchUp 0x7f030284
int attr overlapAnchor 0x7f030285
int attr overlay 0x7f030286
int attr paddingBottomNoButtons 0x7f030287
int attr paddingBottomSystemWindowInsets 0x7f030288
int attr paddingEnd 0x7f030289
int attr paddingLeftSystemWindowInsets 0x7f03028a
int attr paddingRightSystemWindowInsets 0x7f03028b
int attr paddingStart 0x7f03028c
int attr paddingTopNoTitle 0x7f03028d
int attr paddingTopSystemWindowInsets 0x7f03028e
int attr panelBackground 0x7f03028f
int attr panelMenuListTheme 0x7f030290
int attr panelMenuListWidth 0x7f030291
int attr passwordToggleContentDescription 0x7f030292
int attr passwordToggleDrawable 0x7f030293
int attr passwordToggleEnabled 0x7f030294
int attr passwordToggleTint 0x7f030295
int attr passwordToggleTintMode 0x7f030296
int attr pathMotionArc 0x7f030297
int attr path_percent 0x7f030298
int attr percentHeight 0x7f030299
int attr percentWidth 0x7f03029a
int attr percentX 0x7f03029b
int attr percentY 0x7f03029c
int attr perpendicularPath_percent 0x7f03029d
int attr pivotAnchor 0x7f03029e
int attr placeholderActivityName 0x7f03029f
int attr placeholderText 0x7f0302a0
int attr placeholderTextAppearance 0x7f0302a1
int attr placeholderTextColor 0x7f0302a2
int attr placeholder_emptyVisibility 0x7f0302a3
int attr popEnterAnim 0x7f0302a4
int attr popExitAnim 0x7f0302a5
int attr popUpTo 0x7f0302a6
int attr popUpToInclusive 0x7f0302a7
int attr popUpToSaveState 0x7f0302a8
int attr popupMenuBackground 0x7f0302a9
int attr popupMenuStyle 0x7f0302aa
int attr popupTheme 0x7f0302ab
int attr popupWindowStyle 0x7f0302ac
int attr prefixText 0x7f0302ad
int attr prefixTextAppearance 0x7f0302ae
int attr prefixTextColor 0x7f0302af
int attr preserveIconSpacing 0x7f0302b0
int attr pressedTranslationZ 0x7f0302b1
int attr primaryActivityName 0x7f0302b2
int attr progressBarPadding 0x7f0302b3
int attr progressBarStyle 0x7f0302b4
int attr queryBackground 0x7f0302b5
int attr queryHint 0x7f0302b6
int attr queryPatterns 0x7f0302b7
int attr radioButtonStyle 0x7f0302b8
int attr rangeFillColor 0x7f0302b9
int attr ratingBarStyle 0x7f0302ba
int attr ratingBarStyleIndicator 0x7f0302bb
int attr ratingBarStyleSmall 0x7f0302bc
int attr recyclerViewStyle 0x7f0302bd
int attr region_heightLessThan 0x7f0302be
int attr region_heightMoreThan 0x7f0302bf
int attr region_widthLessThan 0x7f0302c0
int attr region_widthMoreThan 0x7f0302c1
int attr restoreState 0x7f0302c2
int attr reverseLayout 0x7f0302c3
int attr rippleColor 0x7f0302c4
int attr round 0x7f0302c5
int attr roundPercent 0x7f0302c6
int attr route 0x7f0302c7
int attr saturation 0x7f0302c8
int attr scrimAnimationDuration 0x7f0302c9
int attr scrimBackground 0x7f0302ca
int attr scrimVisibleHeightTrigger 0x7f0302cb
int attr searchHintIcon 0x7f0302cc
int attr searchIcon 0x7f0302cd
int attr searchViewStyle 0x7f0302ce
int attr secondaryActivityAction 0x7f0302cf
int attr secondaryActivityName 0x7f0302d0
int attr seekBarStyle 0x7f0302d1
int attr selectableItemBackground 0x7f0302d2
int attr selectableItemBackgroundBorderless 0x7f0302d3
int attr selectionRequired 0x7f0302d4
int attr selectorSize 0x7f0302d5
int attr shapeAppearance 0x7f0302d6
int attr shapeAppearanceLargeComponent 0x7f0302d7
int attr shapeAppearanceMediumComponent 0x7f0302d8
int attr shapeAppearanceOverlay 0x7f0302d9
int attr shapeAppearanceSmallComponent 0x7f0302da
int attr shortcutMatchRequired 0x7f0302db
int attr showAnimationBehavior 0x7f0302dc
int attr showAsAction 0x7f0302dd
int attr showDelay 0x7f0302de
int attr showDividers 0x7f0302df
int attr showMotionSpec 0x7f0302e0
int attr showPaths 0x7f0302e1
int attr showText 0x7f0302e2
int attr showTitle 0x7f0302e3
int attr shrinkMotionSpec 0x7f0302e4
int attr singleChoiceItemLayout 0x7f0302e5
int attr singleLine 0x7f0302e6
int attr singleSelection 0x7f0302e7
int attr sizePercent 0x7f0302e8
int attr sliderStyle 0x7f0302e9
int attr snackbarButtonStyle 0x7f0302ea
int attr snackbarStyle 0x7f0302eb
int attr snackbarTextViewStyle 0x7f0302ec
int attr spanCount 0x7f0302ed
int attr spinBars 0x7f0302ee
int attr spinnerDropDownItemStyle 0x7f0302ef
int attr spinnerStyle 0x7f0302f0
int attr splitLayoutDirection 0x7f0302f1
int attr splitMinSmallestWidth 0x7f0302f2
int attr splitMinWidth 0x7f0302f3
int attr splitRatio 0x7f0302f4
int attr splitTrack 0x7f0302f5
int attr srcCompat 0x7f0302f6
int attr stackFromEnd 0x7f0302f7
int attr staggered 0x7f0302f8
int attr startDestination 0x7f0302f9
int attr startIconCheckable 0x7f0302fa
int attr startIconContentDescription 0x7f0302fb
int attr startIconDrawable 0x7f0302fc
int attr startIconTint 0x7f0302fd
int attr startIconTintMode 0x7f0302fe
int attr state_above_anchor 0x7f0302ff
int attr state_collapsed 0x7f030300
int attr state_collapsible 0x7f030301
int attr state_dragged 0x7f030302
int attr state_liftable 0x7f030303
int attr state_lifted 0x7f030304
int attr statusBarBackground 0x7f030305
int attr statusBarForeground 0x7f030306
int attr statusBarScrim 0x7f030307
int attr strokeColor 0x7f030308
int attr strokeWidth 0x7f030309
int attr subMenuArrow 0x7f03030a
int attr submitBackground 0x7f03030b
int attr subtitle 0x7f03030c
int attr subtitleCentered 0x7f03030d
int attr subtitleTextAppearance 0x7f03030e
int attr subtitleTextColor 0x7f03030f
int attr subtitleTextStyle 0x7f030310
int attr suffixText 0x7f030311
int attr suffixTextAppearance 0x7f030312
int attr suffixTextColor 0x7f030313
int attr suggestionRowLayout 0x7f030314
int attr switchMinWidth 0x7f030315
int attr switchPadding 0x7f030316
int attr switchStyle 0x7f030317
int attr switchTextAppearance 0x7f030318
int attr tabBackground 0x7f030319
int attr tabContentStart 0x7f03031a
int attr tabGravity 0x7f03031b
int attr tabIconTint 0x7f03031c
int attr tabIconTintMode 0x7f03031d
int attr tabIndicator 0x7f03031e
int attr tabIndicatorAnimationDuration 0x7f03031f
int attr tabIndicatorAnimationMode 0x7f030320
int attr tabIndicatorColor 0x7f030321
int attr tabIndicatorFullWidth 0x7f030322
int attr tabIndicatorGravity 0x7f030323
int attr tabIndicatorHeight 0x7f030324
int attr tabInlineLabel 0x7f030325
int attr tabMaxWidth 0x7f030326
int attr tabMinWidth 0x7f030327
int attr tabMode 0x7f030328
int attr tabPadding 0x7f030329
int attr tabPaddingBottom 0x7f03032a
int attr tabPaddingEnd 0x7f03032b
int attr tabPaddingStart 0x7f03032c
int attr tabPaddingTop 0x7f03032d
int attr tabRippleColor 0x7f03032e
int attr tabSelectedTextColor 0x7f03032f
int attr tabStyle 0x7f030330
int attr tabTextAppearance 0x7f030331
int attr tabTextColor 0x7f030332
int attr tabUnboundedRipple 0x7f030333
int attr targetId 0x7f030334
int attr targetPackage 0x7f030335
int attr telltales_tailColor 0x7f030336
int attr telltales_tailScale 0x7f030337
int attr telltales_velocityMode 0x7f030338
int attr textAllCaps 0x7f030339
int attr textAppearanceBody1 0x7f03033a
int attr textAppearanceBody2 0x7f03033b
int attr textAppearanceButton 0x7f03033c
int attr textAppearanceCaption 0x7f03033d
int attr textAppearanceHeadline1 0x7f03033e
int attr textAppearanceHeadline2 0x7f03033f
int attr textAppearanceHeadline3 0x7f030340
int attr textAppearanceHeadline4 0x7f030341
int attr textAppearanceHeadline5 0x7f030342
int attr textAppearanceHeadline6 0x7f030343
int attr textAppearanceLargePopupMenu 0x7f030344
int attr textAppearanceLineHeightEnabled 0x7f030345
int attr textAppearanceListItem 0x7f030346
int attr textAppearanceListItemSecondary 0x7f030347
int attr textAppearanceListItemSmall 0x7f030348
int attr textAppearanceOverline 0x7f030349
int attr textAppearancePopupMenuHeader 0x7f03034a
int attr textAppearanceSearchResultSubtitle 0x7f03034b
int attr textAppearanceSearchResultTitle 0x7f03034c
int attr textAppearanceSmallPopupMenu 0x7f03034d
int attr textAppearanceSubtitle1 0x7f03034e
int attr textAppearanceSubtitle2 0x7f03034f
int attr textColorAlertDialogListItem 0x7f030350
int attr textColorSearchUrl 0x7f030351
int attr textEndPadding 0x7f030352
int attr textInputLayoutFocusedRectEnabled 0x7f030353
int attr textInputStyle 0x7f030354
int attr textLocale 0x7f030355
int attr textStartPadding 0x7f030356
int attr theme 0x7f030357
int attr themeLineHeight 0x7f030358
int attr thickness 0x7f030359
int attr thumbColor 0x7f03035a
int attr thumbElevation 0x7f03035b
int attr thumbRadius 0x7f03035c
int attr thumbStrokeColor 0x7f03035d
int attr thumbStrokeWidth 0x7f03035e
int attr thumbTextPadding 0x7f03035f
int attr thumbTint 0x7f030360
int attr thumbTintMode 0x7f030361
int attr tickColor 0x7f030362
int attr tickColorActive 0x7f030363
int attr tickColorInactive 0x7f030364
int attr tickMark 0x7f030365
int attr tickMarkTint 0x7f030366
int attr tickMarkTintMode 0x7f030367
int attr tickVisible 0x7f030368
int attr tint 0x7f030369
int attr tintMode 0x7f03036a
int attr title 0x7f03036b
int attr titleCentered 0x7f03036c
int attr titleCollapseMode 0x7f03036d
int attr titleEnabled 0x7f03036e
int attr titleMargin 0x7f03036f
int attr titleMarginBottom 0x7f030370
int attr titleMarginEnd 0x7f030371
int attr titleMarginStart 0x7f030372
int attr titleMarginTop 0x7f030373
int attr titleMargins 0x7f030374
int attr titleTextAppearance 0x7f030375
int attr titleTextColor 0x7f030376
int attr titleTextStyle 0x7f030377
int attr toolbarId 0x7f030378
int attr toolbarNavigationButtonStyle 0x7f030379
int attr toolbarStyle 0x7f03037a
int attr tooltipForegroundColor 0x7f03037b
int attr tooltipFrameBackground 0x7f03037c
int attr tooltipStyle 0x7f03037d
int attr tooltipText 0x7f03037e
int attr touchAnchorId 0x7f03037f
int attr touchAnchorSide 0x7f030380
int attr touchRegionId 0x7f030381
int attr track 0x7f030382
int attr trackColor 0x7f030383
int attr trackColorActive 0x7f030384
int attr trackColorInactive 0x7f030385
int attr trackCornerRadius 0x7f030386
int attr trackHeight 0x7f030387
int attr trackThickness 0x7f030388
int attr trackTint 0x7f030389
int attr trackTintMode 0x7f03038a
int attr transitionDisable 0x7f03038b
int attr transitionEasing 0x7f03038c
int attr transitionFlags 0x7f03038d
int attr transitionPathRotate 0x7f03038e
int attr transitionShapeAppearance 0x7f03038f
int attr triggerId 0x7f030390
int attr triggerReceiver 0x7f030391
int attr triggerSlack 0x7f030392
int attr ttcIndex 0x7f030393
int attr uri 0x7f030394
int attr useCompatPadding 0x7f030395
int attr useMaterialThemeColors 0x7f030396
int attr values 0x7f030397
int attr verticalOffset 0x7f030398
int attr viewInflaterClass 0x7f030399
int attr visibilityMode 0x7f03039a
int attr voiceIcon 0x7f03039b
int attr warmth 0x7f03039c
int attr waveDecay 0x7f03039d
int attr waveOffset 0x7f03039e
int attr wavePeriod 0x7f03039f
int attr waveShape 0x7f0303a0
int attr waveVariesBy 0x7f0303a1
int attr windowActionBar 0x7f0303a2
int attr windowActionBarOverlay 0x7f0303a3
int attr windowActionModeOverlay 0x7f0303a4
int attr windowFixedHeightMajor 0x7f0303a5
int attr windowFixedHeightMinor 0x7f0303a6
int attr windowFixedWidthMajor 0x7f0303a7
int attr windowFixedWidthMinor 0x7f0303a8
int attr windowMinWidthMajor 0x7f0303a9
int attr windowMinWidthMinor 0x7f0303aa
int attr windowNoTitle 0x7f0303ab
int attr yearSelectedStyle 0x7f0303ac
int attr yearStyle 0x7f0303ad
int attr yearTodayStyle 0x7f0303ae
int bool abc_action_bar_embed_tabs 0x7f040000
int bool abc_allow_stacked_button_bar 0x7f040001
int bool abc_config_actionMenuItemAllCaps 0x7f040002
int bool enable_system_alarm_service_default 0x7f040003
int bool enable_system_foreground_service_default 0x7f040004
int bool enable_system_job_service_default 0x7f040005
int bool mtrl_btn_textappearance_all_caps 0x7f040006
int bool workmanager_test_configuration 0x7f040007
int color abc_background_cache_hint_selector_material_dark 0x7f050000
int color abc_background_cache_hint_selector_material_light 0x7f050001
int color abc_btn_colored_borderless_text_material 0x7f050002
int color abc_btn_colored_text_material 0x7f050003
int color abc_color_highlight_material 0x7f050004
int color abc_decor_view_status_guard 0x7f050005
int color abc_decor_view_status_guard_light 0x7f050006
int color abc_hint_foreground_material_dark 0x7f050007
int color abc_hint_foreground_material_light 0x7f050008
int color abc_primary_text_disable_only_material_dark 0x7f050009
int color abc_primary_text_disable_only_material_light 0x7f05000a
int color abc_primary_text_material_dark 0x7f05000b
int color abc_primary_text_material_light 0x7f05000c
int color abc_search_url_text 0x7f05000d
int color abc_search_url_text_normal 0x7f05000e
int color abc_search_url_text_pressed 0x7f05000f
int color abc_search_url_text_selected 0x7f050010
int color abc_secondary_text_material_dark 0x7f050011
int color abc_secondary_text_material_light 0x7f050012
int color abc_tint_btn_checkable 0x7f050013
int color abc_tint_default 0x7f050014
int color abc_tint_edittext 0x7f050015
int color abc_tint_seek_thumb 0x7f050016
int color abc_tint_spinner 0x7f050017
int color abc_tint_switch_track 0x7f050018
int color accent_material_dark 0x7f050019
int color accent_material_light 0x7f05001a
int color androidx_core_ripple_material_light 0x7f05001b
int color androidx_core_secondary_text_default_material_light 0x7f05001c
int color background_floating_material_dark 0x7f05001d
int color background_floating_material_light 0x7f05001e
int color background_material_dark 0x7f05001f
int color background_material_light 0x7f050020
int color black 0x7f050021
int color bright_foreground_disabled_material_dark 0x7f050022
int color bright_foreground_disabled_material_light 0x7f050023
int color bright_foreground_inverse_material_dark 0x7f050024
int color bright_foreground_inverse_material_light 0x7f050025
int color bright_foreground_material_dark 0x7f050026
int color bright_foreground_material_light 0x7f050027
int color button_material_dark 0x7f050028
int color button_material_light 0x7f050029
int color call_notification_answer_color 0x7f05002a
int color call_notification_decline_color 0x7f05002b
int color cardview_dark_background 0x7f05002c
int color cardview_light_background 0x7f05002d
int color cardview_shadow_end_color 0x7f05002e
int color cardview_shadow_start_color 0x7f05002f
int color checkbox_themeable_attribute_color 0x7f050030
int color design_bottom_navigation_shadow_color 0x7f050031
int color design_box_stroke_color 0x7f050032
int color design_dark_default_color_background 0x7f050033
int color design_dark_default_color_error 0x7f050034
int color design_dark_default_color_on_background 0x7f050035
int color design_dark_default_color_on_error 0x7f050036
int color design_dark_default_color_on_primary 0x7f050037
int color design_dark_default_color_on_secondary 0x7f050038
int color design_dark_default_color_on_surface 0x7f050039
int color design_dark_default_color_primary 0x7f05003a
int color design_dark_default_color_primary_dark 0x7f05003b
int color design_dark_default_color_primary_variant 0x7f05003c
int color design_dark_default_color_secondary 0x7f05003d
int color design_dark_default_color_secondary_variant 0x7f05003e
int color design_dark_default_color_surface 0x7f05003f
int color design_default_color_background 0x7f050040
int color design_default_color_error 0x7f050041
int color design_default_color_on_background 0x7f050042
int color design_default_color_on_error 0x7f050043
int color design_default_color_on_primary 0x7f050044
int color design_default_color_on_secondary 0x7f050045
int color design_default_color_on_surface 0x7f050046
int color design_default_color_primary 0x7f050047
int color design_default_color_primary_dark 0x7f050048
int color design_default_color_primary_variant 0x7f050049
int color design_default_color_secondary 0x7f05004a
int color design_default_color_secondary_variant 0x7f05004b
int color design_default_color_surface 0x7f05004c
int color design_error 0x7f05004d
int color design_fab_shadow_end_color 0x7f05004e
int color design_fab_shadow_mid_color 0x7f05004f
int color design_fab_shadow_start_color 0x7f050050
int color design_fab_stroke_end_inner_color 0x7f050051
int color design_fab_stroke_end_outer_color 0x7f050052
int color design_fab_stroke_top_inner_color 0x7f050053
int color design_fab_stroke_top_outer_color 0x7f050054
int color design_icon_tint 0x7f050055
int color design_snackbar_background_color 0x7f050056
int color dim_foreground_disabled_material_dark 0x7f050057
int color dim_foreground_disabled_material_light 0x7f050058
int color dim_foreground_material_dark 0x7f050059
int color dim_foreground_material_light 0x7f05005a
int color error_color_material_dark 0x7f05005b
int color error_color_material_light 0x7f05005c
int color foreground_material_dark 0x7f05005d
int color foreground_material_light 0x7f05005e
int color highlighted_text_material_dark 0x7f05005f
int color highlighted_text_material_light 0x7f050060
int color material_blue_grey_800 0x7f050061
int color material_blue_grey_900 0x7f050062
int color material_blue_grey_950 0x7f050063
int color material_cursor_color 0x7f050064
int color material_deep_teal_200 0x7f050065
int color material_deep_teal_500 0x7f050066
int color material_grey_100 0x7f050067
int color material_grey_300 0x7f050068
int color material_grey_50 0x7f050069
int color material_grey_600 0x7f05006a
int color material_grey_800 0x7f05006b
int color material_grey_850 0x7f05006c
int color material_grey_900 0x7f05006d
int color material_on_background_disabled 0x7f05006e
int color material_on_background_emphasis_high_type 0x7f05006f
int color material_on_background_emphasis_medium 0x7f050070
int color material_on_primary_disabled 0x7f050071
int color material_on_primary_emphasis_high_type 0x7f050072
int color material_on_primary_emphasis_medium 0x7f050073
int color material_on_surface_disabled 0x7f050074
int color material_on_surface_emphasis_high_type 0x7f050075
int color material_on_surface_emphasis_medium 0x7f050076
int color material_on_surface_stroke 0x7f050077
int color material_slider_active_tick_marks_color 0x7f050078
int color material_slider_active_track_color 0x7f050079
int color material_slider_halo_color 0x7f05007a
int color material_slider_inactive_tick_marks_color 0x7f05007b
int color material_slider_inactive_track_color 0x7f05007c
int color material_slider_thumb_color 0x7f05007d
int color material_timepicker_button_background 0x7f05007e
int color material_timepicker_button_stroke 0x7f05007f
int color material_timepicker_clock_text_color 0x7f050080
int color material_timepicker_clockface 0x7f050081
int color material_timepicker_modebutton_tint 0x7f050082
int color mtrl_btn_bg_color_selector 0x7f050083
int color mtrl_btn_ripple_color 0x7f050084
int color mtrl_btn_stroke_color_selector 0x7f050085
int color mtrl_btn_text_btn_bg_color_selector 0x7f050086
int color mtrl_btn_text_btn_ripple_color 0x7f050087
int color mtrl_btn_text_color_disabled 0x7f050088
int color mtrl_btn_text_color_selector 0x7f050089
int color mtrl_btn_transparent_bg_color 0x7f05008a
int color mtrl_calendar_item_stroke_color 0x7f05008b
int color mtrl_calendar_selected_range 0x7f05008c
int color mtrl_card_view_foreground 0x7f05008d
int color mtrl_card_view_ripple 0x7f05008e
int color mtrl_chip_background_color 0x7f05008f
int color mtrl_chip_close_icon_tint 0x7f050090
int color mtrl_chip_surface_color 0x7f050091
int color mtrl_chip_text_color 0x7f050092
int color mtrl_choice_chip_background_color 0x7f050093
int color mtrl_choice_chip_ripple_color 0x7f050094
int color mtrl_choice_chip_text_color 0x7f050095
int color mtrl_error 0x7f050096
int color mtrl_fab_bg_color_selector 0x7f050097
int color mtrl_fab_icon_text_color_selector 0x7f050098
int color mtrl_fab_ripple_color 0x7f050099
int color mtrl_filled_background_color 0x7f05009a
int color mtrl_filled_icon_tint 0x7f05009b
int color mtrl_filled_stroke_color 0x7f05009c
int color mtrl_indicator_text_color 0x7f05009d
int color mtrl_navigation_bar_colored_item_tint 0x7f05009e
int color mtrl_navigation_bar_colored_ripple_color 0x7f05009f
int color mtrl_navigation_bar_item_tint 0x7f0500a0
int color mtrl_navigation_bar_ripple_color 0x7f0500a1
int color mtrl_navigation_item_background_color 0x7f0500a2
int color mtrl_navigation_item_icon_tint 0x7f0500a3
int color mtrl_navigation_item_text_color 0x7f0500a4
int color mtrl_on_primary_text_btn_text_color_selector 0x7f0500a5
int color mtrl_on_surface_ripple_color 0x7f0500a6
int color mtrl_outlined_icon_tint 0x7f0500a7
int color mtrl_outlined_stroke_color 0x7f0500a8
int color mtrl_popupmenu_overlay_color 0x7f0500a9
int color mtrl_scrim_color 0x7f0500aa
int color mtrl_tabs_colored_ripple_color 0x7f0500ab
int color mtrl_tabs_icon_color_selector 0x7f0500ac
int color mtrl_tabs_icon_color_selector_colored 0x7f0500ad
int color mtrl_tabs_legacy_text_color_selector 0x7f0500ae
int color mtrl_tabs_ripple_color 0x7f0500af
int color mtrl_text_btn_text_color_selector 0x7f0500b0
int color mtrl_textinput_default_box_stroke_color 0x7f0500b1
int color mtrl_textinput_disabled_color 0x7f0500b2
int color mtrl_textinput_filled_box_default_background_color 0x7f0500b3
int color mtrl_textinput_focused_box_stroke_color 0x7f0500b4
int color mtrl_textinput_hovered_box_stroke_color 0x7f0500b5
int color notification_action_color_filter 0x7f0500b6
int color notification_icon_bg_color 0x7f0500b7
int color primary_dark_material_dark 0x7f0500b8
int color primary_dark_material_light 0x7f0500b9
int color primary_material_dark 0x7f0500ba
int color primary_material_light 0x7f0500bb
int color primary_text_default_material_dark 0x7f0500bc
int color primary_text_default_material_light 0x7f0500bd
int color primary_text_disabled_material_dark 0x7f0500be
int color primary_text_disabled_material_light 0x7f0500bf
int color purple_200 0x7f0500c0
int color purple_500 0x7f0500c1
int color purple_700 0x7f0500c2
int color radiobutton_themeable_attribute_color 0x7f0500c3
int color ripple_material_dark 0x7f0500c4
int color ripple_material_light 0x7f0500c5
int color secondary_text_default_material_dark 0x7f0500c6
int color secondary_text_default_material_light 0x7f0500c7
int color secondary_text_disabled_material_dark 0x7f0500c8
int color secondary_text_disabled_material_light 0x7f0500c9
int color status_complete 0x7f0500ca
int color status_di_muon 0x7f0500cb
int color status_du_cong 0x7f0500cc
int color status_incomplete 0x7f0500cd
int color status_info 0x7f0500ce
int color status_neutral 0x7f0500cf
int color status_nghi_benh 0x7f0500d0
int color status_nghi_le 0x7f0500d1
int color status_nghi_phep 0x7f0500d2
int color status_thieu_log 0x7f0500d3
int color status_vang_mat 0x7f0500d4
int color status_ve_som 0x7f0500d5
int color status_warning 0x7f0500d6
int color switch_thumb_disabled_material_dark 0x7f0500d7
int color switch_thumb_disabled_material_light 0x7f0500d8
int color switch_thumb_material_dark 0x7f0500d9
int color switch_thumb_material_light 0x7f0500da
int color switch_thumb_normal_material_dark 0x7f0500db
int color switch_thumb_normal_material_light 0x7f0500dc
int color teal_200 0x7f0500dd
int color teal_700 0x7f0500de
int color test_mtrl_calendar_day 0x7f0500df
int color test_mtrl_calendar_day_selected 0x7f0500e0
int color tooltip_background_dark 0x7f0500e1
int color tooltip_background_light 0x7f0500e2
int color vector_tint_color 0x7f0500e3
int color vector_tint_theme_color 0x7f0500e4
int color weather_caution 0x7f0500e5
int color weather_info 0x7f0500e6
int color weather_warning 0x7f0500e7
int color white 0x7f0500e8
int color workly_primary 0x7f0500e9
int color workly_primary_variant 0x7f0500ea
int color workly_secondary 0x7f0500eb
int color workly_secondary_variant 0x7f0500ec
int dimen abc_action_bar_content_inset_material 0x7f060000
int dimen abc_action_bar_content_inset_with_nav 0x7f060001
int dimen abc_action_bar_default_height_material 0x7f060002
int dimen abc_action_bar_default_padding_end_material 0x7f060003
int dimen abc_action_bar_default_padding_start_material 0x7f060004
int dimen abc_action_bar_elevation_material 0x7f060005
int dimen abc_action_bar_icon_vertical_padding_material 0x7f060006
int dimen abc_action_bar_overflow_padding_end_material 0x7f060007
int dimen abc_action_bar_overflow_padding_start_material 0x7f060008
int dimen abc_action_bar_stacked_max_height 0x7f060009
int dimen abc_action_bar_stacked_tab_max_width 0x7f06000a
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f06000b
int dimen abc_action_bar_subtitle_top_margin_material 0x7f06000c
int dimen abc_action_button_min_height_material 0x7f06000d
int dimen abc_action_button_min_width_material 0x7f06000e
int dimen abc_action_button_min_width_overflow_material 0x7f06000f
int dimen abc_alert_dialog_button_bar_height 0x7f060010
int dimen abc_alert_dialog_button_dimen 0x7f060011
int dimen abc_button_inset_horizontal_material 0x7f060012
int dimen abc_button_inset_vertical_material 0x7f060013
int dimen abc_button_padding_horizontal_material 0x7f060014
int dimen abc_button_padding_vertical_material 0x7f060015
int dimen abc_cascading_menus_min_smallest_width 0x7f060016
int dimen abc_config_prefDialogWidth 0x7f060017
int dimen abc_control_corner_material 0x7f060018
int dimen abc_control_inset_material 0x7f060019
int dimen abc_control_padding_material 0x7f06001a
int dimen abc_dialog_corner_radius_material 0x7f06001b
int dimen abc_dialog_fixed_height_major 0x7f06001c
int dimen abc_dialog_fixed_height_minor 0x7f06001d
int dimen abc_dialog_fixed_width_major 0x7f06001e
int dimen abc_dialog_fixed_width_minor 0x7f06001f
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f060020
int dimen abc_dialog_list_padding_top_no_title 0x7f060021
int dimen abc_dialog_min_width_major 0x7f060022
int dimen abc_dialog_min_width_minor 0x7f060023
int dimen abc_dialog_padding_material 0x7f060024
int dimen abc_dialog_padding_top_material 0x7f060025
int dimen abc_dialog_title_divider_material 0x7f060026
int dimen abc_disabled_alpha_material_dark 0x7f060027
int dimen abc_disabled_alpha_material_light 0x7f060028
int dimen abc_dropdownitem_icon_width 0x7f060029
int dimen abc_dropdownitem_text_padding_left 0x7f06002a
int dimen abc_dropdownitem_text_padding_right 0x7f06002b
int dimen abc_edit_text_inset_bottom_material 0x7f06002c
int dimen abc_edit_text_inset_horizontal_material 0x7f06002d
int dimen abc_edit_text_inset_top_material 0x7f06002e
int dimen abc_floating_window_z 0x7f06002f
int dimen abc_list_item_height_large_material 0x7f060030
int dimen abc_list_item_height_material 0x7f060031
int dimen abc_list_item_height_small_material 0x7f060032
int dimen abc_list_item_padding_horizontal_material 0x7f060033
int dimen abc_panel_menu_list_width 0x7f060034
int dimen abc_progress_bar_height_material 0x7f060035
int dimen abc_search_view_preferred_height 0x7f060036
int dimen abc_search_view_preferred_width 0x7f060037
int dimen abc_seekbar_track_background_height_material 0x7f060038
int dimen abc_seekbar_track_progress_height_material 0x7f060039
int dimen abc_select_dialog_padding_start_material 0x7f06003a
int dimen abc_switch_padding 0x7f06003b
int dimen abc_text_size_body_1_material 0x7f06003c
int dimen abc_text_size_body_2_material 0x7f06003d
int dimen abc_text_size_button_material 0x7f06003e
int dimen abc_text_size_caption_material 0x7f06003f
int dimen abc_text_size_display_1_material 0x7f060040
int dimen abc_text_size_display_2_material 0x7f060041
int dimen abc_text_size_display_3_material 0x7f060042
int dimen abc_text_size_display_4_material 0x7f060043
int dimen abc_text_size_headline_material 0x7f060044
int dimen abc_text_size_large_material 0x7f060045
int dimen abc_text_size_medium_material 0x7f060046
int dimen abc_text_size_menu_header_material 0x7f060047
int dimen abc_text_size_menu_material 0x7f060048
int dimen abc_text_size_small_material 0x7f060049
int dimen abc_text_size_subhead_material 0x7f06004a
int dimen abc_text_size_subtitle_material_toolbar 0x7f06004b
int dimen abc_text_size_title_material 0x7f06004c
int dimen abc_text_size_title_material_toolbar 0x7f06004d
int dimen action_bar_size 0x7f06004e
int dimen appcompat_dialog_background_inset 0x7f06004f
int dimen cardview_compat_inset_shadow 0x7f060050
int dimen cardview_default_elevation 0x7f060051
int dimen cardview_default_radius 0x7f060052
int dimen clock_face_margin_start 0x7f060053
int dimen compat_button_inset_horizontal_material 0x7f060054
int dimen compat_button_inset_vertical_material 0x7f060055
int dimen compat_button_padding_horizontal_material 0x7f060056
int dimen compat_button_padding_vertical_material 0x7f060057
int dimen compat_control_corner_material 0x7f060058
int dimen compat_notification_large_icon_max_height 0x7f060059
int dimen compat_notification_large_icon_max_width 0x7f06005a
int dimen def_drawer_elevation 0x7f06005b
int dimen default_dimension 0x7f06005c
int dimen design_appbar_elevation 0x7f06005d
int dimen design_bottom_navigation_active_item_max_width 0x7f06005e
int dimen design_bottom_navigation_active_item_min_width 0x7f06005f
int dimen design_bottom_navigation_active_text_size 0x7f060060
int dimen design_bottom_navigation_elevation 0x7f060061
int dimen design_bottom_navigation_height 0x7f060062
int dimen design_bottom_navigation_icon_size 0x7f060063
int dimen design_bottom_navigation_item_max_width 0x7f060064
int dimen design_bottom_navigation_item_min_width 0x7f060065
int dimen design_bottom_navigation_label_padding 0x7f060066
int dimen design_bottom_navigation_margin 0x7f060067
int dimen design_bottom_navigation_shadow_height 0x7f060068
int dimen design_bottom_navigation_text_size 0x7f060069
int dimen design_bottom_sheet_elevation 0x7f06006a
int dimen design_bottom_sheet_modal_elevation 0x7f06006b
int dimen design_bottom_sheet_peek_height_min 0x7f06006c
int dimen design_fab_border_width 0x7f06006d
int dimen design_fab_elevation 0x7f06006e
int dimen design_fab_image_size 0x7f06006f
int dimen design_fab_size_mini 0x7f060070
int dimen design_fab_size_normal 0x7f060071
int dimen design_fab_translation_z_hovered_focused 0x7f060072
int dimen design_fab_translation_z_pressed 0x7f060073
int dimen design_navigation_elevation 0x7f060074
int dimen design_navigation_icon_padding 0x7f060075
int dimen design_navigation_icon_size 0x7f060076
int dimen design_navigation_item_horizontal_padding 0x7f060077
int dimen design_navigation_item_icon_padding 0x7f060078
int dimen design_navigation_max_width 0x7f060079
int dimen design_navigation_padding_bottom 0x7f06007a
int dimen design_navigation_separator_vertical_padding 0x7f06007b
int dimen design_snackbar_action_inline_max_width 0x7f06007c
int dimen design_snackbar_action_text_color_alpha 0x7f06007d
int dimen design_snackbar_background_corner_radius 0x7f06007e
int dimen design_snackbar_elevation 0x7f06007f
int dimen design_snackbar_extra_spacing_horizontal 0x7f060080
int dimen design_snackbar_max_width 0x7f060081
int dimen design_snackbar_min_width 0x7f060082
int dimen design_snackbar_padding_horizontal 0x7f060083
int dimen design_snackbar_padding_vertical 0x7f060084
int dimen design_snackbar_padding_vertical_2lines 0x7f060085
int dimen design_snackbar_text_size 0x7f060086
int dimen design_tab_max_width 0x7f060087
int dimen design_tab_scrollable_min_width 0x7f060088
int dimen design_tab_text_size 0x7f060089
int dimen design_tab_text_size_2line 0x7f06008a
int dimen design_textinput_caption_translate_y 0x7f06008b
int dimen disabled_alpha_material_dark 0x7f06008c
int dimen disabled_alpha_material_light 0x7f06008d
int dimen fastscroll_default_thickness 0x7f06008e
int dimen fastscroll_margin 0x7f06008f
int dimen fastscroll_minimum_range 0x7f060090
int dimen highlight_alpha_material_colored 0x7f060091
int dimen highlight_alpha_material_dark 0x7f060092
int dimen highlight_alpha_material_light 0x7f060093
int dimen hint_alpha_material_dark 0x7f060094
int dimen hint_alpha_material_light 0x7f060095
int dimen hint_pressed_alpha_material_dark 0x7f060096
int dimen hint_pressed_alpha_material_light 0x7f060097
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f060098
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f060099
int dimen item_touch_helper_swipe_escape_velocity 0x7f06009a
int dimen material_bottom_sheet_max_width 0x7f06009b
int dimen material_clock_display_padding 0x7f06009c
int dimen material_clock_face_margin_top 0x7f06009d
int dimen material_clock_hand_center_dot_radius 0x7f06009e
int dimen material_clock_hand_padding 0x7f06009f
int dimen material_clock_hand_stroke_width 0x7f0600a0
int dimen material_clock_number_text_size 0x7f0600a1
int dimen material_clock_period_toggle_height 0x7f0600a2
int dimen material_clock_period_toggle_margin_left 0x7f0600a3
int dimen material_clock_period_toggle_width 0x7f0600a4
int dimen material_clock_size 0x7f0600a5
int dimen material_cursor_inset_bottom 0x7f0600a6
int dimen material_cursor_inset_top 0x7f0600a7
int dimen material_cursor_width 0x7f0600a8
int dimen material_emphasis_disabled 0x7f0600a9
int dimen material_emphasis_high_type 0x7f0600aa
int dimen material_emphasis_medium 0x7f0600ab
int dimen material_filled_edittext_font_1_3_padding_bottom 0x7f0600ac
int dimen material_filled_edittext_font_1_3_padding_top 0x7f0600ad
int dimen material_filled_edittext_font_2_0_padding_bottom 0x7f0600ae
int dimen material_filled_edittext_font_2_0_padding_top 0x7f0600af
int dimen material_font_1_3_box_collapsed_padding_top 0x7f0600b0
int dimen material_font_2_0_box_collapsed_padding_top 0x7f0600b1
int dimen material_helper_text_default_padding_top 0x7f0600b2
int dimen material_helper_text_font_1_3_padding_horizontal 0x7f0600b3
int dimen material_helper_text_font_1_3_padding_top 0x7f0600b4
int dimen material_input_text_to_prefix_suffix_padding 0x7f0600b5
int dimen material_text_view_test_line_height 0x7f0600b6
int dimen material_text_view_test_line_height_override 0x7f0600b7
int dimen material_textinput_default_width 0x7f0600b8
int dimen material_textinput_max_width 0x7f0600b9
int dimen material_textinput_min_width 0x7f0600ba
int dimen material_time_picker_minimum_screen_height 0x7f0600bb
int dimen material_time_picker_minimum_screen_width 0x7f0600bc
int dimen material_timepicker_dialog_buttons_margin_top 0x7f0600bd
int dimen mtrl_alert_dialog_background_inset_bottom 0x7f0600be
int dimen mtrl_alert_dialog_background_inset_end 0x7f0600bf
int dimen mtrl_alert_dialog_background_inset_start 0x7f0600c0
int dimen mtrl_alert_dialog_background_inset_top 0x7f0600c1
int dimen mtrl_alert_dialog_picker_background_inset 0x7f0600c2
int dimen mtrl_badge_horizontal_edge_offset 0x7f0600c3
int dimen mtrl_badge_long_text_horizontal_padding 0x7f0600c4
int dimen mtrl_badge_radius 0x7f0600c5
int dimen mtrl_badge_text_horizontal_edge_offset 0x7f0600c6
int dimen mtrl_badge_text_size 0x7f0600c7
int dimen mtrl_badge_toolbar_action_menu_item_horizontal_offset 0x7f0600c8
int dimen mtrl_badge_toolbar_action_menu_item_vertical_offset 0x7f0600c9
int dimen mtrl_badge_with_text_radius 0x7f0600ca
int dimen mtrl_bottomappbar_fabOffsetEndMode 0x7f0600cb
int dimen mtrl_bottomappbar_fab_bottom_margin 0x7f0600cc
int dimen mtrl_bottomappbar_fab_cradle_margin 0x7f0600cd
int dimen mtrl_bottomappbar_fab_cradle_rounded_corner_radius 0x7f0600ce
int dimen mtrl_bottomappbar_fab_cradle_vertical_offset 0x7f0600cf
int dimen mtrl_bottomappbar_height 0x7f0600d0
int dimen mtrl_btn_corner_radius 0x7f0600d1
int dimen mtrl_btn_dialog_btn_min_width 0x7f0600d2
int dimen mtrl_btn_disabled_elevation 0x7f0600d3
int dimen mtrl_btn_disabled_z 0x7f0600d4
int dimen mtrl_btn_elevation 0x7f0600d5
int dimen mtrl_btn_focused_z 0x7f0600d6
int dimen mtrl_btn_hovered_z 0x7f0600d7
int dimen mtrl_btn_icon_btn_padding_left 0x7f0600d8
int dimen mtrl_btn_icon_padding 0x7f0600d9
int dimen mtrl_btn_inset 0x7f0600da
int dimen mtrl_btn_letter_spacing 0x7f0600db
int dimen mtrl_btn_max_width 0x7f0600dc
int dimen mtrl_btn_padding_bottom 0x7f0600dd
int dimen mtrl_btn_padding_left 0x7f0600de
int dimen mtrl_btn_padding_right 0x7f0600df
int dimen mtrl_btn_padding_top 0x7f0600e0
int dimen mtrl_btn_pressed_z 0x7f0600e1
int dimen mtrl_btn_snackbar_margin_horizontal 0x7f0600e2
int dimen mtrl_btn_stroke_size 0x7f0600e3
int dimen mtrl_btn_text_btn_icon_padding 0x7f0600e4
int dimen mtrl_btn_text_btn_padding_left 0x7f0600e5
int dimen mtrl_btn_text_btn_padding_right 0x7f0600e6
int dimen mtrl_btn_text_size 0x7f0600e7
int dimen mtrl_btn_z 0x7f0600e8
int dimen mtrl_calendar_action_confirm_button_min_width 0x7f0600e9
int dimen mtrl_calendar_action_height 0x7f0600ea
int dimen mtrl_calendar_action_padding 0x7f0600eb
int dimen mtrl_calendar_bottom_padding 0x7f0600ec
int dimen mtrl_calendar_content_padding 0x7f0600ed
int dimen mtrl_calendar_day_corner 0x7f0600ee
int dimen mtrl_calendar_day_height 0x7f0600ef
int dimen mtrl_calendar_day_horizontal_padding 0x7f0600f0
int dimen mtrl_calendar_day_today_stroke 0x7f0600f1
int dimen mtrl_calendar_day_vertical_padding 0x7f0600f2
int dimen mtrl_calendar_day_width 0x7f0600f3
int dimen mtrl_calendar_days_of_week_height 0x7f0600f4
int dimen mtrl_calendar_dialog_background_inset 0x7f0600f5
int dimen mtrl_calendar_header_content_padding 0x7f0600f6
int dimen mtrl_calendar_header_content_padding_fullscreen 0x7f0600f7
int dimen mtrl_calendar_header_divider_thickness 0x7f0600f8
int dimen mtrl_calendar_header_height 0x7f0600f9
int dimen mtrl_calendar_header_height_fullscreen 0x7f0600fa
int dimen mtrl_calendar_header_selection_line_height 0x7f0600fb
int dimen mtrl_calendar_header_text_padding 0x7f0600fc
int dimen mtrl_calendar_header_toggle_margin_bottom 0x7f0600fd
int dimen mtrl_calendar_header_toggle_margin_top 0x7f0600fe
int dimen mtrl_calendar_landscape_header_width 0x7f0600ff
int dimen mtrl_calendar_maximum_default_fullscreen_minor_axis 0x7f060100
int dimen mtrl_calendar_month_horizontal_padding 0x7f060101
int dimen mtrl_calendar_month_vertical_padding 0x7f060102
int dimen mtrl_calendar_navigation_bottom_padding 0x7f060103
int dimen mtrl_calendar_navigation_height 0x7f060104
int dimen mtrl_calendar_navigation_top_padding 0x7f060105
int dimen mtrl_calendar_pre_l_text_clip_padding 0x7f060106
int dimen mtrl_calendar_selection_baseline_to_top_fullscreen 0x7f060107
int dimen mtrl_calendar_selection_text_baseline_to_bottom 0x7f060108
int dimen mtrl_calendar_selection_text_baseline_to_bottom_fullscreen 0x7f060109
int dimen mtrl_calendar_selection_text_baseline_to_top 0x7f06010a
int dimen mtrl_calendar_text_input_padding_top 0x7f06010b
int dimen mtrl_calendar_title_baseline_to_top 0x7f06010c
int dimen mtrl_calendar_title_baseline_to_top_fullscreen 0x7f06010d
int dimen mtrl_calendar_year_corner 0x7f06010e
int dimen mtrl_calendar_year_height 0x7f06010f
int dimen mtrl_calendar_year_horizontal_padding 0x7f060110
int dimen mtrl_calendar_year_vertical_padding 0x7f060111
int dimen mtrl_calendar_year_width 0x7f060112
int dimen mtrl_card_checked_icon_margin 0x7f060113
int dimen mtrl_card_checked_icon_size 0x7f060114
int dimen mtrl_card_corner_radius 0x7f060115
int dimen mtrl_card_dragged_z 0x7f060116
int dimen mtrl_card_elevation 0x7f060117
int dimen mtrl_card_spacing 0x7f060118
int dimen mtrl_chip_pressed_translation_z 0x7f060119
int dimen mtrl_chip_text_size 0x7f06011a
int dimen mtrl_edittext_rectangle_top_offset 0x7f06011b
int dimen mtrl_exposed_dropdown_menu_popup_elevation 0x7f06011c
int dimen mtrl_exposed_dropdown_menu_popup_vertical_offset 0x7f06011d
int dimen mtrl_exposed_dropdown_menu_popup_vertical_padding 0x7f06011e
int dimen mtrl_extended_fab_bottom_padding 0x7f06011f
int dimen mtrl_extended_fab_corner_radius 0x7f060120
int dimen mtrl_extended_fab_disabled_elevation 0x7f060121
int dimen mtrl_extended_fab_disabled_translation_z 0x7f060122
int dimen mtrl_extended_fab_elevation 0x7f060123
int dimen mtrl_extended_fab_end_padding 0x7f060124
int dimen mtrl_extended_fab_end_padding_icon 0x7f060125
int dimen mtrl_extended_fab_icon_size 0x7f060126
int dimen mtrl_extended_fab_icon_text_spacing 0x7f060127
int dimen mtrl_extended_fab_min_height 0x7f060128
int dimen mtrl_extended_fab_min_width 0x7f060129
int dimen mtrl_extended_fab_start_padding 0x7f06012a
int dimen mtrl_extended_fab_start_padding_icon 0x7f06012b
int dimen mtrl_extended_fab_top_padding 0x7f06012c
int dimen mtrl_extended_fab_translation_z_base 0x7f06012d
int dimen mtrl_extended_fab_translation_z_hovered_focused 0x7f06012e
int dimen mtrl_extended_fab_translation_z_pressed 0x7f06012f
int dimen mtrl_fab_elevation 0x7f060130
int dimen mtrl_fab_min_touch_target 0x7f060131
int dimen mtrl_fab_translation_z_hovered_focused 0x7f060132
int dimen mtrl_fab_translation_z_pressed 0x7f060133
int dimen mtrl_high_ripple_default_alpha 0x7f060134
int dimen mtrl_high_ripple_focused_alpha 0x7f060135
int dimen mtrl_high_ripple_hovered_alpha 0x7f060136
int dimen mtrl_high_ripple_pressed_alpha 0x7f060137
int dimen mtrl_large_touch_target 0x7f060138
int dimen mtrl_low_ripple_default_alpha 0x7f060139
int dimen mtrl_low_ripple_focused_alpha 0x7f06013a
int dimen mtrl_low_ripple_hovered_alpha 0x7f06013b
int dimen mtrl_low_ripple_pressed_alpha 0x7f06013c
int dimen mtrl_min_touch_target_size 0x7f06013d
int dimen mtrl_navigation_bar_item_default_icon_size 0x7f06013e
int dimen mtrl_navigation_bar_item_default_margin 0x7f06013f
int dimen mtrl_navigation_elevation 0x7f060140
int dimen mtrl_navigation_item_horizontal_padding 0x7f060141
int dimen mtrl_navigation_item_icon_padding 0x7f060142
int dimen mtrl_navigation_item_icon_size 0x7f060143
int dimen mtrl_navigation_item_shape_horizontal_margin 0x7f060144
int dimen mtrl_navigation_item_shape_vertical_margin 0x7f060145
int dimen mtrl_navigation_rail_active_text_size 0x7f060146
int dimen mtrl_navigation_rail_compact_width 0x7f060147
int dimen mtrl_navigation_rail_default_width 0x7f060148
int dimen mtrl_navigation_rail_elevation 0x7f060149
int dimen mtrl_navigation_rail_icon_margin 0x7f06014a
int dimen mtrl_navigation_rail_icon_size 0x7f06014b
int dimen mtrl_navigation_rail_margin 0x7f06014c
int dimen mtrl_navigation_rail_text_bottom_margin 0x7f06014d
int dimen mtrl_navigation_rail_text_size 0x7f06014e
int dimen mtrl_progress_circular_inset 0x7f06014f
int dimen mtrl_progress_circular_inset_extra_small 0x7f060150
int dimen mtrl_progress_circular_inset_medium 0x7f060151
int dimen mtrl_progress_circular_inset_small 0x7f060152
int dimen mtrl_progress_circular_radius 0x7f060153
int dimen mtrl_progress_circular_size 0x7f060154
int dimen mtrl_progress_circular_size_extra_small 0x7f060155
int dimen mtrl_progress_circular_size_medium 0x7f060156
int dimen mtrl_progress_circular_size_small 0x7f060157
int dimen mtrl_progress_circular_track_thickness_extra_small 0x7f060158
int dimen mtrl_progress_circular_track_thickness_medium 0x7f060159
int dimen mtrl_progress_circular_track_thickness_small 0x7f06015a
int dimen mtrl_progress_indicator_full_rounded_corner_radius 0x7f06015b
int dimen mtrl_progress_track_thickness 0x7f06015c
int dimen mtrl_shape_corner_size_large_component 0x7f06015d
int dimen mtrl_shape_corner_size_medium_component 0x7f06015e
int dimen mtrl_shape_corner_size_small_component 0x7f06015f
int dimen mtrl_slider_halo_radius 0x7f060160
int dimen mtrl_slider_label_padding 0x7f060161
int dimen mtrl_slider_label_radius 0x7f060162
int dimen mtrl_slider_label_square_side 0x7f060163
int dimen mtrl_slider_thumb_elevation 0x7f060164
int dimen mtrl_slider_thumb_radius 0x7f060165
int dimen mtrl_slider_track_height 0x7f060166
int dimen mtrl_slider_track_side_padding 0x7f060167
int dimen mtrl_slider_track_top 0x7f060168
int dimen mtrl_slider_widget_height 0x7f060169
int dimen mtrl_snackbar_action_text_color_alpha 0x7f06016a
int dimen mtrl_snackbar_background_corner_radius 0x7f06016b
int dimen mtrl_snackbar_background_overlay_color_alpha 0x7f06016c
int dimen mtrl_snackbar_margin 0x7f06016d
int dimen mtrl_snackbar_message_margin_horizontal 0x7f06016e
int dimen mtrl_snackbar_padding_horizontal 0x7f06016f
int dimen mtrl_switch_thumb_elevation 0x7f060170
int dimen mtrl_textinput_box_corner_radius_medium 0x7f060171
int dimen mtrl_textinput_box_corner_radius_small 0x7f060172
int dimen mtrl_textinput_box_label_cutout_padding 0x7f060173
int dimen mtrl_textinput_box_stroke_width_default 0x7f060174
int dimen mtrl_textinput_box_stroke_width_focused 0x7f060175
int dimen mtrl_textinput_counter_margin_start 0x7f060176
int dimen mtrl_textinput_end_icon_margin_start 0x7f060177
int dimen mtrl_textinput_outline_box_expanded_padding 0x7f060178
int dimen mtrl_textinput_start_icon_margin_end 0x7f060179
int dimen mtrl_toolbar_default_height 0x7f06017a
int dimen mtrl_tooltip_arrowSize 0x7f06017b
int dimen mtrl_tooltip_cornerSize 0x7f06017c
int dimen mtrl_tooltip_minHeight 0x7f06017d
int dimen mtrl_tooltip_minWidth 0x7f06017e
int dimen mtrl_tooltip_padding 0x7f06017f
int dimen mtrl_transition_shared_axis_slide_distance 0x7f060180
int dimen notification_action_icon_size 0x7f060181
int dimen notification_action_text_size 0x7f060182
int dimen notification_big_circle_margin 0x7f060183
int dimen notification_content_margin_start 0x7f060184
int dimen notification_large_icon_height 0x7f060185
int dimen notification_large_icon_width 0x7f060186
int dimen notification_main_column_padding_top 0x7f060187
int dimen notification_media_narrow_margin 0x7f060188
int dimen notification_right_icon_size 0x7f060189
int dimen notification_right_side_padding_top 0x7f06018a
int dimen notification_small_icon_background_padding 0x7f06018b
int dimen notification_small_icon_size_as_large 0x7f06018c
int dimen notification_subtext_size 0x7f06018d
int dimen notification_top_pad 0x7f06018e
int dimen notification_top_pad_large_text 0x7f06018f
int dimen sliding_pane_detail_pane_width 0x7f060190
int dimen test_mtrl_calendar_day_cornerSize 0x7f060191
int dimen test_navigation_bar_active_item_max_width 0x7f060192
int dimen test_navigation_bar_active_item_min_width 0x7f060193
int dimen test_navigation_bar_active_text_size 0x7f060194
int dimen test_navigation_bar_elevation 0x7f060195
int dimen test_navigation_bar_height 0x7f060196
int dimen test_navigation_bar_icon_size 0x7f060197
int dimen test_navigation_bar_item_max_width 0x7f060198
int dimen test_navigation_bar_item_min_width 0x7f060199
int dimen test_navigation_bar_label_padding 0x7f06019a
int dimen test_navigation_bar_shadow_height 0x7f06019b
int dimen test_navigation_bar_text_size 0x7f06019c
int dimen tooltip_corner_radius 0x7f06019d
int dimen tooltip_horizontal_padding 0x7f06019e
int dimen tooltip_margin 0x7f06019f
int dimen tooltip_precise_anchor_extra_offset 0x7f0601a0
int dimen tooltip_precise_anchor_threshold 0x7f0601a1
int dimen tooltip_vertical_padding 0x7f0601a2
int dimen tooltip_y_offset_non_touch 0x7f0601a3
int dimen tooltip_y_offset_touch 0x7f0601a4
int drawable abc_ab_share_pack_mtrl_alpha 0x7f070006
int drawable abc_action_bar_item_background_material 0x7f070007
int drawable abc_btn_borderless_material 0x7f070008
int drawable abc_btn_check_material 0x7f070009
int drawable abc_btn_check_material_anim 0x7f07000a
int drawable abc_btn_check_to_on_mtrl_000 0x7f07000b
int drawable abc_btn_check_to_on_mtrl_015 0x7f07000c
int drawable abc_btn_colored_material 0x7f07000d
int drawable abc_btn_default_mtrl_shape 0x7f07000e
int drawable abc_btn_radio_material 0x7f07000f
int drawable abc_btn_radio_material_anim 0x7f070010
int drawable abc_btn_radio_to_on_mtrl_000 0x7f070011
int drawable abc_btn_radio_to_on_mtrl_015 0x7f070012
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f070013
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f070014
int drawable abc_cab_background_internal_bg 0x7f070015
int drawable abc_cab_background_top_material 0x7f070016
int drawable abc_cab_background_top_mtrl_alpha 0x7f070017
int drawable abc_control_background_material 0x7f070018
int drawable abc_dialog_material_background 0x7f070019
int drawable abc_edit_text_material 0x7f07001a
int drawable abc_ic_ab_back_material 0x7f07001b
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f07001c
int drawable abc_ic_clear_material 0x7f07001d
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f07001e
int drawable abc_ic_go_search_api_material 0x7f07001f
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f070020
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f070021
int drawable abc_ic_menu_overflow_material 0x7f070022
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f070023
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f070024
int drawable abc_ic_menu_share_mtrl_alpha 0x7f070025
int drawable abc_ic_search_api_material 0x7f070026
int drawable abc_ic_star_black_16dp 0x7f070027
int drawable abc_ic_star_black_36dp 0x7f070028
int drawable abc_ic_star_black_48dp 0x7f070029
int drawable abc_ic_star_half_black_16dp 0x7f07002a
int drawable abc_ic_star_half_black_36dp 0x7f07002b
int drawable abc_ic_star_half_black_48dp 0x7f07002c
int drawable abc_ic_voice_search_api_material 0x7f07002d
int drawable abc_item_background_holo_dark 0x7f07002e
int drawable abc_item_background_holo_light 0x7f07002f
int drawable abc_list_divider_material 0x7f070030
int drawable abc_list_divider_mtrl_alpha 0x7f070031
int drawable abc_list_focused_holo 0x7f070032
int drawable abc_list_longpressed_holo 0x7f070033
int drawable abc_list_pressed_holo_dark 0x7f070034
int drawable abc_list_pressed_holo_light 0x7f070035
int drawable abc_list_selector_background_transition_holo_dark 0x7f070036
int drawable abc_list_selector_background_transition_holo_light 0x7f070037
int drawable abc_list_selector_disabled_holo_dark 0x7f070038
int drawable abc_list_selector_disabled_holo_light 0x7f070039
int drawable abc_list_selector_holo_dark 0x7f07003a
int drawable abc_list_selector_holo_light 0x7f07003b
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f07003c
int drawable abc_popup_background_mtrl_mult 0x7f07003d
int drawable abc_ratingbar_indicator_material 0x7f07003e
int drawable abc_ratingbar_material 0x7f07003f
int drawable abc_ratingbar_small_material 0x7f070040
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f070041
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f070042
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f070043
int drawable abc_scrubber_primary_mtrl_alpha 0x7f070044
int drawable abc_scrubber_track_mtrl_alpha 0x7f070045
int drawable abc_seekbar_thumb_material 0x7f070046
int drawable abc_seekbar_tick_mark_material 0x7f070047
int drawable abc_seekbar_track_material 0x7f070048
int drawable abc_spinner_mtrl_am_alpha 0x7f070049
int drawable abc_spinner_textfield_background_material 0x7f07004a
int drawable abc_switch_thumb_material 0x7f07004b
int drawable abc_switch_track_mtrl_alpha 0x7f07004c
int drawable abc_tab_indicator_material 0x7f07004d
int drawable abc_tab_indicator_mtrl_alpha 0x7f07004e
int drawable abc_text_cursor_material 0x7f07004f
int drawable abc_text_select_handle_left_mtrl_dark 0x7f070050
int drawable abc_text_select_handle_left_mtrl_light 0x7f070051
int drawable abc_text_select_handle_middle_mtrl_dark 0x7f070052
int drawable abc_text_select_handle_middle_mtrl_light 0x7f070053
int drawable abc_text_select_handle_right_mtrl_dark 0x7f070054
int drawable abc_text_select_handle_right_mtrl_light 0x7f070055
int drawable abc_textfield_activated_mtrl_alpha 0x7f070056
int drawable abc_textfield_default_mtrl_alpha 0x7f070057
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f070058
int drawable abc_textfield_search_default_mtrl_alpha 0x7f070059
int drawable abc_textfield_search_material 0x7f07005a
int drawable abc_vector_test 0x7f07005b
int drawable avd_hide_password 0x7f07005c
int drawable avd_show_password 0x7f07005d
int drawable btn_checkbox_checked_mtrl 0x7f07005e
int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x7f07005f
int drawable btn_checkbox_unchecked_mtrl 0x7f070060
int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x7f070061
int drawable btn_radio_off_mtrl 0x7f070062
int drawable btn_radio_off_to_on_mtrl_animation 0x7f070063
int drawable btn_radio_on_mtrl 0x7f070064
int drawable btn_radio_on_to_off_mtrl_animation 0x7f070065
int drawable design_fab_background 0x7f070066
int drawable design_ic_visibility 0x7f070067
int drawable design_ic_visibility_off 0x7f070068
int drawable design_password_eye 0x7f070069
int drawable design_snackbar_background 0x7f07006a
int drawable ic_call_answer 0x7f07006b
int drawable ic_call_answer_low 0x7f07006c
int drawable ic_call_answer_video 0x7f07006d
int drawable ic_call_answer_video_low 0x7f07006e
int drawable ic_call_decline 0x7f07006f
int drawable ic_call_decline_low 0x7f070070
int drawable ic_clock_black_24dp 0x7f070071
int drawable ic_keyboard_black_24dp 0x7f070072
int drawable ic_launcher_background 0x7f070073
int drawable ic_launcher_foreground 0x7f070074
int drawable ic_mtrl_checked_circle 0x7f070075
int drawable ic_mtrl_chip_checked_black 0x7f070076
int drawable ic_mtrl_chip_checked_circle 0x7f070077
int drawable ic_mtrl_chip_close_circle 0x7f070078
int drawable material_cursor_drawable 0x7f070079
int drawable material_ic_calendar_black_24dp 0x7f07007a
int drawable material_ic_clear_black_24dp 0x7f07007b
int drawable material_ic_edit_black_24dp 0x7f07007c
int drawable material_ic_keyboard_arrow_left_black_24dp 0x7f07007d
int drawable material_ic_keyboard_arrow_next_black_24dp 0x7f07007e
int drawable material_ic_keyboard_arrow_previous_black_24dp 0x7f07007f
int drawable material_ic_keyboard_arrow_right_black_24dp 0x7f070080
int drawable material_ic_menu_arrow_down_black_24dp 0x7f070081
int drawable material_ic_menu_arrow_up_black_24dp 0x7f070082
int drawable mtrl_dialog_background 0x7f070083
int drawable mtrl_dropdown_arrow 0x7f070084
int drawable mtrl_ic_arrow_drop_down 0x7f070085
int drawable mtrl_ic_arrow_drop_up 0x7f070086
int drawable mtrl_ic_cancel 0x7f070087
int drawable mtrl_ic_error 0x7f070088
int drawable mtrl_navigation_bar_item_background 0x7f070089
int drawable mtrl_popupmenu_background 0x7f07008a
int drawable mtrl_popupmenu_background_dark 0x7f07008b
int drawable mtrl_tabs_default_indicator 0x7f07008c
int drawable navigation_empty_icon 0x7f07008d
int drawable notification_action_background 0x7f07008e
int drawable notification_bg 0x7f07008f
int drawable notification_bg_low 0x7f070090
int drawable notification_bg_low_normal 0x7f070091
int drawable notification_bg_low_pressed 0x7f070092
int drawable notification_bg_normal 0x7f070093
int drawable notification_bg_normal_pressed 0x7f070094
int drawable notification_icon_background 0x7f070095
int drawable notification_oversize_large_icon_bg 0x7f070096
int drawable notification_template_icon_bg 0x7f070097
int drawable notification_template_icon_low_bg 0x7f070098
int drawable notification_tile_bg 0x7f070099
int drawable notify_panel_notification_icon_bg 0x7f07009a
int drawable test_custom_background 0x7f07009b
int drawable tooltip_frame_dark 0x7f07009c
int drawable tooltip_frame_light 0x7f07009d
int id ALT 0x7f080000
int id BOTTOM_END 0x7f080001
int id BOTTOM_START 0x7f080002
int id CTRL 0x7f080003
int id FUNCTION 0x7f080004
int id META 0x7f080005
int id NO_DEBUG 0x7f080006
int id SHIFT 0x7f080007
int id SHOW_ALL 0x7f080008
int id SHOW_PATH 0x7f080009
int id SHOW_PROGRESS 0x7f08000a
int id SYM 0x7f08000b
int id TOP_END 0x7f08000c
int id TOP_START 0x7f08000d
int id accelerate 0x7f08000e
int id accessibility_action_clickable_span 0x7f08000f
int id accessibility_custom_action_0 0x7f080010
int id accessibility_custom_action_1 0x7f080011
int id accessibility_custom_action_10 0x7f080012
int id accessibility_custom_action_11 0x7f080013
int id accessibility_custom_action_12 0x7f080014
int id accessibility_custom_action_13 0x7f080015
int id accessibility_custom_action_14 0x7f080016
int id accessibility_custom_action_15 0x7f080017
int id accessibility_custom_action_16 0x7f080018
int id accessibility_custom_action_17 0x7f080019
int id accessibility_custom_action_18 0x7f08001a
int id accessibility_custom_action_19 0x7f08001b
int id accessibility_custom_action_2 0x7f08001c
int id accessibility_custom_action_20 0x7f08001d
int id accessibility_custom_action_21 0x7f08001e
int id accessibility_custom_action_22 0x7f08001f
int id accessibility_custom_action_23 0x7f080020
int id accessibility_custom_action_24 0x7f080021
int id accessibility_custom_action_25 0x7f080022
int id accessibility_custom_action_26 0x7f080023
int id accessibility_custom_action_27 0x7f080024
int id accessibility_custom_action_28 0x7f080025
int id accessibility_custom_action_29 0x7f080026
int id accessibility_custom_action_3 0x7f080027
int id accessibility_custom_action_30 0x7f080028
int id accessibility_custom_action_31 0x7f080029
int id accessibility_custom_action_4 0x7f08002a
int id accessibility_custom_action_5 0x7f08002b
int id accessibility_custom_action_6 0x7f08002c
int id accessibility_custom_action_7 0x7f08002d
int id accessibility_custom_action_8 0x7f08002e
int id accessibility_custom_action_9 0x7f08002f
int id action_bar 0x7f080030
int id action_bar_activity_content 0x7f080031
int id action_bar_container 0x7f080032
int id action_bar_root 0x7f080033
int id action_bar_spinner 0x7f080034
int id action_bar_subtitle 0x7f080035
int id action_bar_title 0x7f080036
int id action_container 0x7f080037
int id action_context_bar 0x7f080038
int id action_divider 0x7f080039
int id action_image 0x7f08003a
int id action_menu_divider 0x7f08003b
int id action_menu_presenter 0x7f08003c
int id action_mode_bar 0x7f08003d
int id action_mode_bar_stub 0x7f08003e
int id action_mode_close_button 0x7f08003f
int id action_text 0x7f080040
int id actions 0x7f080041
int id activity_chooser_view_content 0x7f080042
int id add 0x7f080043
int id alertTitle 0x7f080044
int id aligned 0x7f080045
int id all 0x7f080046
int id always 0x7f080047
int id androidx_compose_ui_view_composition_context 0x7f080048
int id androidx_window_activity_scope 0x7f080049
int id animateToEnd 0x7f08004a
int id animateToStart 0x7f08004b
int id arc 0x7f08004c
int id asConfigured 0x7f08004d
int id async 0x7f08004e
int id auto 0x7f08004f
int id autoComplete 0x7f080050
int id autoCompleteToEnd 0x7f080051
int id autoCompleteToStart 0x7f080052
int id barrier 0x7f080053
int id baseline 0x7f080054
int id beginOnFirstDraw 0x7f080055
int id beginning 0x7f080056
int id blocking 0x7f080057
int id bottom 0x7f080058
int id bounce 0x7f080059
int id buttonPanel 0x7f08005a
int id cancel_button 0x7f08005b
int id center 0x7f08005c
int id center_horizontal 0x7f08005d
int id center_vertical 0x7f08005e
int id chain 0x7f08005f
int id chains 0x7f080060
int id checkbox 0x7f080061
int id checked 0x7f080062
int id chip 0x7f080063
int id chip1 0x7f080064
int id chip2 0x7f080065
int id chip3 0x7f080066
int id chip_group 0x7f080067
int id chronometer 0x7f080068
int id circle_center 0x7f080069
int id clear_text 0x7f08006a
int id clip_horizontal 0x7f08006b
int id clip_vertical 0x7f08006c
int id clockwise 0x7f08006d
int id collapseActionView 0x7f08006e
int id compose_view_saveable_id_tag 0x7f08006f
int id confirm_button 0x7f080070
int id consume_window_insets_tag 0x7f080071
int id container 0x7f080072
int id content 0x7f080073
int id contentPanel 0x7f080074
int id contiguous 0x7f080075
int id coordinator 0x7f080076
int id cos 0x7f080077
int id counterclockwise 0x7f080078
int id custom 0x7f080079
int id customPanel 0x7f08007a
int id cut 0x7f08007b
int id date_picker_actions 0x7f08007c
int id decelerate 0x7f08007d
int id decelerateAndComplete 0x7f08007e
int id decor_content_parent 0x7f08007f
int id default_activity_button 0x7f080080
int id deltaRelative 0x7f080081
int id design_bottom_sheet 0x7f080082
int id design_menu_item_action_area 0x7f080083
int id design_menu_item_action_area_stub 0x7f080084
int id design_menu_item_text 0x7f080085
int id design_navigation_view 0x7f080086
int id dialog_button 0x7f080087
int id dimensions 0x7f080088
int id direct 0x7f080089
int id disableHome 0x7f08008a
int id disablePostScroll 0x7f08008b
int id disableScroll 0x7f08008c
int id disjoint 0x7f08008d
int id dragDown 0x7f08008e
int id dragEnd 0x7f08008f
int id dragLeft 0x7f080090
int id dragRight 0x7f080091
int id dragStart 0x7f080092
int id dragUp 0x7f080093
int id dropdown_menu 0x7f080094
int id easeIn 0x7f080095
int id easeInOut 0x7f080096
int id easeOut 0x7f080097
int id edit_query 0x7f080098
int id edit_text_id 0x7f080099
int id elastic 0x7f08009a
int id end 0x7f08009b
int id endToStart 0x7f08009c
int id enterAlways 0x7f08009d
int id enterAlwaysCollapsed 0x7f08009e
int id exitUntilCollapsed 0x7f08009f
int id expand_activities_button 0x7f0800a0
int id expanded_menu 0x7f0800a1
int id fade 0x7f0800a2
int id fill 0x7f0800a3
int id fill_horizontal 0x7f0800a4
int id fill_vertical 0x7f0800a5
int id filled 0x7f0800a6
int id fitToContents 0x7f0800a7
int id fixed 0x7f0800a8
int id flip 0x7f0800a9
int id floating 0x7f0800aa
int id forever 0x7f0800ab
int id fragment_container_view_tag 0x7f0800ac
int id ghost_view 0x7f0800ad
int id ghost_view_holder 0x7f0800ae
int id gone 0x7f0800af
int id graph 0x7f0800b0
int id graph_wrap 0x7f0800b1
int id group_divider 0x7f0800b2
int id groups 0x7f0800b3
int id guideline 0x7f0800b4
int id header_title 0x7f0800b5
int id hide_ime_id 0x7f0800b6
int id hide_in_inspector_tag 0x7f0800b7
int id hideable 0x7f0800b8
int id home 0x7f0800b9
int id homeAsUp 0x7f0800ba
int id honorRequest 0x7f0800bb
int id icon 0x7f0800bc
int id icon_group 0x7f0800bd
int id ifRoom 0x7f0800be
int id ignore 0x7f0800bf
int id ignoreRequest 0x7f0800c0
int id image 0x7f0800c1
int id info 0x7f0800c2
int id inspection_slot_table_set 0x7f0800c3
int id invisible 0x7f0800c4
int id inward 0x7f0800c5
int id is_pooling_container_tag 0x7f0800c6
int id italic 0x7f0800c7
int id item_touch_helper_previous_elevation 0x7f0800c8
int id jumpToEnd 0x7f0800c9
int id jumpToStart 0x7f0800ca
int id labeled 0x7f0800cb
int id layout 0x7f0800cc
int id left 0x7f0800cd
int id leftToRight 0x7f0800ce
int id line1 0x7f0800cf
int id line3 0x7f0800d0
int id linear 0x7f0800d1
int id listMode 0x7f0800d2
int id list_item 0x7f0800d3
int id locale 0x7f0800d4
int id ltr 0x7f0800d5
int id masked 0x7f0800d6
int id material_clock_display 0x7f0800d7
int id material_clock_face 0x7f0800d8
int id material_clock_hand 0x7f0800d9
int id material_clock_period_am_button 0x7f0800da
int id material_clock_period_pm_button 0x7f0800db
int id material_clock_period_toggle 0x7f0800dc
int id material_hour_text_input 0x7f0800dd
int id material_hour_tv 0x7f0800de
int id material_label 0x7f0800df
int id material_minute_text_input 0x7f0800e0
int id material_minute_tv 0x7f0800e1
int id material_textinput_timepicker 0x7f0800e2
int id material_timepicker_cancel_button 0x7f0800e3
int id material_timepicker_container 0x7f0800e4
int id material_timepicker_edit_text 0x7f0800e5
int id material_timepicker_mode_button 0x7f0800e6
int id material_timepicker_ok_button 0x7f0800e7
int id material_timepicker_view 0x7f0800e8
int id material_value_index 0x7f0800e9
int id message 0x7f0800ea
int id middle 0x7f0800eb
int id mini 0x7f0800ec
int id month_grid 0x7f0800ed
int id month_navigation_bar 0x7f0800ee
int id month_navigation_fragment_toggle 0x7f0800ef
int id month_navigation_next 0x7f0800f0
int id month_navigation_previous 0x7f0800f1
int id month_title 0x7f0800f2
int id motion_base 0x7f0800f3
int id mtrl_anchor_parent 0x7f0800f4
int id mtrl_calendar_day_selector_frame 0x7f0800f5
int id mtrl_calendar_days_of_week 0x7f0800f6
int id mtrl_calendar_frame 0x7f0800f7
int id mtrl_calendar_main_pane 0x7f0800f8
int id mtrl_calendar_months 0x7f0800f9
int id mtrl_calendar_selection_frame 0x7f0800fa
int id mtrl_calendar_text_input_frame 0x7f0800fb
int id mtrl_calendar_year_selector_frame 0x7f0800fc
int id mtrl_card_checked_layer_id 0x7f0800fd
int id mtrl_child_content_container 0x7f0800fe
int id mtrl_internal_children_alpha_tag 0x7f0800ff
int id mtrl_motion_snapshot_view 0x7f080100
int id mtrl_picker_fullscreen 0x7f080101
int id mtrl_picker_header 0x7f080102
int id mtrl_picker_header_selection_text 0x7f080103
int id mtrl_picker_header_title_and_selection 0x7f080104
int id mtrl_picker_header_toggle 0x7f080105
int id mtrl_picker_text_input_date 0x7f080106
int id mtrl_picker_text_input_range_end 0x7f080107
int id mtrl_picker_text_input_range_start 0x7f080108
int id mtrl_picker_title_text 0x7f080109
int id mtrl_view_tag_bottom_padding 0x7f08010a
int id multiply 0x7f08010b
int id nav_controller_view_tag 0x7f08010c
int id nav_host_fragment_container 0x7f08010d
int id navigation_bar_item_icon_view 0x7f08010e
int id navigation_bar_item_labels_group 0x7f08010f
int id navigation_bar_item_large_label_view 0x7f080110
int id navigation_bar_item_small_label_view 0x7f080111
int id navigation_header_container 0x7f080112
int id never 0x7f080113
int id noScroll 0x7f080114
int id none 0x7f080115
int id normal 0x7f080116
int id notification_background 0x7f080117
int id notification_main_column 0x7f080118
int id notification_main_column_container 0x7f080119
int id off 0x7f08011a
int id on 0x7f08011b
int id outline 0x7f08011c
int id outward 0x7f08011d
int id packed 0x7f08011e
int id parallax 0x7f08011f
int id parent 0x7f080120
int id parentPanel 0x7f080121
int id parentRelative 0x7f080122
int id parent_matrix 0x7f080123
int id password_toggle 0x7f080124
int id path 0x7f080125
int id pathRelative 0x7f080126
int id peekHeight 0x7f080127
int id percent 0x7f080128
int id pin 0x7f080129
int id pooling_container_listener_holder_tag 0x7f08012a
int id position 0x7f08012b
int id postLayout 0x7f08012c
int id progress_circular 0x7f08012d
int id progress_horizontal 0x7f08012e
int id radio 0x7f08012f
int id ratio 0x7f080130
int id rectangles 0x7f080131
int id report_drawn 0x7f080132
int id reverseSawtooth 0x7f080133
int id right 0x7f080134
int id rightToLeft 0x7f080135
int id right_icon 0x7f080136
int id right_side 0x7f080137
int id rounded 0x7f080138
int id row_index_key 0x7f080139
int id rtl 0x7f08013a
int id save_non_transition_alpha 0x7f08013b
int id save_overlay_view 0x7f08013c
int id sawtooth 0x7f08013d
int id scale 0x7f08013e
int id screen 0x7f08013f
int id scroll 0x7f080140
int id scrollIndicatorDown 0x7f080141
int id scrollIndicatorUp 0x7f080142
int id scrollView 0x7f080143
int id scrollable 0x7f080144
int id search_badge 0x7f080145
int id search_bar 0x7f080146
int id search_button 0x7f080147
int id search_close_btn 0x7f080148
int id search_edit_frame 0x7f080149
int id search_go_btn 0x7f08014a
int id search_mag_icon 0x7f08014b
int id search_plate 0x7f08014c
int id search_src_text 0x7f08014d
int id search_voice_btn 0x7f08014e
int id select_dialog_listview 0x7f08014f
int id selected 0x7f080150
int id selection_type 0x7f080151
int id shortcut 0x7f080152
int id showCustom 0x7f080153
int id showHome 0x7f080154
int id showTitle 0x7f080155
int id sin 0x7f080156
int id skipCollapsed 0x7f080157
int id slide 0x7f080158
int id sliding_pane_detail_container 0x7f080159
int id sliding_pane_layout 0x7f08015a
int id snackbar_action 0x7f08015b
int id snackbar_text 0x7f08015c
int id snap 0x7f08015d
int id snapMargins 0x7f08015e
int id spacer 0x7f08015f
int id special_effects_controller_view_tag 0x7f080160
int id spline 0x7f080161
int id split_action_bar 0x7f080162
int id spread 0x7f080163
int id spread_inside 0x7f080164
int id square 0x7f080165
int id src_atop 0x7f080166
int id src_in 0x7f080167
int id src_over 0x7f080168
int id standard 0x7f080169
int id start 0x7f08016a
int id startHorizontal 0x7f08016b
int id startToEnd 0x7f08016c
int id startVertical 0x7f08016d
int id staticLayout 0x7f08016e
int id staticPostLayout 0x7f08016f
int id stop 0x7f080170
int id stretch 0x7f080171
int id submenuarrow 0x7f080172
int id submit_area 0x7f080173
int id tabMode 0x7f080174
int id tag_accessibility_actions 0x7f080175
int id tag_accessibility_clickable_spans 0x7f080176
int id tag_accessibility_heading 0x7f080177
int id tag_accessibility_pane_title 0x7f080178
int id tag_on_apply_window_listener 0x7f080179
int id tag_on_receive_content_listener 0x7f08017a
int id tag_on_receive_content_mime_types 0x7f08017b
int id tag_screen_reader_focusable 0x7f08017c
int id tag_state_description 0x7f08017d
int id tag_transition_group 0x7f08017e
int id tag_unhandled_key_event_manager 0x7f08017f
int id tag_unhandled_key_listeners 0x7f080180
int id tag_window_insets_animation_callback 0x7f080181
int id test_checkbox_android_button_tint 0x7f080182
int id test_checkbox_app_button_tint 0x7f080183
int id test_radiobutton_android_button_tint 0x7f080184
int id test_radiobutton_app_button_tint 0x7f080185
int id text 0x7f080186
int id text2 0x7f080187
int id textEnd 0x7f080188
int id textSpacerNoButtons 0x7f080189
int id textSpacerNoTitle 0x7f08018a
int id textStart 0x7f08018b
int id textTop 0x7f08018c
int id text_input_end_icon 0x7f08018d
int id text_input_error_icon 0x7f08018e
int id text_input_start_icon 0x7f08018f
int id textinput_counter 0x7f080190
int id textinput_error 0x7f080191
int id textinput_helper_text 0x7f080192
int id textinput_placeholder 0x7f080193
int id textinput_prefix_text 0x7f080194
int id textinput_suffix_text 0x7f080195
int id time 0x7f080196
int id title 0x7f080197
int id titleDividerNoCustom 0x7f080198
int id title_template 0x7f080199
int id toggle 0x7f08019a
int id top 0x7f08019b
int id topPanel 0x7f08019c
int id touch_outside 0x7f08019d
int id transitionToEnd 0x7f08019e
int id transitionToStart 0x7f08019f
int id transition_current_scene 0x7f0801a0
int id transition_layout_save 0x7f0801a1
int id transition_position 0x7f0801a2
int id transition_scene_layoutid_cache 0x7f0801a3
int id transition_transform 0x7f0801a4
int id triangle 0x7f0801a5
int id unchecked 0x7f0801a6
int id uniform 0x7f0801a7
int id unlabeled 0x7f0801a8
int id up 0x7f0801a9
int id useLogo 0x7f0801aa
int id view_offset_helper 0x7f0801ab
int id view_tree_lifecycle_owner 0x7f0801ac
int id view_tree_on_back_pressed_dispatcher_owner 0x7f0801ad
int id view_tree_saved_state_registry_owner 0x7f0801ae
int id view_tree_view_model_store_owner 0x7f0801af
int id visible 0x7f0801b0
int id visible_removing_fragment_view_tag 0x7f0801b1
int id withText 0x7f0801b2
int id withinBounds 0x7f0801b3
int id wrap 0x7f0801b4
int id wrap_content 0x7f0801b5
int id wrapped_composition_tag 0x7f0801b6
int id zero_corner_chip 0x7f0801b7
int integer abc_config_activityDefaultDur 0x7f090000
int integer abc_config_activityShortDur 0x7f090001
int integer app_bar_elevation_anim_duration 0x7f090002
int integer bottom_sheet_slide_duration 0x7f090003
int integer cancel_button_image_alpha 0x7f090004
int integer config_navAnimTime 0x7f090005
int integer config_tooltipAnimTime 0x7f090006
int integer design_snackbar_text_max_lines 0x7f090007
int integer design_tab_indicator_anim_duration_ms 0x7f090008
int integer hide_password_duration 0x7f090009
int integer material_motion_duration_long_1 0x7f09000a
int integer material_motion_duration_long_2 0x7f09000b
int integer material_motion_duration_medium_1 0x7f09000c
int integer material_motion_duration_medium_2 0x7f09000d
int integer material_motion_duration_short_1 0x7f09000e
int integer material_motion_duration_short_2 0x7f09000f
int integer material_motion_path 0x7f090010
int integer mtrl_badge_max_character_count 0x7f090011
int integer mtrl_btn_anim_delay_ms 0x7f090012
int integer mtrl_btn_anim_duration_ms 0x7f090013
int integer mtrl_calendar_header_orientation 0x7f090014
int integer mtrl_calendar_selection_text_lines 0x7f090015
int integer mtrl_calendar_year_selector_span 0x7f090016
int integer mtrl_card_anim_delay_ms 0x7f090017
int integer mtrl_card_anim_duration_ms 0x7f090018
int integer mtrl_chip_anim_duration 0x7f090019
int integer mtrl_tab_indicator_anim_duration_ms 0x7f09001a
int integer show_password_duration 0x7f09001b
int integer status_bar_notification_info_maxnum 0x7f09001c
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x7f0a0000
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x7f0a0001
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x7f0a0002
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x7f0a0003
int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x7f0a0004
int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x7f0a0005
int interpolator fast_out_slow_in 0x7f0a0006
int interpolator mtrl_fast_out_linear_in 0x7f0a0007
int interpolator mtrl_fast_out_slow_in 0x7f0a0008
int interpolator mtrl_linear 0x7f0a0009
int interpolator mtrl_linear_out_slow_in 0x7f0a000a
int layout abc_action_bar_title_item 0x7f0b0000
int layout abc_action_bar_up_container 0x7f0b0001
int layout abc_action_menu_item_layout 0x7f0b0002
int layout abc_action_menu_layout 0x7f0b0003
int layout abc_action_mode_bar 0x7f0b0004
int layout abc_action_mode_close_item_material 0x7f0b0005
int layout abc_activity_chooser_view 0x7f0b0006
int layout abc_activity_chooser_view_list_item 0x7f0b0007
int layout abc_alert_dialog_button_bar_material 0x7f0b0008
int layout abc_alert_dialog_material 0x7f0b0009
int layout abc_alert_dialog_title_material 0x7f0b000a
int layout abc_cascading_menu_item_layout 0x7f0b000b
int layout abc_dialog_title_material 0x7f0b000c
int layout abc_expanded_menu_layout 0x7f0b000d
int layout abc_list_menu_item_checkbox 0x7f0b000e
int layout abc_list_menu_item_icon 0x7f0b000f
int layout abc_list_menu_item_layout 0x7f0b0010
int layout abc_list_menu_item_radio 0x7f0b0011
int layout abc_popup_menu_header_item_layout 0x7f0b0012
int layout abc_popup_menu_item_layout 0x7f0b0013
int layout abc_screen_content_include 0x7f0b0014
int layout abc_screen_simple 0x7f0b0015
int layout abc_screen_simple_overlay_action_mode 0x7f0b0016
int layout abc_screen_toolbar 0x7f0b0017
int layout abc_search_dropdown_item_icons_2line 0x7f0b0018
int layout abc_search_view 0x7f0b0019
int layout abc_select_dialog_material 0x7f0b001a
int layout abc_tooltip 0x7f0b001b
int layout custom_dialog 0x7f0b001c
int layout design_bottom_navigation_item 0x7f0b001d
int layout design_bottom_sheet_dialog 0x7f0b001e
int layout design_layout_snackbar 0x7f0b001f
int layout design_layout_snackbar_include 0x7f0b0020
int layout design_layout_tab_icon 0x7f0b0021
int layout design_layout_tab_text 0x7f0b0022
int layout design_menu_item_action_area 0x7f0b0023
int layout design_navigation_item 0x7f0b0024
int layout design_navigation_item_header 0x7f0b0025
int layout design_navigation_item_separator 0x7f0b0026
int layout design_navigation_item_subheader 0x7f0b0027
int layout design_navigation_menu 0x7f0b0028
int layout design_navigation_menu_item 0x7f0b0029
int layout design_text_input_end_icon 0x7f0b002a
int layout design_text_input_start_icon 0x7f0b002b
int layout ime_base_split_test_activity 0x7f0b002c
int layout ime_secondary_split_test_activity 0x7f0b002d
int layout material_chip_input_combo 0x7f0b002e
int layout material_clock_display 0x7f0b002f
int layout material_clock_display_divider 0x7f0b0030
int layout material_clock_period_toggle 0x7f0b0031
int layout material_clock_period_toggle_land 0x7f0b0032
int layout material_clockface_textview 0x7f0b0033
int layout material_clockface_view 0x7f0b0034
int layout material_radial_view_group 0x7f0b0035
int layout material_textinput_timepicker 0x7f0b0036
int layout material_time_chip 0x7f0b0037
int layout material_time_input 0x7f0b0038
int layout material_timepicker 0x7f0b0039
int layout material_timepicker_dialog 0x7f0b003a
int layout material_timepicker_textinput_display 0x7f0b003b
int layout mtrl_alert_dialog 0x7f0b003c
int layout mtrl_alert_dialog_actions 0x7f0b003d
int layout mtrl_alert_dialog_title 0x7f0b003e
int layout mtrl_alert_select_dialog_item 0x7f0b003f
int layout mtrl_alert_select_dialog_multichoice 0x7f0b0040
int layout mtrl_alert_select_dialog_singlechoice 0x7f0b0041
int layout mtrl_calendar_day 0x7f0b0042
int layout mtrl_calendar_day_of_week 0x7f0b0043
int layout mtrl_calendar_days_of_week 0x7f0b0044
int layout mtrl_calendar_horizontal 0x7f0b0045
int layout mtrl_calendar_month 0x7f0b0046
int layout mtrl_calendar_month_labeled 0x7f0b0047
int layout mtrl_calendar_month_navigation 0x7f0b0048
int layout mtrl_calendar_months 0x7f0b0049
int layout mtrl_calendar_vertical 0x7f0b004a
int layout mtrl_calendar_year 0x7f0b004b
int layout mtrl_layout_snackbar 0x7f0b004c
int layout mtrl_layout_snackbar_include 0x7f0b004d
int layout mtrl_navigation_rail_item 0x7f0b004e
int layout mtrl_picker_actions 0x7f0b004f
int layout mtrl_picker_dialog 0x7f0b0050
int layout mtrl_picker_fullscreen 0x7f0b0051
int layout mtrl_picker_header_dialog 0x7f0b0052
int layout mtrl_picker_header_fullscreen 0x7f0b0053
int layout mtrl_picker_header_selection_text 0x7f0b0054
int layout mtrl_picker_header_title_text 0x7f0b0055
int layout mtrl_picker_header_toggle 0x7f0b0056
int layout mtrl_picker_text_input_date 0x7f0b0057
int layout mtrl_picker_text_input_date_range 0x7f0b0058
int layout notification_action 0x7f0b0059
int layout notification_action_tombstone 0x7f0b005a
int layout notification_template_custom_big 0x7f0b005b
int layout notification_template_icon_group 0x7f0b005c
int layout notification_template_part_chronometer 0x7f0b005d
int layout notification_template_part_time 0x7f0b005e
int layout select_dialog_item_material 0x7f0b005f
int layout select_dialog_multichoice_material 0x7f0b0060
int layout select_dialog_singlechoice_material 0x7f0b0061
int layout support_simple_spinner_dropdown_item 0x7f0b0062
int layout test_action_chip 0x7f0b0063
int layout test_chip_zero_corner_radius 0x7f0b0064
int layout test_design_checkbox 0x7f0b0065
int layout test_design_radiobutton 0x7f0b0066
int layout test_navigation_bar_item_layout 0x7f0b0067
int layout test_reflow_chipgroup 0x7f0b0068
int layout test_toolbar 0x7f0b0069
int layout test_toolbar_custom_background 0x7f0b006a
int layout test_toolbar_elevation 0x7f0b006b
int layout test_toolbar_surface 0x7f0b006c
int layout text_view_with_line_height_from_appearance 0x7f0b006d
int layout text_view_with_line_height_from_layout 0x7f0b006e
int layout text_view_with_line_height_from_style 0x7f0b006f
int layout text_view_with_theme_line_height 0x7f0b0070
int layout text_view_without_line_height 0x7f0b0071
int mipmap ic_launcher 0x7f0c0000
int mipmap ic_launcher_round 0x7f0c0001
int plurals mtrl_badge_content_description 0x7f0d0000
int string abc_action_bar_home_description 0x7f0e0000
int string abc_action_bar_up_description 0x7f0e0001
int string abc_action_menu_overflow_description 0x7f0e0002
int string abc_action_mode_done 0x7f0e0003
int string abc_activity_chooser_view_see_all 0x7f0e0004
int string abc_activitychooserview_choose_application 0x7f0e0005
int string abc_capital_off 0x7f0e0006
int string abc_capital_on 0x7f0e0007
int string abc_menu_alt_shortcut_label 0x7f0e0008
int string abc_menu_ctrl_shortcut_label 0x7f0e0009
int string abc_menu_delete_shortcut_label 0x7f0e000a
int string abc_menu_enter_shortcut_label 0x7f0e000b
int string abc_menu_function_shortcut_label 0x7f0e000c
int string abc_menu_meta_shortcut_label 0x7f0e000d
int string abc_menu_shift_shortcut_label 0x7f0e000e
int string abc_menu_space_shortcut_label 0x7f0e000f
int string abc_menu_sym_shortcut_label 0x7f0e0010
int string abc_prepend_shortcut_label 0x7f0e0011
int string abc_search_hint 0x7f0e0012
int string abc_searchview_description_clear 0x7f0e0013
int string abc_searchview_description_query 0x7f0e0014
int string abc_searchview_description_search 0x7f0e0015
int string abc_searchview_description_submit 0x7f0e0016
int string abc_searchview_description_voice 0x7f0e0017
int string abc_shareactionprovider_share_with 0x7f0e0018
int string abc_shareactionprovider_share_with_application 0x7f0e0019
int string abc_toolbar_collapse_description 0x7f0e001a
int string add_first_note 0x7f0e001b
int string add_note 0x7f0e001c
int string add_shift 0x7f0e001d
int string advanced_calculation 0x7f0e001e
int string alarm_channel_description 0x7f0e001f
int string alarm_channel_name 0x7f0e0020
int string alarm_confirm 0x7f0e0021
int string alarm_dismiss 0x7f0e0022
int string alarm_settings 0x7f0e0023
int string alarm_snooze 0x7f0e0024
int string alarm_sound 0x7f0e0025
int string alarm_title 0x7f0e0026
int string alarm_vibration 0x7f0e0027
int string all_templates_added 0x7f0e0028
int string all_templates_added_desc 0x7f0e0029
int string androidx_startup 0x7f0e002a
int string app_info 0x7f0e002b
int string app_name 0x7f0e002c
int string app_permissions 0x7f0e002d
int string app_version 0x7f0e002e
int string appbar_scrolling_view_behavior 0x7f0e002f
int string apply_filter 0x7f0e0030
int string apply_immediately 0x7f0e0031
int string associated_shifts 0x7f0e0032
int string backup_data 0x7f0e0033
int string backup_restore 0x7f0e0034
int string bottom_sheet_behavior 0x7f0e0035
int string bottom_sheet_collapse_description 0x7f0e0036
int string bottom_sheet_dismiss_description 0x7f0e0037
int string bottom_sheet_drag_handle_description 0x7f0e0038
int string bottom_sheet_expand_description 0x7f0e0039
int string bottomsheet_action_expand_halfway 0x7f0e003a
int string break_minutes 0x7f0e003b
int string btn_check_in 0x7f0e003c
int string btn_check_out 0x7f0e003d
int string btn_complete 0x7f0e003e
int string btn_completed 0x7f0e003f
int string btn_go_work 0x7f0e0040
int string btn_going 0x7f0e0041
int string btn_punch 0x7f0e0042
int string btn_ready_complete 0x7f0e0043
int string btn_reset 0x7f0e0044
int string btn_working 0x7f0e0045
int string call_notification_answer_action 0x7f0e0046
int string call_notification_answer_video_action 0x7f0e0047
int string call_notification_decline_action 0x7f0e0048
int string call_notification_hang_up_action 0x7f0e0049
int string call_notification_incoming_text 0x7f0e004a
int string call_notification_ongoing_text 0x7f0e004b
int string call_notification_screening_text 0x7f0e004c
int string cancel 0x7f0e004d
int string change_shift_reminder 0x7f0e004e
int string character_counter_content_description 0x7f0e004f
int string character_counter_overflowed_content_description 0x7f0e0050
int string character_counter_pattern 0x7f0e0051
int string check_in 0x7f0e0052
int string check_out 0x7f0e0053
int string chip_text 0x7f0e0054
int string clear_all_data 0x7f0e0055
int string clear_filter 0x7f0e0056
int string clear_text_end_icon_content_description 0x7f0e0057
int string close_drawer 0x7f0e0058
int string close_sheet 0x7f0e0059
int string collapse 0x7f0e005a
int string collapsed 0x7f0e005b
int string confirm 0x7f0e005c
int string confirm_clear_data 0x7f0e005d
int string confirm_delete_note 0x7f0e005e
int string confirm_delete_shift 0x7f0e005f
int string confirm_reset_settings 0x7f0e0060
int string confirm_rotation_selection 0x7f0e0061
int string current_shift 0x7f0e0062
int string data_exported 0x7f0e0063
int string data_imported 0x7f0e0064
int string data_management 0x7f0e0065
int string date_input_headline 0x7f0e0066
int string date_input_headline_description 0x7f0e0067
int string date_input_invalid_for_pattern 0x7f0e0068
int string date_input_invalid_not_allowed 0x7f0e0069
int string date_input_invalid_year_range 0x7f0e006a
int string date_input_label 0x7f0e006b
int string date_input_no_input_description 0x7f0e006c
int string date_input_title 0x7f0e006d
int string date_picker_headline 0x7f0e006e
int string date_picker_headline_description 0x7f0e006f
int string date_picker_navigate_to_year_description 0x7f0e0070
int string date_picker_no_selection_description 0x7f0e0071
int string date_picker_scroll_to_earlier_years 0x7f0e0072
int string date_picker_scroll_to_later_years 0x7f0e0073
int string date_picker_switch_to_calendar_mode 0x7f0e0074
int string date_picker_switch_to_day_selection 0x7f0e0075
int string date_picker_switch_to_input_mode 0x7f0e0076
int string date_picker_switch_to_next_month 0x7f0e0077
int string date_picker_switch_to_previous_month 0x7f0e0078
int string date_picker_switch_to_year_selection 0x7f0e0079
int string date_picker_title 0x7f0e007a
int string date_picker_today_description 0x7f0e007b
int string date_picker_year_picker_pane_title 0x7f0e007c
int string date_range 0x7f0e007d
int string date_range_input_invalid_range_input 0x7f0e007e
int string date_range_input_title 0x7f0e007f
int string date_range_picker_day_in_range 0x7f0e0080
int string date_range_picker_end_headline 0x7f0e0081
int string date_range_picker_scroll_to_next_month 0x7f0e0082
int string date_range_picker_scroll_to_previous_month 0x7f0e0083
int string date_range_picker_start_headline 0x7f0e0084
int string date_range_picker_title 0x7f0e0085
int string day_friday 0x7f0e0086
int string day_monday 0x7f0e0087
int string day_saturday 0x7f0e0088
int string day_sunday 0x7f0e0089
int string day_thursday 0x7f0e008a
int string day_tuesday 0x7f0e008b
int string day_wednesday 0x7f0e008c
int string days_applied 0x7f0e008d
int string default_error_message 0x7f0e008e
int string default_popup_window_title 0x7f0e008f
int string delete 0x7f0e0090
int string delete_note 0x7f0e0091
int string delete_shift 0x7f0e0092
int string departure_time 0x7f0e0093
int string dest_title 0x7f0e0094
int string dialog 0x7f0e0095
int string dropdown_menu 0x7f0e0096
int string early_threshold 0x7f0e0097
int string edit 0x7f0e0098
int string edit_note 0x7f0e0099
int string edit_shift 0x7f0e009a
int string end_time 0x7f0e009b
int string error 0x7f0e009c
int string error_duplicate_name 0x7f0e009d
int string error_icon_content_description 0x7f0e009e
int string error_invalid_range 0x7f0e009f
int string error_invalid_time 0x7f0e00a0
int string error_network 0x7f0e00a1
int string error_no_days_selected 0x7f0e00a2
int string error_permission_denied 0x7f0e00a3
int string error_required_field 0x7f0e00a4
int string expand 0x7f0e00a5
int string expanded 0x7f0e00a6
int string export_data 0x7f0e00a7
int string export_statistics 0x7f0e00a8
int string exposed_dropdown_menu_content_description 0x7f0e00a9
int string fab_transformation_scrim_behavior 0x7f0e00aa
int string fab_transformation_sheet_behavior 0x7f0e00ab
int string first_day_of_week 0x7f0e00ac
int string forget_location 0x7f0e00ad
int string frequency_biweekly 0x7f0e00ae
int string frequency_monthly 0x7f0e00af
int string frequency_weekly 0x7f0e00b0
int string from_date 0x7f0e00b1
int string general_channel_description 0x7f0e00b2
int string general_channel_name 0x7f0e00b3
int string general_settings 0x7f0e00b4
int string hide_bottom_view_on_scroll_behavior 0x7f0e00b5
int string holiday_list 0x7f0e00b6
int string home_title 0x7f0e00b7
int string humidity 0x7f0e00b8
int string icon_content_description 0x7f0e00b9
int string import_data 0x7f0e00ba
int string in_progress 0x7f0e00bb
int string indeterminate 0x7f0e00bc
int string item_view_role_description 0x7f0e00bd
int string language 0x7f0e00be
int string language_english 0x7f0e00bf
int string language_vietnamese 0x7f0e00c0
int string late_threshold 0x7f0e00c1
int string late_threshold_minutes 0x7f0e00c2
int string loading 0x7f0e00c3
int string location_home 0x7f0e00c4
int string location_office 0x7f0e00c5
int string m3c_bottom_sheet_pane_title 0x7f0e00c6
int string material_clock_display_divider 0x7f0e00c7
int string material_clock_toggle_content_description 0x7f0e00c8
int string material_hour_selection 0x7f0e00c9
int string material_hour_suffix 0x7f0e00ca
int string material_minute_selection 0x7f0e00cb
int string material_minute_suffix 0x7f0e00cc
int string material_motion_easing_accelerated 0x7f0e00cd
int string material_motion_easing_decelerated 0x7f0e00ce
int string material_motion_easing_emphasized 0x7f0e00cf
int string material_motion_easing_linear 0x7f0e00d0
int string material_motion_easing_standard 0x7f0e00d1
int string material_slider_range_end 0x7f0e00d2
int string material_slider_range_start 0x7f0e00d3
int string material_timepicker_am 0x7f0e00d4
int string material_timepicker_clock_mode_description 0x7f0e00d5
int string material_timepicker_hour 0x7f0e00d6
int string material_timepicker_minute 0x7f0e00d7
int string material_timepicker_pm 0x7f0e00d8
int string material_timepicker_select_time 0x7f0e00d9
int string material_timepicker_text_input_mode_description 0x7f0e00da
int string max_rotation_shifts 0x7f0e00db
int string min_rotation_shifts 0x7f0e00dc
int string mode_full 0x7f0e00dd
int string mode_simple 0x7f0e00de
int string mtrl_badge_numberless_content_description 0x7f0e00df
int string mtrl_chip_close_icon_content_description 0x7f0e00e0
int string mtrl_exceed_max_badge_number_content_description 0x7f0e00e1
int string mtrl_exceed_max_badge_number_suffix 0x7f0e00e2
int string mtrl_picker_a11y_next_month 0x7f0e00e3
int string mtrl_picker_a11y_prev_month 0x7f0e00e4
int string mtrl_picker_announce_current_selection 0x7f0e00e5
int string mtrl_picker_cancel 0x7f0e00e6
int string mtrl_picker_confirm 0x7f0e00e7
int string mtrl_picker_date_header_selected 0x7f0e00e8
int string mtrl_picker_date_header_title 0x7f0e00e9
int string mtrl_picker_date_header_unselected 0x7f0e00ea
int string mtrl_picker_day_of_week_column_header 0x7f0e00eb
int string mtrl_picker_invalid_format 0x7f0e00ec
int string mtrl_picker_invalid_format_example 0x7f0e00ed
int string mtrl_picker_invalid_format_use 0x7f0e00ee
int string mtrl_picker_invalid_range 0x7f0e00ef
int string mtrl_picker_navigate_to_year_description 0x7f0e00f0
int string mtrl_picker_out_of_range 0x7f0e00f1
int string mtrl_picker_range_header_only_end_selected 0x7f0e00f2
int string mtrl_picker_range_header_only_start_selected 0x7f0e00f3
int string mtrl_picker_range_header_selected 0x7f0e00f4
int string mtrl_picker_range_header_title 0x7f0e00f5
int string mtrl_picker_range_header_unselected 0x7f0e00f6
int string mtrl_picker_save 0x7f0e00f7
int string mtrl_picker_text_input_date_hint 0x7f0e00f8
int string mtrl_picker_text_input_date_range_end_hint 0x7f0e00f9
int string mtrl_picker_text_input_date_range_start_hint 0x7f0e00fa
int string mtrl_picker_text_input_day_abbr 0x7f0e00fb
int string mtrl_picker_text_input_month_abbr 0x7f0e00fc
int string mtrl_picker_text_input_year_abbr 0x7f0e00fd
int string mtrl_picker_toggle_to_calendar_input_mode 0x7f0e00fe
int string mtrl_picker_toggle_to_day_selection 0x7f0e00ff
int string mtrl_picker_toggle_to_text_input_mode 0x7f0e0100
int string mtrl_picker_toggle_to_year_selection 0x7f0e0101
int string multi_button_mode 0x7f0e0102
int string nav_app_bar_navigate_up_description 0x7f0e0103
int string nav_app_bar_open_drawer_description 0x7f0e0104
int string nav_home 0x7f0e0105
int string nav_notes 0x7f0e0106
int string nav_settings 0x7f0e0107
int string nav_shifts 0x7f0e0108
int string nav_statistics 0x7f0e0109
int string navigation_menu 0x7f0e010a
int string night_hours 0x7f0e010b
int string no 0x7f0e010c
int string no_active_shift 0x7f0e010d
int string no_notes 0x7f0e010e
int string not_selected 0x7f0e010f
int string note_content 0x7f0e0110
int string note_deleted 0x7f0e0111
int string note_priority 0x7f0e0112
int string note_saved 0x7f0e0113
int string note_title 0x7f0e0114
int string notes 0x7f0e0115
int string notes_display_2 0x7f0e0116
int string notes_display_3 0x7f0e0117
int string notes_display_5 0x7f0e0118
int string notes_display_limit 0x7f0e0119
int string off 0x7f0e011a
int string office_end_time 0x7f0e011b
int string ok 0x7f0e011c
int string on 0x7f0e011d
int string ot_holiday_rate 0x7f0e011e
int string ot_hours 0x7f0e011f
int string ot_saturday_rate 0x7f0e0120
int string ot_sunday_rate 0x7f0e0121
int string ot_weekday_rate 0x7f0e0122
int string other_settings 0x7f0e0123
int string password_toggle_content_description 0x7f0e0124
int string path_password_eye 0x7f0e0125
int string path_password_eye_mask_strike_through 0x7f0e0126
int string path_password_eye_mask_visible 0x7f0e0127
int string path_password_strike_through 0x7f0e0128
int string penalty_rounding 0x7f0e0129
int string priority_high 0x7f0e012a
int string priority_low 0x7f0e012b
int string priority_normal 0x7f0e012c
int string range_end 0x7f0e012d
int string range_start 0x7f0e012e
int string remind_after_end 0x7f0e012f
int string remind_before_start 0x7f0e0130
int string reminder_days 0x7f0e0131
int string reminder_settings 0x7f0e0132
int string reminder_time 0x7f0e0133
int string require_punch 0x7f0e0134
int string reset_settings 0x7f0e0135
int string restore_data 0x7f0e0136
int string rotation_ask_weekly 0x7f0e0137
int string rotation_auto 0x7f0e0138
int string rotation_disabled 0x7f0e0139
int string rotation_frequency 0x7f0e013a
int string rotation_mode_selection 0x7f0e013b
int string rotation_shifts_selected 0x7f0e013c
int string save 0x7f0e013d
int string search_bar_search 0x7f0e013e
int string search_menu_title 0x7f0e013f
int string select_rotation_shifts 0x7f0e0140
int string select_shift 0x7f0e0141
int string select_shifts_for_rotation 0x7f0e0142
int string selected 0x7f0e0143
int string settings 0x7f0e0144
int string settings_saved 0x7f0e0145
int string shared_location_status 0x7f0e0146
int string shift_basic_info 0x7f0e0147
int string shift_deleted 0x7f0e0148
int string shift_management 0x7f0e0149
int string shift_name 0x7f0e014a
int string shift_reminder_settings 0x7f0e014b
int string shift_rotation_mode 0x7f0e014c
int string shift_rotation_settings 0x7f0e014d
int string shift_saved 0x7f0e014e
int string shift_templates 0x7f0e014f
int string shift_templates_description 0x7f0e0150
int string shift_time_settings 0x7f0e0151
int string shift_validation_errors 0x7f0e0152
int string shift_work_settings 0x7f0e0153
int string show_punch 0x7f0e0154
int string snackbar_dismiss 0x7f0e0155
int string sort_by 0x7f0e0156
int string sort_by_date 0x7f0e0157
int string sort_by_name 0x7f0e0158
int string sort_by_priority 0x7f0e0159
int string standard_hours 0x7f0e015a
int string start_time 0x7f0e015b
int string statistics 0x7f0e015c
int string status 0x7f0e015d
int string status_bar_notification_info_overflow 0x7f0e015e
int string status_di_muon 0x7f0e015f
int string status_di_muon_ve_som 0x7f0e0160
int string status_du_cong 0x7f0e0161
int string status_nghi_benh 0x7f0e0162
int string status_nghi_le 0x7f0e0163
int string status_nghi_phep 0x7f0e0164
int string status_thieu_log 0x7f0e0165
int string status_unknown 0x7f0e0166
int string status_vang_mat 0x7f0e0167
int string status_ve_som 0x7f0e0168
int string success 0x7f0e0169
int string suggestions_available 0x7f0e016a
int string summary 0x7f0e016b
int string sunday_hours 0x7f0e016c
int string switch_role 0x7f0e016d
int string tab 0x7f0e016e
int string temperature 0x7f0e016f
int string template_add 0x7f0e0170
int string template_added 0x7f0e0171
int string template_exists 0x7f0e0172
int string template_percent 0x7f0e0173
int string theme 0x7f0e0174
int string theme_dark 0x7f0e0175
int string theme_light 0x7f0e0176
int string theme_system 0x7f0e0177
int string time_filter 0x7f0e0178
int string time_format 0x7f0e0179
int string time_format_12h 0x7f0e017a
int string time_format_24h 0x7f0e017b
int string time_picker_am 0x7f0e017c
int string time_picker_hour 0x7f0e017d
int string time_picker_hour_24h_suffix 0x7f0e017e
int string time_picker_hour_selection 0x7f0e017f
int string time_picker_hour_suffix 0x7f0e0180
int string time_picker_hour_text_field 0x7f0e0181
int string time_picker_minute 0x7f0e0182
int string time_picker_minute_selection 0x7f0e0183
int string time_picker_minute_suffix 0x7f0e0184
int string time_picker_minute_text_field 0x7f0e0185
int string time_picker_period_toggle_description 0x7f0e0186
int string time_picker_pm 0x7f0e0187
int string to_date 0x7f0e0188
int string tooltip_long_press_label 0x7f0e0189
int string tooltip_pane_description 0x7f0e018a
int string total_hours 0x7f0e018b
int string upcoming_notes 0x7f0e018c
int string view_all_notes 0x7f0e018d
int string weather_cache_time 0x7f0e018e
int string weather_channel_description 0x7f0e018f
int string weather_channel_name 0x7f0e0190
int string weather_condition 0x7f0e0191
int string weather_detail 0x7f0e0192
int string weather_forecast 0x7f0e0193
int string weather_home 0x7f0e0194
int string weather_location_management 0x7f0e0195
int string weather_office 0x7f0e0196
int string weather_settings 0x7f0e0197
int string weather_warning 0x7f0e0198
int string weather_warning_enabled 0x7f0e0199
int string weather_warning_title 0x7f0e019a
int string wind_speed 0x7f0e019b
int string yes 0x7f0e019c
int style AlertDialog_AppCompat 0x7f0f0000
int style AlertDialog_AppCompat_Light 0x7f0f0001
int style AndroidThemeColorAccentYellow 0x7f0f0002
int style Animation_AppCompat_Dialog 0x7f0f0003
int style Animation_AppCompat_DropDownUp 0x7f0f0004
int style Animation_AppCompat_Tooltip 0x7f0f0005
int style Animation_Design_BottomSheetDialog 0x7f0f0006
int style Animation_MaterialComponents_BottomSheetDialog 0x7f0f0007
int style Base_AlertDialog_AppCompat 0x7f0f0008
int style Base_AlertDialog_AppCompat_Light 0x7f0f0009
int style Base_Animation_AppCompat_Dialog 0x7f0f000a
int style Base_Animation_AppCompat_DropDownUp 0x7f0f000b
int style Base_Animation_AppCompat_Tooltip 0x7f0f000c
int style Base_CardView 0x7f0f000d
int style Base_DialogWindowTitle_AppCompat 0x7f0f000e
int style Base_DialogWindowTitleBackground_AppCompat 0x7f0f000f
int style Base_MaterialAlertDialog_MaterialComponents_Title_Icon 0x7f0f0010
int style Base_MaterialAlertDialog_MaterialComponents_Title_Panel 0x7f0f0011
int style Base_MaterialAlertDialog_MaterialComponents_Title_Text 0x7f0f0012
int style Base_TextAppearance_AppCompat 0x7f0f0013
int style Base_TextAppearance_AppCompat_Body1 0x7f0f0014
int style Base_TextAppearance_AppCompat_Body2 0x7f0f0015
int style Base_TextAppearance_AppCompat_Button 0x7f0f0016
int style Base_TextAppearance_AppCompat_Caption 0x7f0f0017
int style Base_TextAppearance_AppCompat_Display1 0x7f0f0018
int style Base_TextAppearance_AppCompat_Display2 0x7f0f0019
int style Base_TextAppearance_AppCompat_Display3 0x7f0f001a
int style Base_TextAppearance_AppCompat_Display4 0x7f0f001b
int style Base_TextAppearance_AppCompat_Headline 0x7f0f001c
int style Base_TextAppearance_AppCompat_Inverse 0x7f0f001d
int style Base_TextAppearance_AppCompat_Large 0x7f0f001e
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f0f001f
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f0f0020
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f0f0021
int style Base_TextAppearance_AppCompat_Medium 0x7f0f0022
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f0f0023
int style Base_TextAppearance_AppCompat_Menu 0x7f0f0024
int style Base_TextAppearance_AppCompat_SearchResult 0x7f0f0025
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f0f0026
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f0f0027
int style Base_TextAppearance_AppCompat_Small 0x7f0f0028
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f0f0029
int style Base_TextAppearance_AppCompat_Subhead 0x7f0f002a
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f0f002b
int style Base_TextAppearance_AppCompat_Title 0x7f0f002c
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f0f002d
int style Base_TextAppearance_AppCompat_Tooltip 0x7f0f002e
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f0f002f
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f0f0030
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f0f0031
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f0f0032
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f0f0033
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f0f0034
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f0f0035
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f0f0036
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f0f0037
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f0f0038
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f0f0039
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f0f003a
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f0f003b
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f0f003c
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f0f003d
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f0f003e
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f0f003f
int style Base_TextAppearance_MaterialComponents_Badge 0x7f0f0040
int style Base_TextAppearance_MaterialComponents_Button 0x7f0f0041
int style Base_TextAppearance_MaterialComponents_Headline6 0x7f0f0042
int style Base_TextAppearance_MaterialComponents_Subtitle2 0x7f0f0043
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f0f0044
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f0f0045
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f0f0046
int style Base_Theme_AppCompat 0x7f0f0047
int style Base_Theme_AppCompat_CompactMenu 0x7f0f0048
int style Base_Theme_AppCompat_Dialog 0x7f0f0049
int style Base_Theme_AppCompat_Dialog_Alert 0x7f0f004a
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f0f004b
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f0f004c
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f0f004d
int style Base_Theme_AppCompat_Light 0x7f0f004e
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f0f004f
int style Base_Theme_AppCompat_Light_Dialog 0x7f0f0050
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f0f0051
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f0f0052
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f0f0053
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f0f0054
int style Base_Theme_MaterialComponents 0x7f0f0055
int style Base_Theme_MaterialComponents_Bridge 0x7f0f0056
int style Base_Theme_MaterialComponents_CompactMenu 0x7f0f0057
int style Base_Theme_MaterialComponents_Dialog 0x7f0f0058
int style Base_Theme_MaterialComponents_Dialog_Alert 0x7f0f0059
int style Base_Theme_MaterialComponents_Dialog_Bridge 0x7f0f005a
int style Base_Theme_MaterialComponents_Dialog_FixedSize 0x7f0f005b
int style Base_Theme_MaterialComponents_Dialog_MinWidth 0x7f0f005c
int style Base_Theme_MaterialComponents_DialogWhenLarge 0x7f0f005d
int style Base_Theme_MaterialComponents_Light 0x7f0f005e
int style Base_Theme_MaterialComponents_Light_Bridge 0x7f0f005f
int style Base_Theme_MaterialComponents_Light_DarkActionBar 0x7f0f0060
int style Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f0f0061
int style Base_Theme_MaterialComponents_Light_Dialog 0x7f0f0062
int style Base_Theme_MaterialComponents_Light_Dialog_Alert 0x7f0f0063
int style Base_Theme_MaterialComponents_Light_Dialog_Bridge 0x7f0f0064
int style Base_Theme_MaterialComponents_Light_Dialog_FixedSize 0x7f0f0065
int style Base_Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f0f0066
int style Base_Theme_MaterialComponents_Light_DialogWhenLarge 0x7f0f0067
int style Base_ThemeOverlay_AppCompat 0x7f0f0068
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f0f0069
int style Base_ThemeOverlay_AppCompat_Dark 0x7f0f006a
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f0f006b
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f0f006c
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f0f006d
int style Base_ThemeOverlay_AppCompat_Light 0x7f0f006e
int style Base_ThemeOverlay_MaterialComponents_Dialog 0x7f0f006f
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f0f0070
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert_Framework 0x7f0f0071
int style Base_ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework 0x7f0f0072
int style Base_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f0f0073
int style Base_V14_Theme_MaterialComponents 0x7f0f0074
int style Base_V14_Theme_MaterialComponents_Bridge 0x7f0f0075
int style Base_V14_Theme_MaterialComponents_Dialog 0x7f0f0076
int style Base_V14_Theme_MaterialComponents_Dialog_Bridge 0x7f0f0077
int style Base_V14_Theme_MaterialComponents_Light 0x7f0f0078
int style Base_V14_Theme_MaterialComponents_Light_Bridge 0x7f0f0079
int style Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f0f007a
int style Base_V14_Theme_MaterialComponents_Light_Dialog 0x7f0f007b
int style Base_V14_Theme_MaterialComponents_Light_Dialog_Bridge 0x7f0f007c
int style Base_V14_ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f0f007d
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog 0x7f0f007e
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f0f007f
int style Base_V14_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f0f0080
int style Base_V21_Theme_AppCompat 0x7f0f0081
int style Base_V21_Theme_AppCompat_Dialog 0x7f0f0082
int style Base_V21_Theme_AppCompat_Light 0x7f0f0083
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f0f0084
int style Base_V21_Theme_MaterialComponents 0x7f0f0085
int style Base_V21_Theme_MaterialComponents_Dialog 0x7f0f0086
int style Base_V21_Theme_MaterialComponents_Light 0x7f0f0087
int style Base_V21_Theme_MaterialComponents_Light_Dialog 0x7f0f0088
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f0f0089
int style Base_V21_ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f0f008a
int style Base_V22_Theme_AppCompat 0x7f0f008b
int style Base_V22_Theme_AppCompat_Light 0x7f0f008c
int style Base_V23_Theme_AppCompat 0x7f0f008d
int style Base_V23_Theme_AppCompat_Light 0x7f0f008e
int style Base_V26_Theme_AppCompat 0x7f0f008f
int style Base_V26_Theme_AppCompat_Light 0x7f0f0090
int style Base_V26_Widget_AppCompat_Toolbar 0x7f0f0091
int style Base_V28_Theme_AppCompat 0x7f0f0092
int style Base_V28_Theme_AppCompat_Light 0x7f0f0093
int style Base_V7_Theme_AppCompat 0x7f0f0094
int style Base_V7_Theme_AppCompat_Dialog 0x7f0f0095
int style Base_V7_Theme_AppCompat_Light 0x7f0f0096
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f0f0097
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f0f0098
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f0f0099
int style Base_V7_Widget_AppCompat_EditText 0x7f0f009a
int style Base_V7_Widget_AppCompat_Toolbar 0x7f0f009b
int style Base_Widget_AppCompat_ActionBar 0x7f0f009c
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f0f009d
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f0f009e
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f0f009f
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f0f00a0
int style Base_Widget_AppCompat_ActionButton 0x7f0f00a1
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f0f00a2
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f0f00a3
int style Base_Widget_AppCompat_ActionMode 0x7f0f00a4
int style Base_Widget_AppCompat_ActivityChooserView 0x7f0f00a5
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f0f00a6
int style Base_Widget_AppCompat_Button 0x7f0f00a7
int style Base_Widget_AppCompat_Button_Borderless 0x7f0f00a8
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f0f00a9
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f0f00aa
int style Base_Widget_AppCompat_Button_Colored 0x7f0f00ab
int style Base_Widget_AppCompat_Button_Small 0x7f0f00ac
int style Base_Widget_AppCompat_ButtonBar 0x7f0f00ad
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f0f00ae
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f0f00af
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f0f00b0
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f0f00b1
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f0f00b2
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f0f00b3
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f0f00b4
int style Base_Widget_AppCompat_EditText 0x7f0f00b5
int style Base_Widget_AppCompat_ImageButton 0x7f0f00b6
int style Base_Widget_AppCompat_Light_ActionBar 0x7f0f00b7
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f0f00b8
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f0f00b9
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f0f00ba
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f0f00bb
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f0f00bc
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f0f00bd
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f0f00be
int style Base_Widget_AppCompat_ListMenuView 0x7f0f00bf
int style Base_Widget_AppCompat_ListPopupWindow 0x7f0f00c0
int style Base_Widget_AppCompat_ListView 0x7f0f00c1
int style Base_Widget_AppCompat_ListView_DropDown 0x7f0f00c2
int style Base_Widget_AppCompat_ListView_Menu 0x7f0f00c3
int style Base_Widget_AppCompat_PopupMenu 0x7f0f00c4
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f0f00c5
int style Base_Widget_AppCompat_PopupWindow 0x7f0f00c6
int style Base_Widget_AppCompat_ProgressBar 0x7f0f00c7
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f0f00c8
int style Base_Widget_AppCompat_RatingBar 0x7f0f00c9
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f0f00ca
int style Base_Widget_AppCompat_RatingBar_Small 0x7f0f00cb
int style Base_Widget_AppCompat_SearchView 0x7f0f00cc
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f0f00cd
int style Base_Widget_AppCompat_SeekBar 0x7f0f00ce
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f0f00cf
int style Base_Widget_AppCompat_Spinner 0x7f0f00d0
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f0f00d1
int style Base_Widget_AppCompat_TextView 0x7f0f00d2
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f0f00d3
int style Base_Widget_AppCompat_Toolbar 0x7f0f00d4
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f0f00d5
int style Base_Widget_Design_TabLayout 0x7f0f00d6
int style Base_Widget_MaterialComponents_AutoCompleteTextView 0x7f0f00d7
int style Base_Widget_MaterialComponents_CheckedTextView 0x7f0f00d8
int style Base_Widget_MaterialComponents_Chip 0x7f0f00d9
int style Base_Widget_MaterialComponents_MaterialCalendar_NavigationButton 0x7f0f00da
int style Base_Widget_MaterialComponents_PopupMenu 0x7f0f00db
int style Base_Widget_MaterialComponents_PopupMenu_ContextMenu 0x7f0f00dc
int style Base_Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x7f0f00dd
int style Base_Widget_MaterialComponents_PopupMenu_Overflow 0x7f0f00de
int style Base_Widget_MaterialComponents_Slider 0x7f0f00df
int style Base_Widget_MaterialComponents_Snackbar 0x7f0f00e0
int style Base_Widget_MaterialComponents_TextInputEditText 0x7f0f00e1
int style Base_Widget_MaterialComponents_TextInputLayout 0x7f0f00e2
int style Base_Widget_MaterialComponents_TextView 0x7f0f00e3
int style CardView 0x7f0f00e4
int style CardView_Dark 0x7f0f00e5
int style CardView_Light 0x7f0f00e6
int style DialogWindowTheme 0x7f0f00e7
int style EmptyTheme 0x7f0f00e8
int style FloatingDialogTheme 0x7f0f00e9
int style FloatingDialogWindowTheme 0x7f0f00ea
int style MaterialAlertDialog_MaterialComponents 0x7f0f00eb
int style MaterialAlertDialog_MaterialComponents_Body_Text 0x7f0f00ec
int style MaterialAlertDialog_MaterialComponents_Picker_Date_Calendar 0x7f0f00ed
int style MaterialAlertDialog_MaterialComponents_Picker_Date_Spinner 0x7f0f00ee
int style MaterialAlertDialog_MaterialComponents_Title_Icon 0x7f0f00ef
int style MaterialAlertDialog_MaterialComponents_Title_Icon_CenterStacked 0x7f0f00f0
int style MaterialAlertDialog_MaterialComponents_Title_Panel 0x7f0f00f1
int style MaterialAlertDialog_MaterialComponents_Title_Panel_CenterStacked 0x7f0f00f2
int style MaterialAlertDialog_MaterialComponents_Title_Text 0x7f0f00f3
int style MaterialAlertDialog_MaterialComponents_Title_Text_CenterStacked 0x7f0f00f4
int style Platform_AppCompat 0x7f0f00f5
int style Platform_AppCompat_Light 0x7f0f00f6
int style Platform_MaterialComponents 0x7f0f00f7
int style Platform_MaterialComponents_Dialog 0x7f0f00f8
int style Platform_MaterialComponents_Light 0x7f0f00f9
int style Platform_MaterialComponents_Light_Dialog 0x7f0f00fa
int style Platform_ThemeOverlay_AppCompat 0x7f0f00fb
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f0f00fc
int style Platform_ThemeOverlay_AppCompat_Light 0x7f0f00fd
int style Platform_V21_AppCompat 0x7f0f00fe
int style Platform_V21_AppCompat_Light 0x7f0f00ff
int style Platform_V25_AppCompat 0x7f0f0100
int style Platform_V25_AppCompat_Light 0x7f0f0101
int style Platform_Widget_AppCompat_Spinner 0x7f0f0102
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f0f0103
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f0f0104
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f0f0105
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f0f0106
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f0f0107
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x7f0f0108
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x7f0f0109
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f0f010a
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x7f0f010b
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f0f010c
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f0f010d
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f0f010e
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f0f010f
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f0f0110
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f0f0111
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f0f0112
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f0f0113
int style ShapeAppearance_MaterialComponents 0x7f0f0114
int style ShapeAppearance_MaterialComponents_LargeComponent 0x7f0f0115
int style ShapeAppearance_MaterialComponents_MediumComponent 0x7f0f0116
int style ShapeAppearance_MaterialComponents_SmallComponent 0x7f0f0117
int style ShapeAppearance_MaterialComponents_Test 0x7f0f0118
int style ShapeAppearance_MaterialComponents_Tooltip 0x7f0f0119
int style ShapeAppearanceOverlay 0x7f0f011a
int style ShapeAppearanceOverlay_BottomLeftDifferentCornerSize 0x7f0f011b
int style ShapeAppearanceOverlay_BottomRightCut 0x7f0f011c
int style ShapeAppearanceOverlay_Cut 0x7f0f011d
int style ShapeAppearanceOverlay_DifferentCornerSize 0x7f0f011e
int style ShapeAppearanceOverlay_MaterialComponents_BottomSheet 0x7f0f011f
int style ShapeAppearanceOverlay_MaterialComponents_Chip 0x7f0f0120
int style ShapeAppearanceOverlay_MaterialComponents_ExtendedFloatingActionButton 0x7f0f0121
int style ShapeAppearanceOverlay_MaterialComponents_FloatingActionButton 0x7f0f0122
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day 0x7f0f0123
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Window_Fullscreen 0x7f0f0124
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Year 0x7f0f0125
int style ShapeAppearanceOverlay_MaterialComponents_TextInputLayout_FilledBox 0x7f0f0126
int style ShapeAppearanceOverlay_TopLeftCut 0x7f0f0127
int style ShapeAppearanceOverlay_TopRightDifferentCornerSize 0x7f0f0128
int style Test_ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day 0x7f0f0129
int style Test_Theme_MaterialComponents_MaterialCalendar 0x7f0f012a
int style Test_Widget_MaterialComponents_MaterialCalendar 0x7f0f012b
int style Test_Widget_MaterialComponents_MaterialCalendar_Day 0x7f0f012c
int style Test_Widget_MaterialComponents_MaterialCalendar_Day_Selected 0x7f0f012d
int style TestStyleWithLineHeight 0x7f0f012e
int style TestStyleWithLineHeightAppearance 0x7f0f012f
int style TestStyleWithThemeLineHeightAttribute 0x7f0f0130
int style TestStyleWithoutLineHeight 0x7f0f0131
int style TestThemeWithLineHeight 0x7f0f0132
int style TestThemeWithLineHeightDisabled 0x7f0f0133
int style TextAppearance_AppCompat 0x7f0f0134
int style TextAppearance_AppCompat_Body1 0x7f0f0135
int style TextAppearance_AppCompat_Body2 0x7f0f0136
int style TextAppearance_AppCompat_Button 0x7f0f0137
int style TextAppearance_AppCompat_Caption 0x7f0f0138
int style TextAppearance_AppCompat_Display1 0x7f0f0139
int style TextAppearance_AppCompat_Display2 0x7f0f013a
int style TextAppearance_AppCompat_Display3 0x7f0f013b
int style TextAppearance_AppCompat_Display4 0x7f0f013c
int style TextAppearance_AppCompat_Headline 0x7f0f013d
int style TextAppearance_AppCompat_Inverse 0x7f0f013e
int style TextAppearance_AppCompat_Large 0x7f0f013f
int style TextAppearance_AppCompat_Large_Inverse 0x7f0f0140
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f0f0141
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f0f0142
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f0f0143
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f0f0144
int style TextAppearance_AppCompat_Medium 0x7f0f0145
int style TextAppearance_AppCompat_Medium_Inverse 0x7f0f0146
int style TextAppearance_AppCompat_Menu 0x7f0f0147
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f0f0148
int style TextAppearance_AppCompat_SearchResult_Title 0x7f0f0149
int style TextAppearance_AppCompat_Small 0x7f0f014a
int style TextAppearance_AppCompat_Small_Inverse 0x7f0f014b
int style TextAppearance_AppCompat_Subhead 0x7f0f014c
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f0f014d
int style TextAppearance_AppCompat_Title 0x7f0f014e
int style TextAppearance_AppCompat_Title_Inverse 0x7f0f014f
int style TextAppearance_AppCompat_Tooltip 0x7f0f0150
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f0f0151
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f0f0152
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f0f0153
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f0f0154
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f0f0155
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f0f0156
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f0f0157
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f0f0158
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f0f0159
int style TextAppearance_AppCompat_Widget_Button 0x7f0f015a
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f0f015b
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f0f015c
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f0f015d
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f0f015e
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f0f015f
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f0f0160
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f0f0161
int style TextAppearance_AppCompat_Widget_Switch 0x7f0f0162
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f0f0163
int style TextAppearance_Compat_Notification 0x7f0f0164
int style TextAppearance_Compat_Notification_Info 0x7f0f0165
int style TextAppearance_Compat_Notification_Line2 0x7f0f0166
int style TextAppearance_Compat_Notification_Time 0x7f0f0167
int style TextAppearance_Compat_Notification_Title 0x7f0f0168
int style TextAppearance_Design_CollapsingToolbar_Expanded 0x7f0f0169
int style TextAppearance_Design_Counter 0x7f0f016a
int style TextAppearance_Design_Counter_Overflow 0x7f0f016b
int style TextAppearance_Design_Error 0x7f0f016c
int style TextAppearance_Design_HelperText 0x7f0f016d
int style TextAppearance_Design_Hint 0x7f0f016e
int style TextAppearance_Design_Placeholder 0x7f0f016f
int style TextAppearance_Design_Prefix 0x7f0f0170
int style TextAppearance_Design_Snackbar_Message 0x7f0f0171
int style TextAppearance_Design_Suffix 0x7f0f0172
int style TextAppearance_Design_Tab 0x7f0f0173
int style TextAppearance_MaterialComponents_Badge 0x7f0f0174
int style TextAppearance_MaterialComponents_Body1 0x7f0f0175
int style TextAppearance_MaterialComponents_Body2 0x7f0f0176
int style TextAppearance_MaterialComponents_Button 0x7f0f0177
int style TextAppearance_MaterialComponents_Caption 0x7f0f0178
int style TextAppearance_MaterialComponents_Chip 0x7f0f0179
int style TextAppearance_MaterialComponents_Headline1 0x7f0f017a
int style TextAppearance_MaterialComponents_Headline2 0x7f0f017b
int style TextAppearance_MaterialComponents_Headline3 0x7f0f017c
int style TextAppearance_MaterialComponents_Headline4 0x7f0f017d
int style TextAppearance_MaterialComponents_Headline5 0x7f0f017e
int style TextAppearance_MaterialComponents_Headline6 0x7f0f017f
int style TextAppearance_MaterialComponents_Overline 0x7f0f0180
int style TextAppearance_MaterialComponents_Subtitle1 0x7f0f0181
int style TextAppearance_MaterialComponents_Subtitle2 0x7f0f0182
int style TextAppearance_MaterialComponents_TimePicker_Title 0x7f0f0183
int style TextAppearance_MaterialComponents_Tooltip 0x7f0f0184
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f0f0185
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f0f0186
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f0f0187
int style Theme_AppCompat 0x7f0f0188
int style Theme_AppCompat_CompactMenu 0x7f0f0189
int style Theme_AppCompat_DayNight 0x7f0f018a
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f0f018b
int style Theme_AppCompat_DayNight_Dialog 0x7f0f018c
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f0f018d
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f0f018e
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f0f018f
int style Theme_AppCompat_DayNight_NoActionBar 0x7f0f0190
int style Theme_AppCompat_Dialog 0x7f0f0191
int style Theme_AppCompat_Dialog_Alert 0x7f0f0192
int style Theme_AppCompat_Dialog_MinWidth 0x7f0f0193
int style Theme_AppCompat_DialogWhenLarge 0x7f0f0194
int style Theme_AppCompat_Empty 0x7f0f0195
int style Theme_AppCompat_Light 0x7f0f0196
int style Theme_AppCompat_Light_DarkActionBar 0x7f0f0197
int style Theme_AppCompat_Light_Dialog 0x7f0f0198
int style Theme_AppCompat_Light_Dialog_Alert 0x7f0f0199
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f0f019a
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f0f019b
int style Theme_AppCompat_Light_NoActionBar 0x7f0f019c
int style Theme_AppCompat_NoActionBar 0x7f0f019d
int style Theme_Design 0x7f0f019e
int style Theme_Design_BottomSheetDialog 0x7f0f019f
int style Theme_Design_Light 0x7f0f01a0
int style Theme_Design_Light_BottomSheetDialog 0x7f0f01a1
int style Theme_Design_Light_NoActionBar 0x7f0f01a2
int style Theme_Design_NoActionBar 0x7f0f01a3
int style Theme_MaterialComponents 0x7f0f01a4
int style Theme_MaterialComponents_BottomSheetDialog 0x7f0f01a5
int style Theme_MaterialComponents_Bridge 0x7f0f01a6
int style Theme_MaterialComponents_CompactMenu 0x7f0f01a7
int style Theme_MaterialComponents_DayNight 0x7f0f01a8
int style Theme_MaterialComponents_DayNight_BottomSheetDialog 0x7f0f01a9
int style Theme_MaterialComponents_DayNight_Bridge 0x7f0f01aa
int style Theme_MaterialComponents_DayNight_DarkActionBar 0x7f0f01ab
int style Theme_MaterialComponents_DayNight_DarkActionBar_Bridge 0x7f0f01ac
int style Theme_MaterialComponents_DayNight_Dialog 0x7f0f01ad
int style Theme_MaterialComponents_DayNight_Dialog_Alert 0x7f0f01ae
int style Theme_MaterialComponents_DayNight_Dialog_Alert_Bridge 0x7f0f01af
int style Theme_MaterialComponents_DayNight_Dialog_Bridge 0x7f0f01b0
int style Theme_MaterialComponents_DayNight_Dialog_FixedSize 0x7f0f01b1
int style Theme_MaterialComponents_DayNight_Dialog_FixedSize_Bridge 0x7f0f01b2
int style Theme_MaterialComponents_DayNight_Dialog_MinWidth 0x7f0f01b3
int style Theme_MaterialComponents_DayNight_Dialog_MinWidth_Bridge 0x7f0f01b4
int style Theme_MaterialComponents_DayNight_DialogWhenLarge 0x7f0f01b5
int style Theme_MaterialComponents_DayNight_NoActionBar 0x7f0f01b6
int style Theme_MaterialComponents_DayNight_NoActionBar_Bridge 0x7f0f01b7
int style Theme_MaterialComponents_Dialog 0x7f0f01b8
int style Theme_MaterialComponents_Dialog_Alert 0x7f0f01b9
int style Theme_MaterialComponents_Dialog_Alert_Bridge 0x7f0f01ba
int style Theme_MaterialComponents_Dialog_Bridge 0x7f0f01bb
int style Theme_MaterialComponents_Dialog_FixedSize 0x7f0f01bc
int style Theme_MaterialComponents_Dialog_FixedSize_Bridge 0x7f0f01bd
int style Theme_MaterialComponents_Dialog_MinWidth 0x7f0f01be
int style Theme_MaterialComponents_Dialog_MinWidth_Bridge 0x7f0f01bf
int style Theme_MaterialComponents_DialogWhenLarge 0x7f0f01c0
int style Theme_MaterialComponents_Light 0x7f0f01c1
int style Theme_MaterialComponents_Light_BarSize 0x7f0f01c2
int style Theme_MaterialComponents_Light_BottomSheetDialog 0x7f0f01c3
int style Theme_MaterialComponents_Light_Bridge 0x7f0f01c4
int style Theme_MaterialComponents_Light_DarkActionBar 0x7f0f01c5
int style Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f0f01c6
int style Theme_MaterialComponents_Light_Dialog 0x7f0f01c7
int style Theme_MaterialComponents_Light_Dialog_Alert 0x7f0f01c8
int style Theme_MaterialComponents_Light_Dialog_Alert_Bridge 0x7f0f01c9
int style Theme_MaterialComponents_Light_Dialog_Bridge 0x7f0f01ca
int style Theme_MaterialComponents_Light_Dialog_FixedSize 0x7f0f01cb
int style Theme_MaterialComponents_Light_Dialog_FixedSize_Bridge 0x7f0f01cc
int style Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f0f01cd
int style Theme_MaterialComponents_Light_Dialog_MinWidth_Bridge 0x7f0f01ce
int style Theme_MaterialComponents_Light_DialogWhenLarge 0x7f0f01cf
int style Theme_MaterialComponents_Light_LargeTouch 0x7f0f01d0
int style Theme_MaterialComponents_Light_NoActionBar 0x7f0f01d1
int style Theme_MaterialComponents_Light_NoActionBar_Bridge 0x7f0f01d2
int style Theme_MaterialComponents_NoActionBar 0x7f0f01d3
int style Theme_MaterialComponents_NoActionBar_Bridge 0x7f0f01d4
int style Theme_Workly 0x7f0f01d5
int style Theme_Workly_Alarm 0x7f0f01d6
int style ThemeOverlay_AppCompat 0x7f0f01d7
int style ThemeOverlay_AppCompat_ActionBar 0x7f0f01d8
int style ThemeOverlay_AppCompat_Dark 0x7f0f01d9
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f0f01da
int style ThemeOverlay_AppCompat_DayNight 0x7f0f01db
int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x7f0f01dc
int style ThemeOverlay_AppCompat_Dialog 0x7f0f01dd
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f0f01de
int style ThemeOverlay_AppCompat_Light 0x7f0f01df
int style ThemeOverlay_Design_TextInputEditText 0x7f0f01e0
int style ThemeOverlay_MaterialComponents 0x7f0f01e1
int style ThemeOverlay_MaterialComponents_ActionBar 0x7f0f01e2
int style ThemeOverlay_MaterialComponents_ActionBar_Primary 0x7f0f01e3
int style ThemeOverlay_MaterialComponents_ActionBar_Surface 0x7f0f01e4
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView 0x7f0f01e5
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox 0x7f0f01e6
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x7f0f01e7
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x7f0f01e8
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x7f0f01e9
int style ThemeOverlay_MaterialComponents_BottomAppBar_Primary 0x7f0f01ea
int style ThemeOverlay_MaterialComponents_BottomAppBar_Surface 0x7f0f01eb
int style ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f0f01ec
int style ThemeOverlay_MaterialComponents_Dark 0x7f0f01ed
int style ThemeOverlay_MaterialComponents_Dark_ActionBar 0x7f0f01ee
int style ThemeOverlay_MaterialComponents_DayNight_BottomSheetDialog 0x7f0f01ef
int style ThemeOverlay_MaterialComponents_Dialog 0x7f0f01f0
int style ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f0f01f1
int style ThemeOverlay_MaterialComponents_Dialog_Alert_Framework 0x7f0f01f2
int style ThemeOverlay_MaterialComponents_Light 0x7f0f01f3
int style ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework 0x7f0f01f4
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f0f01f5
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Centered 0x7f0f01f6
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date 0x7f0f01f7
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Calendar 0x7f0f01f8
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text 0x7f0f01f9
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text_Day 0x7f0f01fa
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Spinner 0x7f0f01fb
int style ThemeOverlay_MaterialComponents_MaterialCalendar 0x7f0f01fc
int style ThemeOverlay_MaterialComponents_MaterialCalendar_Fullscreen 0x7f0f01fd
int style ThemeOverlay_MaterialComponents_TextInputEditText 0x7f0f01fe
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox 0x7f0f01ff
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f0f0200
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox 0x7f0f0201
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f0f0202
int style ThemeOverlay_MaterialComponents_TimePicker 0x7f0f0203
int style ThemeOverlay_MaterialComponents_TimePicker_Display 0x7f0f0204
int style ThemeOverlay_MaterialComponents_Toolbar_Primary 0x7f0f0205
int style ThemeOverlay_MaterialComponents_Toolbar_Surface 0x7f0f0206
int style ThemeOverlayColorAccentRed 0x7f0f0207
int style Widget_AppCompat_ActionBar 0x7f0f0208
int style Widget_AppCompat_ActionBar_Solid 0x7f0f0209
int style Widget_AppCompat_ActionBar_TabBar 0x7f0f020a
int style Widget_AppCompat_ActionBar_TabText 0x7f0f020b
int style Widget_AppCompat_ActionBar_TabView 0x7f0f020c
int style Widget_AppCompat_ActionButton 0x7f0f020d
int style Widget_AppCompat_ActionButton_CloseMode 0x7f0f020e
int style Widget_AppCompat_ActionButton_Overflow 0x7f0f020f
int style Widget_AppCompat_ActionMode 0x7f0f0210
int style Widget_AppCompat_ActivityChooserView 0x7f0f0211
int style Widget_AppCompat_AutoCompleteTextView 0x7f0f0212
int style Widget_AppCompat_Button 0x7f0f0213
int style Widget_AppCompat_Button_Borderless 0x7f0f0214
int style Widget_AppCompat_Button_Borderless_Colored 0x7f0f0215
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f0f0216
int style Widget_AppCompat_Button_Colored 0x7f0f0217
int style Widget_AppCompat_Button_Small 0x7f0f0218
int style Widget_AppCompat_ButtonBar 0x7f0f0219
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f0f021a
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f0f021b
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f0f021c
int style Widget_AppCompat_CompoundButton_Switch 0x7f0f021d
int style Widget_AppCompat_DrawerArrowToggle 0x7f0f021e
int style Widget_AppCompat_DropDownItem_Spinner 0x7f0f021f
int style Widget_AppCompat_EditText 0x7f0f0220
int style Widget_AppCompat_ImageButton 0x7f0f0221
int style Widget_AppCompat_Light_ActionBar 0x7f0f0222
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f0f0223
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f0f0224
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f0f0225
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f0f0226
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f0f0227
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f0f0228
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f0f0229
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f0f022a
int style Widget_AppCompat_Light_ActionButton 0x7f0f022b
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f0f022c
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f0f022d
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f0f022e
int style Widget_AppCompat_Light_ActivityChooserView 0x7f0f022f
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f0f0230
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f0f0231
int style Widget_AppCompat_Light_ListPopupWindow 0x7f0f0232
int style Widget_AppCompat_Light_ListView_DropDown 0x7f0f0233
int style Widget_AppCompat_Light_PopupMenu 0x7f0f0234
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f0f0235
int style Widget_AppCompat_Light_SearchView 0x7f0f0236
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f0f0237
int style Widget_AppCompat_ListMenuView 0x7f0f0238
int style Widget_AppCompat_ListPopupWindow 0x7f0f0239
int style Widget_AppCompat_ListView 0x7f0f023a
int style Widget_AppCompat_ListView_DropDown 0x7f0f023b
int style Widget_AppCompat_ListView_Menu 0x7f0f023c
int style Widget_AppCompat_PopupMenu 0x7f0f023d
int style Widget_AppCompat_PopupMenu_Overflow 0x7f0f023e
int style Widget_AppCompat_PopupWindow 0x7f0f023f
int style Widget_AppCompat_ProgressBar 0x7f0f0240
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f0f0241
int style Widget_AppCompat_RatingBar 0x7f0f0242
int style Widget_AppCompat_RatingBar_Indicator 0x7f0f0243
int style Widget_AppCompat_RatingBar_Small 0x7f0f0244
int style Widget_AppCompat_SearchView 0x7f0f0245
int style Widget_AppCompat_SearchView_ActionBar 0x7f0f0246
int style Widget_AppCompat_SeekBar 0x7f0f0247
int style Widget_AppCompat_SeekBar_Discrete 0x7f0f0248
int style Widget_AppCompat_Spinner 0x7f0f0249
int style Widget_AppCompat_Spinner_DropDown 0x7f0f024a
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f0f024b
int style Widget_AppCompat_Spinner_Underlined 0x7f0f024c
int style Widget_AppCompat_TextView 0x7f0f024d
int style Widget_AppCompat_TextView_SpinnerItem 0x7f0f024e
int style Widget_AppCompat_Toolbar 0x7f0f024f
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f0f0250
int style Widget_Compat_NotificationActionContainer 0x7f0f0251
int style Widget_Compat_NotificationActionText 0x7f0f0252
int style Widget_Design_AppBarLayout 0x7f0f0253
int style Widget_Design_BottomNavigationView 0x7f0f0254
int style Widget_Design_BottomSheet_Modal 0x7f0f0255
int style Widget_Design_CollapsingToolbar 0x7f0f0256
int style Widget_Design_FloatingActionButton 0x7f0f0257
int style Widget_Design_NavigationView 0x7f0f0258
int style Widget_Design_ScrimInsetsFrameLayout 0x7f0f0259
int style Widget_Design_Snackbar 0x7f0f025a
int style Widget_Design_TabLayout 0x7f0f025b
int style Widget_Design_TextInputEditText 0x7f0f025c
int style Widget_Design_TextInputLayout 0x7f0f025d
int style Widget_MaterialComponents_ActionBar_Primary 0x7f0f025e
int style Widget_MaterialComponents_ActionBar_PrimarySurface 0x7f0f025f
int style Widget_MaterialComponents_ActionBar_Solid 0x7f0f0260
int style Widget_MaterialComponents_ActionBar_Surface 0x7f0f0261
int style Widget_MaterialComponents_AppBarLayout_Primary 0x7f0f0262
int style Widget_MaterialComponents_AppBarLayout_PrimarySurface 0x7f0f0263
int style Widget_MaterialComponents_AppBarLayout_Surface 0x7f0f0264
int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox 0x7f0f0265
int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x7f0f0266
int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x7f0f0267
int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x7f0f0268
int style Widget_MaterialComponents_Badge 0x7f0f0269
int style Widget_MaterialComponents_BottomAppBar 0x7f0f026a
int style Widget_MaterialComponents_BottomAppBar_Colored 0x7f0f026b
int style Widget_MaterialComponents_BottomAppBar_PrimarySurface 0x7f0f026c
int style Widget_MaterialComponents_BottomNavigationView 0x7f0f026d
int style Widget_MaterialComponents_BottomNavigationView_Colored 0x7f0f026e
int style Widget_MaterialComponents_BottomNavigationView_PrimarySurface 0x7f0f026f
int style Widget_MaterialComponents_BottomSheet 0x7f0f0270
int style Widget_MaterialComponents_BottomSheet_Modal 0x7f0f0271
int style Widget_MaterialComponents_Button 0x7f0f0272
int style Widget_MaterialComponents_Button_Icon 0x7f0f0273
int style Widget_MaterialComponents_Button_OutlinedButton 0x7f0f0274
int style Widget_MaterialComponents_Button_OutlinedButton_Icon 0x7f0f0275
int style Widget_MaterialComponents_Button_TextButton 0x7f0f0276
int style Widget_MaterialComponents_Button_TextButton_Dialog 0x7f0f0277
int style Widget_MaterialComponents_Button_TextButton_Dialog_Flush 0x7f0f0278
int style Widget_MaterialComponents_Button_TextButton_Dialog_Icon 0x7f0f0279
int style Widget_MaterialComponents_Button_TextButton_Icon 0x7f0f027a
int style Widget_MaterialComponents_Button_TextButton_Snackbar 0x7f0f027b
int style Widget_MaterialComponents_Button_UnelevatedButton 0x7f0f027c
int style Widget_MaterialComponents_Button_UnelevatedButton_Icon 0x7f0f027d
int style Widget_MaterialComponents_CardView 0x7f0f027e
int style Widget_MaterialComponents_CheckedTextView 0x7f0f027f
int style Widget_MaterialComponents_Chip_Action 0x7f0f0280
int style Widget_MaterialComponents_Chip_Choice 0x7f0f0281
int style Widget_MaterialComponents_Chip_Entry 0x7f0f0282
int style Widget_MaterialComponents_Chip_Filter 0x7f0f0283
int style Widget_MaterialComponents_ChipGroup 0x7f0f0284
int style Widget_MaterialComponents_CircularProgressIndicator 0x7f0f0285
int style Widget_MaterialComponents_CircularProgressIndicator_ExtraSmall 0x7f0f0286
int style Widget_MaterialComponents_CircularProgressIndicator_Medium 0x7f0f0287
int style Widget_MaterialComponents_CircularProgressIndicator_Small 0x7f0f0288
int style Widget_MaterialComponents_CollapsingToolbar 0x7f0f0289
int style Widget_MaterialComponents_CompoundButton_CheckBox 0x7f0f028a
int style Widget_MaterialComponents_CompoundButton_RadioButton 0x7f0f028b
int style Widget_MaterialComponents_CompoundButton_Switch 0x7f0f028c
int style Widget_MaterialComponents_ExtendedFloatingActionButton 0x7f0f028d
int style Widget_MaterialComponents_ExtendedFloatingActionButton_Icon 0x7f0f028e
int style Widget_MaterialComponents_FloatingActionButton 0x7f0f028f
int style Widget_MaterialComponents_Light_ActionBar_Solid 0x7f0f0290
int style Widget_MaterialComponents_LinearProgressIndicator 0x7f0f0291
int style Widget_MaterialComponents_MaterialButtonToggleGroup 0x7f0f0292
int style Widget_MaterialComponents_MaterialCalendar 0x7f0f0293
int style Widget_MaterialComponents_MaterialCalendar_Day 0x7f0f0294
int style Widget_MaterialComponents_MaterialCalendar_Day_Invalid 0x7f0f0295
int style Widget_MaterialComponents_MaterialCalendar_Day_Selected 0x7f0f0296
int style Widget_MaterialComponents_MaterialCalendar_Day_Today 0x7f0f0297
int style Widget_MaterialComponents_MaterialCalendar_DayTextView 0x7f0f0298
int style Widget_MaterialComponents_MaterialCalendar_Fullscreen 0x7f0f0299
int style Widget_MaterialComponents_MaterialCalendar_HeaderCancelButton 0x7f0f029a
int style Widget_MaterialComponents_MaterialCalendar_HeaderConfirmButton 0x7f0f029b
int style Widget_MaterialComponents_MaterialCalendar_HeaderDivider 0x7f0f029c
int style Widget_MaterialComponents_MaterialCalendar_HeaderLayout 0x7f0f029d
int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection 0x7f0f029e
int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection_Fullscreen 0x7f0f029f
int style Widget_MaterialComponents_MaterialCalendar_HeaderTitle 0x7f0f02a0
int style Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton 0x7f0f02a1
int style Widget_MaterialComponents_MaterialCalendar_Item 0x7f0f02a2
int style Widget_MaterialComponents_MaterialCalendar_MonthNavigationButton 0x7f0f02a3
int style Widget_MaterialComponents_MaterialCalendar_MonthTextView 0x7f0f02a4
int style Widget_MaterialComponents_MaterialCalendar_Year 0x7f0f02a5
int style Widget_MaterialComponents_MaterialCalendar_Year_Selected 0x7f0f02a6
int style Widget_MaterialComponents_MaterialCalendar_Year_Today 0x7f0f02a7
int style Widget_MaterialComponents_MaterialCalendar_YearNavigationButton 0x7f0f02a8
int style Widget_MaterialComponents_NavigationRailView 0x7f0f02a9
int style Widget_MaterialComponents_NavigationRailView_Colored 0x7f0f02aa
int style Widget_MaterialComponents_NavigationRailView_Colored_Compact 0x7f0f02ab
int style Widget_MaterialComponents_NavigationRailView_Compact 0x7f0f02ac
int style Widget_MaterialComponents_NavigationRailView_PrimarySurface 0x7f0f02ad
int style Widget_MaterialComponents_NavigationView 0x7f0f02ae
int style Widget_MaterialComponents_PopupMenu 0x7f0f02af
int style Widget_MaterialComponents_PopupMenu_ContextMenu 0x7f0f02b0
int style Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x7f0f02b1
int style Widget_MaterialComponents_PopupMenu_Overflow 0x7f0f02b2
int style Widget_MaterialComponents_ProgressIndicator 0x7f0f02b3
int style Widget_MaterialComponents_ShapeableImageView 0x7f0f02b4
int style Widget_MaterialComponents_Slider 0x7f0f02b5
int style Widget_MaterialComponents_Snackbar 0x7f0f02b6
int style Widget_MaterialComponents_Snackbar_FullWidth 0x7f0f02b7
int style Widget_MaterialComponents_Snackbar_TextView 0x7f0f02b8
int style Widget_MaterialComponents_TabLayout 0x7f0f02b9
int style Widget_MaterialComponents_TabLayout_Colored 0x7f0f02ba
int style Widget_MaterialComponents_TabLayout_PrimarySurface 0x7f0f02bb
int style Widget_MaterialComponents_TextInputEditText_FilledBox 0x7f0f02bc
int style Widget_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f0f02bd
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox 0x7f0f02be
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f0f02bf
int style Widget_MaterialComponents_TextInputLayout_FilledBox 0x7f0f02c0
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense 0x7f0f02c1
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu 0x7f0f02c2
int style Widget_MaterialComponents_TextInputLayout_FilledBox_ExposedDropdownMenu 0x7f0f02c3
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox 0x7f0f02c4
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense 0x7f0f02c5
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu 0x7f0f02c6
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_ExposedDropdownMenu 0x7f0f02c7
int style Widget_MaterialComponents_TextView 0x7f0f02c8
int style Widget_MaterialComponents_TimePicker 0x7f0f02c9
int style Widget_MaterialComponents_TimePicker_Button 0x7f0f02ca
int style Widget_MaterialComponents_TimePicker_Clock 0x7f0f02cb
int style Widget_MaterialComponents_TimePicker_Display 0x7f0f02cc
int style Widget_MaterialComponents_TimePicker_Display_TextInputEditText 0x7f0f02cd
int style Widget_MaterialComponents_TimePicker_ImageButton 0x7f0f02ce
int style Widget_MaterialComponents_TimePicker_ImageButton_ShapeAppearance 0x7f0f02cf
int style Widget_MaterialComponents_Toolbar 0x7f0f02d0
int style Widget_MaterialComponents_Toolbar_Primary 0x7f0f02d1
int style Widget_MaterialComponents_Toolbar_PrimarySurface 0x7f0f02d2
int style Widget_MaterialComponents_Toolbar_Surface 0x7f0f02d3
int style Widget_MaterialComponents_Tooltip 0x7f0f02d4
int style Widget_Support_CoordinatorLayout 0x7f0f02d5
int[] styleable ActionBar { 0x7f03003f, 0x7f030046, 0x7f030047, 0x7f0300d6, 0x7f0300d7, 0x7f0300d8, 0x7f0300d9, 0x7f0300da, 0x7f0300db, 0x7f030101, 0x7f030115, 0x7f030116, 0x7f03012f, 0x7f030189, 0x7f030190, 0x7f030196, 0x7f030197, 0x7f03019a, 0x7f0301a5, 0x7f0301b7, 0x7f030226, 0x7f030276, 0x7f0302ab, 0x7f0302b3, 0x7f0302b4, 0x7f03030c, 0x7f030310, 0x7f03036b, 0x7f030377 }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x010100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x0101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView { }
int[] styleable ActionMode { 0x7f03003f, 0x7f030046, 0x7f0300b1, 0x7f030189, 0x7f030310, 0x7f030377 }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f030145, 0x7f0301ab }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable ActivityFilter { 0x7f030023, 0x7f030025 }
int styleable ActivityFilter_activityAction 0
int styleable ActivityFilter_activityName 1
int[] styleable ActivityNavigator { 0x01010003, 0x7f030000, 0x7f030104, 0x7f030105, 0x7f030335 }
int styleable ActivityNavigator_android_name 0
int styleable ActivityNavigator_action 1
int styleable ActivityNavigator_data 2
int styleable ActivityNavigator_dataPattern 3
int styleable ActivityNavigator_targetPackage 4
int[] styleable ActivityRule { 0x7f03002e }
int styleable ActivityRule_alwaysExpand 0
int[] styleable AlertDialog { 0x010100f2, 0x7f030075, 0x7f030076, 0x7f03021b, 0x7f03021c, 0x7f030271, 0x7f0302e3, 0x7f0302e5 }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable AppBarLayout { 0x010100d4, 0x0101048f, 0x01010540, 0x7f03012f, 0x7f030146, 0x7f030211, 0x7f030212, 0x7f030306 }
int styleable AppBarLayout_android_background 0
int styleable AppBarLayout_android_touchscreenBlocksFocus 1
int styleable AppBarLayout_android_keyboardNavigationCluster 2
int styleable AppBarLayout_elevation 3
int styleable AppBarLayout_expanded 4
int styleable AppBarLayout_liftOnScroll 5
int styleable AppBarLayout_liftOnScrollTargetViewId 6
int styleable AppBarLayout_statusBarForeground 7
int[] styleable AppBarLayoutStates { 0x7f030300, 0x7f030301, 0x7f030303, 0x7f030304 }
int styleable AppBarLayoutStates_state_collapsed 0
int styleable AppBarLayoutStates_state_collapsible 1
int styleable AppBarLayoutStates_state_liftable 2
int styleable AppBarLayoutStates_state_lifted 3
int[] styleable AppBarLayout_Layout { 0x7f03020f, 0x7f030210 }
int styleable AppBarLayout_Layout_layout_scrollFlags 0
int styleable AppBarLayout_Layout_layout_scrollInterpolator 1
int[] styleable AppCompatImageView { 0x01010119, 0x7f0302f6, 0x7f030369, 0x7f03036a }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x01010142, 0x7f030365, 0x7f030366, 0x7f030367 }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 }
int styleable AppCompatTextHelper_android_textAppearance 0
int styleable AppCompatTextHelper_android_drawableTop 1
int styleable AppCompatTextHelper_android_drawableBottom 2
int styleable AppCompatTextHelper_android_drawableLeft 3
int styleable AppCompatTextHelper_android_drawableRight 4
int styleable AppCompatTextHelper_android_drawableStart 5
int styleable AppCompatTextHelper_android_drawableEnd 6
int[] styleable AppCompatTextView { 0x01010034, 0x7f030039, 0x7f03003a, 0x7f03003b, 0x7f03003c, 0x7f03003d, 0x7f03011e, 0x7f03011f, 0x7f030120, 0x7f030121, 0x7f030123, 0x7f030124, 0x7f030125, 0x7f030126, 0x7f03015f, 0x7f030175, 0x7f03017e, 0x7f0301ce, 0x7f030214, 0x7f030339, 0x7f030355 }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_drawableBottomCompat 6
int styleable AppCompatTextView_drawableEndCompat 7
int styleable AppCompatTextView_drawableLeftCompat 8
int styleable AppCompatTextView_drawableRightCompat 9
int styleable AppCompatTextView_drawableStartCompat 10
int styleable AppCompatTextView_drawableTint 11
int styleable AppCompatTextView_drawableTintMode 12
int styleable AppCompatTextView_drawableTopCompat 13
int styleable AppCompatTextView_firstBaselineToTopHeight 14
int styleable AppCompatTextView_fontFamily 15
int styleable AppCompatTextView_fontVariationSettings 16
int styleable AppCompatTextView_lastBaselineToBottomHeight 17
int styleable AppCompatTextView_lineHeight 18
int styleable AppCompatTextView_textAllCaps 19
int styleable AppCompatTextView_textLocale 20
int[] styleable AppCompatTheme { 0x01010057, 0x010100ae, 0x7f030001, 0x7f030002, 0x7f030003, 0x7f030004, 0x7f030005, 0x7f030006, 0x7f030007, 0x7f030008, 0x7f030009, 0x7f03000a, 0x7f03000b, 0x7f03000c, 0x7f03000d, 0x7f03000f, 0x7f030010, 0x7f030011, 0x7f030012, 0x7f030013, 0x7f030014, 0x7f030015, 0x7f030016, 0x7f030017, 0x7f030018, 0x7f030019, 0x7f03001a, 0x7f03001b, 0x7f03001c, 0x7f03001d, 0x7f03001e, 0x7f03001f, 0x7f030024, 0x7f030026, 0x7f030027, 0x7f030028, 0x7f030029, 0x7f030038, 0x7f03005d, 0x7f03006e, 0x7f03006f, 0x7f030070, 0x7f030071, 0x7f030072, 0x7f030077, 0x7f030078, 0x7f030084, 0x7f03008d, 0x7f0300b9, 0x7f0300ba, 0x7f0300bb, 0x7f0300bc, 0x7f0300bd, 0x7f0300be, 0x7f0300bf, 0x7f0300c6, 0x7f0300c7, 0x7f0300cd, 0x7f0300e5, 0x7f030112, 0x7f030113, 0x7f030114, 0x7f030117, 0x7f030119, 0x7f030129, 0x7f03012a, 0x7f03012c, 0x7f03012d, 0x7f03012e, 0x7f030196, 0x7f0301a3, 0x7f030217, 0x7f030218, 0x7f030219, 0x7f03021a, 0x7f03021d, 0x7f03021e, 0x7f03021f, 0x7f030220, 0x7f030221, 0x7f030222, 0x7f030223, 0x7f030224, 0x7f030225, 0x7f03028f, 0x7f030290, 0x7f030291, 0x7f0302aa, 0x7f0302ac, 0x7f0302b8, 0x7f0302ba, 0x7f0302bb, 0x7f0302bc, 0x7f0302ce, 0x7f0302d1, 0x7f0302d2, 0x7f0302d3, 0x7f0302ef, 0x7f0302f0, 0x7f030317, 0x7f030344, 0x7f030346, 0x7f030347, 0x7f030348, 0x7f03034a, 0x7f03034b, 0x7f03034c, 0x7f03034d, 0x7f030350, 0x7f030351, 0x7f030379, 0x7f03037a, 0x7f03037b, 0x7f03037c, 0x7f030399, 0x7f0303a2, 0x7f0303a3, 0x7f0303a4, 0x7f0303a5, 0x7f0303a6, 0x7f0303a7, 0x7f0303a8, 0x7f0303a9, 0x7f0303aa, 0x7f0303ab }
int styleable AppCompatTheme_android_windowIsFloating 0
int styleable AppCompatTheme_android_windowAnimationStyle 1
int styleable AppCompatTheme_actionBarDivider 2
int styleable AppCompatTheme_actionBarItemBackground 3
int styleable AppCompatTheme_actionBarPopupTheme 4
int styleable AppCompatTheme_actionBarSize 5
int styleable AppCompatTheme_actionBarSplitStyle 6
int styleable AppCompatTheme_actionBarStyle 7
int styleable AppCompatTheme_actionBarTabBarStyle 8
int styleable AppCompatTheme_actionBarTabStyle 9
int styleable AppCompatTheme_actionBarTabTextStyle 10
int styleable AppCompatTheme_actionBarTheme 11
int styleable AppCompatTheme_actionBarWidgetTheme 12
int styleable AppCompatTheme_actionButtonStyle 13
int styleable AppCompatTheme_actionDropDownStyle 14
int styleable AppCompatTheme_actionMenuTextAppearance 15
int styleable AppCompatTheme_actionMenuTextColor 16
int styleable AppCompatTheme_actionModeBackground 17
int styleable AppCompatTheme_actionModeCloseButtonStyle 18
int styleable AppCompatTheme_actionModeCloseDrawable 19
int styleable AppCompatTheme_actionModeCopyDrawable 20
int styleable AppCompatTheme_actionModeCutDrawable 21
int styleable AppCompatTheme_actionModeFindDrawable 22
int styleable AppCompatTheme_actionModePasteDrawable 23
int styleable AppCompatTheme_actionModePopupWindowStyle 24
int styleable AppCompatTheme_actionModeSelectAllDrawable 25
int styleable AppCompatTheme_actionModeShareDrawable 26
int styleable AppCompatTheme_actionModeSplitBackground 27
int styleable AppCompatTheme_actionModeStyle 28
int styleable AppCompatTheme_actionModeWebSearchDrawable 29
int styleable AppCompatTheme_actionOverflowButtonStyle 30
int styleable AppCompatTheme_actionOverflowMenuStyle 31
int styleable AppCompatTheme_activityChooserViewStyle 32
int styleable AppCompatTheme_alertDialogButtonGroupStyle 33
int styleable AppCompatTheme_alertDialogCenterButtons 34
int styleable AppCompatTheme_alertDialogStyle 35
int styleable AppCompatTheme_alertDialogTheme 36
int styleable AppCompatTheme_autoCompleteTextViewStyle 37
int styleable AppCompatTheme_borderlessButtonStyle 38
int styleable AppCompatTheme_buttonBarButtonStyle 39
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 40
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 41
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 42
int styleable AppCompatTheme_buttonBarStyle 43
int styleable AppCompatTheme_buttonStyle 44
int styleable AppCompatTheme_buttonStyleSmall 45
int styleable AppCompatTheme_checkboxStyle 46
int styleable AppCompatTheme_checkedTextViewStyle 47
int styleable AppCompatTheme_colorAccent 48
int styleable AppCompatTheme_colorBackgroundFloating 49
int styleable AppCompatTheme_colorButtonNormal 50
int styleable AppCompatTheme_colorControlActivated 51
int styleable AppCompatTheme_colorControlHighlight 52
int styleable AppCompatTheme_colorControlNormal 53
int styleable AppCompatTheme_colorError 54
int styleable AppCompatTheme_colorPrimary 55
int styleable AppCompatTheme_colorPrimaryDark 56
int styleable AppCompatTheme_colorSwitchThumbNormal 57
int styleable AppCompatTheme_controlBackground 58
int styleable AppCompatTheme_dialogCornerRadius 59
int styleable AppCompatTheme_dialogPreferredPadding 60
int styleable AppCompatTheme_dialogTheme 61
int styleable AppCompatTheme_dividerHorizontal 62
int styleable AppCompatTheme_dividerVertical 63
int styleable AppCompatTheme_dropDownListViewStyle 64
int styleable AppCompatTheme_dropdownListPreferredItemHeight 65
int styleable AppCompatTheme_editTextBackground 66
int styleable AppCompatTheme_editTextColor 67
int styleable AppCompatTheme_editTextStyle 68
int styleable AppCompatTheme_homeAsUpIndicator 69
int styleable AppCompatTheme_imageButtonStyle 70
int styleable AppCompatTheme_listChoiceBackgroundIndicator 71
int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 72
int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 73
int styleable AppCompatTheme_listDividerAlertDialog 74
int styleable AppCompatTheme_listMenuViewStyle 75
int styleable AppCompatTheme_listPopupWindowStyle 76
int styleable AppCompatTheme_listPreferredItemHeight 77
int styleable AppCompatTheme_listPreferredItemHeightLarge 78
int styleable AppCompatTheme_listPreferredItemHeightSmall 79
int styleable AppCompatTheme_listPreferredItemPaddingEnd 80
int styleable AppCompatTheme_listPreferredItemPaddingLeft 81
int styleable AppCompatTheme_listPreferredItemPaddingRight 82
int styleable AppCompatTheme_listPreferredItemPaddingStart 83
int styleable AppCompatTheme_panelBackground 84
int styleable AppCompatTheme_panelMenuListTheme 85
int styleable AppCompatTheme_panelMenuListWidth 86
int styleable AppCompatTheme_popupMenuStyle 87
int styleable AppCompatTheme_popupWindowStyle 88
int styleable AppCompatTheme_radioButtonStyle 89
int styleable AppCompatTheme_ratingBarStyle 90
int styleable AppCompatTheme_ratingBarStyleIndicator 91
int styleable AppCompatTheme_ratingBarStyleSmall 92
int styleable AppCompatTheme_searchViewStyle 93
int styleable AppCompatTheme_seekBarStyle 94
int styleable AppCompatTheme_selectableItemBackground 95
int styleable AppCompatTheme_selectableItemBackgroundBorderless 96
int styleable AppCompatTheme_spinnerDropDownItemStyle 97
int styleable AppCompatTheme_spinnerStyle 98
int styleable AppCompatTheme_switchStyle 99
int styleable AppCompatTheme_textAppearanceLargePopupMenu 100
int styleable AppCompatTheme_textAppearanceListItem 101
int styleable AppCompatTheme_textAppearanceListItemSecondary 102
int styleable AppCompatTheme_textAppearanceListItemSmall 103
int styleable AppCompatTheme_textAppearancePopupMenuHeader 104
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 105
int styleable AppCompatTheme_textAppearanceSearchResultTitle 106
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 107
int styleable AppCompatTheme_textColorAlertDialogListItem 108
int styleable AppCompatTheme_textColorSearchUrl 109
int styleable AppCompatTheme_toolbarNavigationButtonStyle 110
int styleable AppCompatTheme_toolbarStyle 111
int styleable AppCompatTheme_tooltipForegroundColor 112
int styleable AppCompatTheme_tooltipFrameBackground 113
int styleable AppCompatTheme_viewInflaterClass 114
int styleable AppCompatTheme_windowActionBar 115
int styleable AppCompatTheme_windowActionBarOverlay 116
int styleable AppCompatTheme_windowActionModeOverlay 117
int styleable AppCompatTheme_windowFixedHeightMajor 118
int styleable AppCompatTheme_windowFixedHeightMinor 119
int styleable AppCompatTheme_windowFixedWidthMajor 120
int styleable AppCompatTheme_windowFixedWidthMinor 121
int styleable AppCompatTheme_windowMinWidthMajor 122
int styleable AppCompatTheme_windowMinWidthMinor 123
int styleable AppCompatTheme_windowNoTitle 124
int[] styleable Badge { 0x7f030040, 0x7f03004a, 0x7f03004c, 0x7f030198, 0x7f030247, 0x7f03027d, 0x7f030398 }
int styleable Badge_backgroundColor 0
int styleable Badge_badgeGravity 1
int styleable Badge_badgeTextColor 2
int styleable Badge_horizontalOffset 3
int styleable Badge_maxCharacterCount 4
int styleable Badge_number 5
int styleable Badge_verticalOffset 6
int[] styleable BaseProgressIndicator { 0x01010139, 0x7f03018e, 0x7f0301a6, 0x7f030252, 0x7f0302dc, 0x7f0302de, 0x7f030383, 0x7f030386, 0x7f030388 }
int styleable BaseProgressIndicator_android_indeterminate 0
int styleable BaseProgressIndicator_hideAnimationBehavior 1
int styleable BaseProgressIndicator_indicatorColor 2
int styleable BaseProgressIndicator_minHideDelay 3
int styleable BaseProgressIndicator_showAnimationBehavior 4
int styleable BaseProgressIndicator_showDelay 5
int styleable BaseProgressIndicator_trackColor 6
int styleable BaseProgressIndicator_trackCornerRadius 7
int styleable BaseProgressIndicator_trackThickness 8
int[] styleable BottomAppBar { 0x7f030048, 0x7f03012f, 0x7f030151, 0x7f030152, 0x7f030153, 0x7f030154, 0x7f030155, 0x7f030191, 0x7f030288, 0x7f03028a, 0x7f03028b }
int styleable BottomAppBar_backgroundTint 0
int styleable BottomAppBar_elevation 1
int styleable BottomAppBar_fabAlignmentMode 2
int styleable BottomAppBar_fabAnimationMode 3
int styleable BottomAppBar_fabCradleMargin 4
int styleable BottomAppBar_fabCradleRoundedCornerRadius 5
int styleable BottomAppBar_fabCradleVerticalOffset 6
int styleable BottomAppBar_hideOnScroll 7
int styleable BottomAppBar_paddingBottomSystemWindowInsets 8
int styleable BottomAppBar_paddingLeftSystemWindowInsets 9
int styleable BottomAppBar_paddingRightSystemWindowInsets 10
int[] styleable BottomNavigationView { 0x7f0301b2 }
int styleable BottomNavigationView_itemHorizontalTranslationEnabled 0
int[] styleable BottomSheetBehavior_Layout { 0x0101011f, 0x01010440, 0x7f030048, 0x7f030053, 0x7f030054, 0x7f030055, 0x7f030056, 0x7f030057, 0x7f030059, 0x7f03005a, 0x7f03005b, 0x7f030183, 0x7f030288, 0x7f03028a, 0x7f03028b, 0x7f03028e, 0x7f0302d6, 0x7f0302d9 }
int styleable BottomSheetBehavior_Layout_android_maxWidth 0
int styleable BottomSheetBehavior_Layout_android_elevation 1
int styleable BottomSheetBehavior_Layout_backgroundTint 2
int styleable BottomSheetBehavior_Layout_behavior_draggable 3
int styleable BottomSheetBehavior_Layout_behavior_expandedOffset 4
int styleable BottomSheetBehavior_Layout_behavior_fitToContents 5
int styleable BottomSheetBehavior_Layout_behavior_halfExpandedRatio 6
int styleable BottomSheetBehavior_Layout_behavior_hideable 7
int styleable BottomSheetBehavior_Layout_behavior_peekHeight 8
int styleable BottomSheetBehavior_Layout_behavior_saveFlags 9
int styleable BottomSheetBehavior_Layout_behavior_skipCollapsed 10
int styleable BottomSheetBehavior_Layout_gestureInsetBottomIgnored 11
int styleable BottomSheetBehavior_Layout_paddingBottomSystemWindowInsets 12
int styleable BottomSheetBehavior_Layout_paddingLeftSystemWindowInsets 13
int styleable BottomSheetBehavior_Layout_paddingRightSystemWindowInsets 14
int styleable BottomSheetBehavior_Layout_paddingTopSystemWindowInsets 15
int styleable BottomSheetBehavior_Layout_shapeAppearance 16
int styleable BottomSheetBehavior_Layout_shapeAppearanceOverlay 17
int[] styleable ButtonBarLayout { 0x7f03002a }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable Capability { 0x7f0302b7, 0x7f0302db }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable CardView { 0x0101013f, 0x01010140, 0x7f03007b, 0x7f03007c, 0x7f03007d, 0x7f03007f, 0x7f030080, 0x7f030081, 0x7f0300dc, 0x7f0300dd, 0x7f0300df, 0x7f0300e0, 0x7f0300e2 }
int styleable CardView_android_minWidth 0
int styleable CardView_android_minHeight 1
int styleable CardView_cardBackgroundColor 2
int styleable CardView_cardCornerRadius 3
int styleable CardView_cardElevation 4
int styleable CardView_cardMaxElevation 5
int styleable CardView_cardPreventCornerOverlap 6
int styleable CardView_cardUseCompatPadding 7
int styleable CardView_contentPadding 8
int styleable CardView_contentPaddingBottom 9
int styleable CardView_contentPaddingLeft 10
int styleable CardView_contentPaddingRight 11
int styleable CardView_contentPaddingTop 12
int[] styleable Chip { 0x01010034, 0x01010095, 0x01010098, 0x010100ab, 0x0101011f, 0x0101014f, 0x010101e5, 0x7f030087, 0x7f030088, 0x7f03008b, 0x7f03008c, 0x7f03008e, 0x7f03008f, 0x7f030090, 0x7f030092, 0x7f030093, 0x7f030094, 0x7f030095, 0x7f030096, 0x7f030097, 0x7f030098, 0x7f03009d, 0x7f03009e, 0x7f03009f, 0x7f0300a1, 0x7f0300aa, 0x7f0300ab, 0x7f0300ac, 0x7f0300ad, 0x7f0300ae, 0x7f0300af, 0x7f0300b0, 0x7f03013b, 0x7f03018f, 0x7f03019b, 0x7f03019f, 0x7f0302c4, 0x7f0302d6, 0x7f0302d9, 0x7f0302e0, 0x7f030352, 0x7f030356 }
int styleable Chip_android_textAppearance 0
int styleable Chip_android_textSize 1
int styleable Chip_android_textColor 2
int styleable Chip_android_ellipsize 3
int styleable Chip_android_maxWidth 4
int styleable Chip_android_text 5
int styleable Chip_android_checkable 6
int styleable Chip_checkedIcon 7
int styleable Chip_checkedIconEnabled 8
int styleable Chip_checkedIconTint 9
int styleable Chip_checkedIconVisible 10
int styleable Chip_chipBackgroundColor 11
int styleable Chip_chipCornerRadius 12
int styleable Chip_chipEndPadding 13
int styleable Chip_chipIcon 14
int styleable Chip_chipIconEnabled 15
int styleable Chip_chipIconSize 16
int styleable Chip_chipIconTint 17
int styleable Chip_chipIconVisible 18
int styleable Chip_chipMinHeight 19
int styleable Chip_chipMinTouchTargetSize 20
int styleable Chip_chipStartPadding 21
int styleable Chip_chipStrokeColor 22
int styleable Chip_chipStrokeWidth 23
int styleable Chip_chipSurfaceColor 24
int styleable Chip_closeIcon 25
int styleable Chip_closeIconEnabled 26
int styleable Chip_closeIconEndPadding 27
int styleable Chip_closeIconSize 28
int styleable Chip_closeIconStartPadding 29
int styleable Chip_closeIconTint 30
int styleable Chip_closeIconVisible 31
int styleable Chip_ensureMinTouchTargetSize 32
int styleable Chip_hideMotionSpec 33
int styleable Chip_iconEndPadding 34
int styleable Chip_iconStartPadding 35
int styleable Chip_rippleColor 36
int styleable Chip_shapeAppearance 37
int styleable Chip_shapeAppearanceOverlay 38
int styleable Chip_showMotionSpec 39
int styleable Chip_textEndPadding 40
int styleable Chip_textStartPadding 41
int[] styleable ChipGroup { 0x7f030086, 0x7f030099, 0x7f03009a, 0x7f03009b, 0x7f0302d4, 0x7f0302e6, 0x7f0302e7 }
int styleable ChipGroup_checkedChip 0
int styleable ChipGroup_chipSpacing 1
int styleable ChipGroup_chipSpacingHorizontal 2
int styleable ChipGroup_chipSpacingVertical 3
int styleable ChipGroup_selectionRequired 4
int styleable ChipGroup_singleLine 5
int styleable ChipGroup_singleSelection 6
int[] styleable CircularProgressIndicator { 0x7f0301a7, 0x7f0301a9, 0x7f0301aa }
int styleable CircularProgressIndicator_indicatorDirectionCircular 0
int styleable CircularProgressIndicator_indicatorInset 1
int styleable CircularProgressIndicator_indicatorSize 2
int[] styleable ClockFaceView { 0x7f0300a6, 0x7f0300a9 }
int styleable ClockFaceView_clockFaceBackgroundColor 0
int styleable ClockFaceView_clockNumberTextColor 1
int[] styleable ClockHandView { 0x7f0300a7, 0x7f03023f, 0x7f0302d5 }
int styleable ClockHandView_clockHandColor 0
int styleable ClockHandView_materialCircleRadius 1
int styleable ClockHandView_selectorSize 2
int[] styleable CollapsingToolbarLayout { 0x7f0300b5, 0x7f0300b6, 0x7f0300e3, 0x7f030148, 0x7f030149, 0x7f03014a, 0x7f03014b, 0x7f03014c, 0x7f03014d, 0x7f03014e, 0x7f03024a, 0x7f0302c9, 0x7f0302cb, 0x7f030307, 0x7f03036b, 0x7f03036d, 0x7f03036e, 0x7f030378 }
int styleable CollapsingToolbarLayout_collapsedTitleGravity 0
int styleable CollapsingToolbarLayout_collapsedTitleTextAppearance 1
int styleable CollapsingToolbarLayout_contentScrim 2
int styleable CollapsingToolbarLayout_expandedTitleGravity 3
int styleable CollapsingToolbarLayout_expandedTitleMargin 4
int styleable CollapsingToolbarLayout_expandedTitleMarginBottom 5
int styleable CollapsingToolbarLayout_expandedTitleMarginEnd 6
int styleable CollapsingToolbarLayout_expandedTitleMarginStart 7
int styleable CollapsingToolbarLayout_expandedTitleMarginTop 8
int styleable CollapsingToolbarLayout_expandedTitleTextAppearance 9
int styleable CollapsingToolbarLayout_maxLines 10
int styleable CollapsingToolbarLayout_scrimAnimationDuration 11
int styleable CollapsingToolbarLayout_scrimVisibleHeightTrigger 12
int styleable CollapsingToolbarLayout_statusBarScrim 13
int styleable CollapsingToolbarLayout_title 14
int styleable CollapsingToolbarLayout_titleCollapseMode 15
int styleable CollapsingToolbarLayout_titleEnabled 16
int styleable CollapsingToolbarLayout_toolbarId 17
int[] styleable CollapsingToolbarLayout_Layout { 0x7f0301d7, 0x7f0301d8 }
int styleable CollapsingToolbarLayout_Layout_layout_collapseMode 0
int styleable CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f03002b, 0x7f0301ca }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable CompoundButton { 0x01010107, 0x7f030073, 0x7f030079, 0x7f03007a }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonCompat 1
int styleable CompoundButton_buttonTint 2
int styleable CompoundButton_buttonTintMode 3
int[] styleable Constraint { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f03002f, 0x7f03004e, 0x7f03004f, 0x7f030050, 0x7f030083, 0x7f0300d2, 0x7f03011d, 0x7f030161, 0x7f030162, 0x7f030163, 0x7f030164, 0x7f030165, 0x7f030166, 0x7f030167, 0x7f030168, 0x7f030169, 0x7f03016a, 0x7f03016b, 0x7f03016c, 0x7f03016d, 0x7f03016f, 0x7f030170, 0x7f030171, 0x7f030172, 0x7f030173, 0x7f0301d9, 0x7f0301da, 0x7f0301db, 0x7f0301dc, 0x7f0301dd, 0x7f0301de, 0x7f0301df, 0x7f0301e0, 0x7f0301e1, 0x7f0301e2, 0x7f0301e3, 0x7f0301e4, 0x7f0301e5, 0x7f0301e6, 0x7f0301e7, 0x7f0301e8, 0x7f0301e9, 0x7f0301ea, 0x7f0301eb, 0x7f0301ec, 0x7f0301ed, 0x7f0301ee, 0x7f0301ef, 0x7f0301f0, 0x7f0301f1, 0x7f0301f2, 0x7f0301f3, 0x7f0301f4, 0x7f0301f5, 0x7f0301f6, 0x7f0301f7, 0x7f0301f8, 0x7f0301f9, 0x7f0301fa, 0x7f0301fb, 0x7f0301fc, 0x7f0301fd, 0x7f0301fe, 0x7f0301ff, 0x7f030200, 0x7f030201, 0x7f030202, 0x7f030204, 0x7f030205, 0x7f030206, 0x7f030207, 0x7f030208, 0x7f030209, 0x7f03020a, 0x7f03020b, 0x7f03026b, 0x7f03026c, 0x7f030297, 0x7f03029e, 0x7f03038c, 0x7f03038e, 0x7f03039a }
int styleable Constraint_android_orientation 0
int styleable Constraint_android_id 1
int styleable Constraint_android_visibility 2
int styleable Constraint_android_layout_width 3
int styleable Constraint_android_layout_height 4
int styleable Constraint_android_layout_marginLeft 5
int styleable Constraint_android_layout_marginTop 6
int styleable Constraint_android_layout_marginRight 7
int styleable Constraint_android_layout_marginBottom 8
int styleable Constraint_android_maxWidth 9
int styleable Constraint_android_maxHeight 10
int styleable Constraint_android_minWidth 11
int styleable Constraint_android_minHeight 12
int styleable Constraint_android_alpha 13
int styleable Constraint_android_transformPivotX 14
int styleable Constraint_android_transformPivotY 15
int styleable Constraint_android_translationX 16
int styleable Constraint_android_translationY 17
int styleable Constraint_android_scaleX 18
int styleable Constraint_android_scaleY 19
int styleable Constraint_android_rotation 20
int styleable Constraint_android_rotationX 21
int styleable Constraint_android_rotationY 22
int styleable Constraint_android_layout_marginStart 23
int styleable Constraint_android_layout_marginEnd 24
int styleable Constraint_android_translationZ 25
int styleable Constraint_android_elevation 26
int styleable Constraint_animate_relativeTo 27
int styleable Constraint_barrierAllowsGoneWidgets 28
int styleable Constraint_barrierDirection 29
int styleable Constraint_barrierMargin 30
int styleable Constraint_chainUseRtl 31
int styleable Constraint_constraint_referenced_ids 32
int styleable Constraint_drawPath 33
int styleable Constraint_flow_firstHorizontalBias 34
int styleable Constraint_flow_firstHorizontalStyle 35
int styleable Constraint_flow_firstVerticalBias 36
int styleable Constraint_flow_firstVerticalStyle 37
int styleable Constraint_flow_horizontalAlign 38
int styleable Constraint_flow_horizontalBias 39
int styleable Constraint_flow_horizontalGap 40
int styleable Constraint_flow_horizontalStyle 41
int styleable Constraint_flow_lastHorizontalBias 42
int styleable Constraint_flow_lastHorizontalStyle 43
int styleable Constraint_flow_lastVerticalBias 44
int styleable Constraint_flow_lastVerticalStyle 45
int styleable Constraint_flow_maxElementsWrap 46
int styleable Constraint_flow_verticalAlign 47
int styleable Constraint_flow_verticalBias 48
int styleable Constraint_flow_verticalGap 49
int styleable Constraint_flow_verticalStyle 50
int styleable Constraint_flow_wrapMode 51
int styleable Constraint_layout_constrainedHeight 52
int styleable Constraint_layout_constrainedWidth 53
int styleable Constraint_layout_constraintBaseline_creator 54
int styleable Constraint_layout_constraintBaseline_toBaselineOf 55
int styleable Constraint_layout_constraintBottom_creator 56
int styleable Constraint_layout_constraintBottom_toBottomOf 57
int styleable Constraint_layout_constraintBottom_toTopOf 58
int styleable Constraint_layout_constraintCircle 59
int styleable Constraint_layout_constraintCircleAngle 60
int styleable Constraint_layout_constraintCircleRadius 61
int styleable Constraint_layout_constraintDimensionRatio 62
int styleable Constraint_layout_constraintEnd_toEndOf 63
int styleable Constraint_layout_constraintEnd_toStartOf 64
int styleable Constraint_layout_constraintGuide_begin 65
int styleable Constraint_layout_constraintGuide_end 66
int styleable Constraint_layout_constraintGuide_percent 67
int styleable Constraint_layout_constraintHeight_default 68
int styleable Constraint_layout_constraintHeight_max 69
int styleable Constraint_layout_constraintHeight_min 70
int styleable Constraint_layout_constraintHeight_percent 71
int styleable Constraint_layout_constraintHorizontal_bias 72
int styleable Constraint_layout_constraintHorizontal_chainStyle 73
int styleable Constraint_layout_constraintHorizontal_weight 74
int styleable Constraint_layout_constraintLeft_creator 75
int styleable Constraint_layout_constraintLeft_toLeftOf 76
int styleable Constraint_layout_constraintLeft_toRightOf 77
int styleable Constraint_layout_constraintRight_creator 78
int styleable Constraint_layout_constraintRight_toLeftOf 79
int styleable Constraint_layout_constraintRight_toRightOf 80
int styleable Constraint_layout_constraintStart_toEndOf 81
int styleable Constraint_layout_constraintStart_toStartOf 82
int styleable Constraint_layout_constraintTag 83
int styleable Constraint_layout_constraintTop_creator 84
int styleable Constraint_layout_constraintTop_toBottomOf 85
int styleable Constraint_layout_constraintTop_toTopOf 86
int styleable Constraint_layout_constraintVertical_bias 87
int styleable Constraint_layout_constraintVertical_chainStyle 88
int styleable Constraint_layout_constraintVertical_weight 89
int styleable Constraint_layout_constraintWidth_default 90
int styleable Constraint_layout_constraintWidth_max 91
int styleable Constraint_layout_constraintWidth_min 92
int styleable Constraint_layout_constraintWidth_percent 93
int styleable Constraint_layout_editor_absoluteX 94
int styleable Constraint_layout_editor_absoluteY 95
int styleable Constraint_layout_goneMarginBottom 96
int styleable Constraint_layout_goneMarginEnd 97
int styleable Constraint_layout_goneMarginLeft 98
int styleable Constraint_layout_goneMarginRight 99
int styleable Constraint_layout_goneMarginStart 100
int styleable Constraint_layout_goneMarginTop 101
int styleable Constraint_motionProgress 102
int styleable Constraint_motionStagger 103
int styleable Constraint_pathMotionArc 104
int styleable Constraint_pivotAnchor 105
int styleable Constraint_transitionEasing 106
int styleable Constraint_transitionPathRotate 107
int styleable Constraint_visibilityMode 108
int[] styleable ConstraintLayout_Layout { 0x010100c4, 0x010100d5, 0x010100d6, 0x010100d7, 0x010100d8, 0x010100d9, 0x010100dc, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010103b3, 0x010103b4, 0x01010440, 0x7f03004e, 0x7f03004f, 0x7f030050, 0x7f030083, 0x7f0300cf, 0x7f0300d2, 0x7f030161, 0x7f030162, 0x7f030163, 0x7f030164, 0x7f030165, 0x7f030166, 0x7f030167, 0x7f030168, 0x7f030169, 0x7f03016a, 0x7f03016b, 0x7f03016c, 0x7f03016d, 0x7f03016f, 0x7f030170, 0x7f030171, 0x7f030172, 0x7f030173, 0x7f0301d1, 0x7f0301d9, 0x7f0301da, 0x7f0301db, 0x7f0301dc, 0x7f0301dd, 0x7f0301de, 0x7f0301df, 0x7f0301e0, 0x7f0301e1, 0x7f0301e2, 0x7f0301e3, 0x7f0301e4, 0x7f0301e5, 0x7f0301e6, 0x7f0301e7, 0x7f0301e8, 0x7f0301e9, 0x7f0301ea, 0x7f0301eb, 0x7f0301ec, 0x7f0301ed, 0x7f0301ee, 0x7f0301ef, 0x7f0301f0, 0x7f0301f1, 0x7f0301f2, 0x7f0301f3, 0x7f0301f4, 0x7f0301f5, 0x7f0301f6, 0x7f0301f7, 0x7f0301f8, 0x7f0301f9, 0x7f0301fa, 0x7f0301fb, 0x7f0301fc, 0x7f0301fd, 0x7f0301fe, 0x7f0301ff, 0x7f030200, 0x7f030201, 0x7f030202, 0x7f030204, 0x7f030205, 0x7f030206, 0x7f030207, 0x7f030208, 0x7f030209, 0x7f03020a, 0x7f03020b, 0x7f03020e }
int styleable ConstraintLayout_Layout_android_orientation 0
int styleable ConstraintLayout_Layout_android_padding 1
int styleable ConstraintLayout_Layout_android_paddingLeft 2
int styleable ConstraintLayout_Layout_android_paddingTop 3
int styleable ConstraintLayout_Layout_android_paddingRight 4
int styleable ConstraintLayout_Layout_android_paddingBottom 5
int styleable ConstraintLayout_Layout_android_visibility 6
int styleable ConstraintLayout_Layout_android_maxWidth 7
int styleable ConstraintLayout_Layout_android_maxHeight 8
int styleable ConstraintLayout_Layout_android_minWidth 9
int styleable ConstraintLayout_Layout_android_minHeight 10
int styleable ConstraintLayout_Layout_android_paddingStart 11
int styleable ConstraintLayout_Layout_android_paddingEnd 12
int styleable ConstraintLayout_Layout_android_elevation 13
int styleable ConstraintLayout_Layout_barrierAllowsGoneWidgets 14
int styleable ConstraintLayout_Layout_barrierDirection 15
int styleable ConstraintLayout_Layout_barrierMargin 16
int styleable ConstraintLayout_Layout_chainUseRtl 17
int styleable ConstraintLayout_Layout_constraintSet 18
int styleable ConstraintLayout_Layout_constraint_referenced_ids 19
int styleable ConstraintLayout_Layout_flow_firstHorizontalBias 20
int styleable ConstraintLayout_Layout_flow_firstHorizontalStyle 21
int styleable ConstraintLayout_Layout_flow_firstVerticalBias 22
int styleable ConstraintLayout_Layout_flow_firstVerticalStyle 23
int styleable ConstraintLayout_Layout_flow_horizontalAlign 24
int styleable ConstraintLayout_Layout_flow_horizontalBias 25
int styleable ConstraintLayout_Layout_flow_horizontalGap 26
int styleable ConstraintLayout_Layout_flow_horizontalStyle 27
int styleable ConstraintLayout_Layout_flow_lastHorizontalBias 28
int styleable ConstraintLayout_Layout_flow_lastHorizontalStyle 29
int styleable ConstraintLayout_Layout_flow_lastVerticalBias 30
int styleable ConstraintLayout_Layout_flow_lastVerticalStyle 31
int styleable ConstraintLayout_Layout_flow_maxElementsWrap 32
int styleable ConstraintLayout_Layout_flow_verticalAlign 33
int styleable ConstraintLayout_Layout_flow_verticalBias 34
int styleable ConstraintLayout_Layout_flow_verticalGap 35
int styleable ConstraintLayout_Layout_flow_verticalStyle 36
int styleable ConstraintLayout_Layout_flow_wrapMode 37
int styleable ConstraintLayout_Layout_layoutDescription 38
int styleable ConstraintLayout_Layout_layout_constrainedHeight 39
int styleable ConstraintLayout_Layout_layout_constrainedWidth 40
int styleable ConstraintLayout_Layout_layout_constraintBaseline_creator 41
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf 42
int styleable ConstraintLayout_Layout_layout_constraintBottom_creator 43
int styleable ConstraintLayout_Layout_layout_constraintBottom_toBottomOf 44
int styleable ConstraintLayout_Layout_layout_constraintBottom_toTopOf 45
int styleable ConstraintLayout_Layout_layout_constraintCircle 46
int styleable ConstraintLayout_Layout_layout_constraintCircleAngle 47
int styleable ConstraintLayout_Layout_layout_constraintCircleRadius 48
int styleable ConstraintLayout_Layout_layout_constraintDimensionRatio 49
int styleable ConstraintLayout_Layout_layout_constraintEnd_toEndOf 50
int styleable ConstraintLayout_Layout_layout_constraintEnd_toStartOf 51
int styleable ConstraintLayout_Layout_layout_constraintGuide_begin 52
int styleable ConstraintLayout_Layout_layout_constraintGuide_end 53
int styleable ConstraintLayout_Layout_layout_constraintGuide_percent 54
int styleable ConstraintLayout_Layout_layout_constraintHeight_default 55
int styleable ConstraintLayout_Layout_layout_constraintHeight_max 56
int styleable ConstraintLayout_Layout_layout_constraintHeight_min 57
int styleable ConstraintLayout_Layout_layout_constraintHeight_percent 58
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_bias 59
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle 60
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_weight 61
int styleable ConstraintLayout_Layout_layout_constraintLeft_creator 62
int styleable ConstraintLayout_Layout_layout_constraintLeft_toLeftOf 63
int styleable ConstraintLayout_Layout_layout_constraintLeft_toRightOf 64
int styleable ConstraintLayout_Layout_layout_constraintRight_creator 65
int styleable ConstraintLayout_Layout_layout_constraintRight_toLeftOf 66
int styleable ConstraintLayout_Layout_layout_constraintRight_toRightOf 67
int styleable ConstraintLayout_Layout_layout_constraintStart_toEndOf 68
int styleable ConstraintLayout_Layout_layout_constraintStart_toStartOf 69
int styleable ConstraintLayout_Layout_layout_constraintTag 70
int styleable ConstraintLayout_Layout_layout_constraintTop_creator 71
int styleable ConstraintLayout_Layout_layout_constraintTop_toBottomOf 72
int styleable ConstraintLayout_Layout_layout_constraintTop_toTopOf 73
int styleable ConstraintLayout_Layout_layout_constraintVertical_bias 74
int styleable ConstraintLayout_Layout_layout_constraintVertical_chainStyle 75
int styleable ConstraintLayout_Layout_layout_constraintVertical_weight 76
int styleable ConstraintLayout_Layout_layout_constraintWidth_default 77
int styleable ConstraintLayout_Layout_layout_constraintWidth_max 78
int styleable ConstraintLayout_Layout_layout_constraintWidth_min 79
int styleable ConstraintLayout_Layout_layout_constraintWidth_percent 80
int styleable ConstraintLayout_Layout_layout_editor_absoluteX 81
int styleable ConstraintLayout_Layout_layout_editor_absoluteY 82
int styleable ConstraintLayout_Layout_layout_goneMarginBottom 83
int styleable ConstraintLayout_Layout_layout_goneMarginEnd 84
int styleable ConstraintLayout_Layout_layout_goneMarginLeft 85
int styleable ConstraintLayout_Layout_layout_goneMarginRight 86
int styleable ConstraintLayout_Layout_layout_goneMarginStart 87
int styleable ConstraintLayout_Layout_layout_goneMarginTop 88
int styleable ConstraintLayout_Layout_layout_optimizationLevel 89
int[] styleable ConstraintLayout_placeholder { 0x7f0300d4, 0x7f0302a3 }
int styleable ConstraintLayout_placeholder_content 0
int styleable ConstraintLayout_placeholder_placeholder_emptyVisibility 1
int[] styleable ConstraintSet { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010101b5, 0x010101b6, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f03002f, 0x7f03004e, 0x7f03004f, 0x7f030050, 0x7f030083, 0x7f0300d2, 0x7f030110, 0x7f03011d, 0x7f030161, 0x7f030162, 0x7f030163, 0x7f030164, 0x7f030165, 0x7f030166, 0x7f030167, 0x7f030168, 0x7f030169, 0x7f03016a, 0x7f03016b, 0x7f03016c, 0x7f03016d, 0x7f03016f, 0x7f030170, 0x7f030171, 0x7f030172, 0x7f030173, 0x7f0301d9, 0x7f0301da, 0x7f0301db, 0x7f0301dc, 0x7f0301dd, 0x7f0301de, 0x7f0301df, 0x7f0301e0, 0x7f0301e1, 0x7f0301e2, 0x7f0301e3, 0x7f0301e4, 0x7f0301e5, 0x7f0301e6, 0x7f0301e7, 0x7f0301e8, 0x7f0301e9, 0x7f0301ea, 0x7f0301eb, 0x7f0301ec, 0x7f0301ed, 0x7f0301ee, 0x7f0301ef, 0x7f0301f0, 0x7f0301f1, 0x7f0301f2, 0x7f0301f3, 0x7f0301f4, 0x7f0301f5, 0x7f0301f6, 0x7f0301f7, 0x7f0301f8, 0x7f0301f9, 0x7f0301fa, 0x7f0301fb, 0x7f0301fc, 0x7f0301fd, 0x7f0301fe, 0x7f0301ff, 0x7f030200, 0x7f030201, 0x7f030202, 0x7f030204, 0x7f030205, 0x7f030206, 0x7f030207, 0x7f030208, 0x7f030209, 0x7f03020a, 0x7f03020b, 0x7f03026b, 0x7f03026c, 0x7f030297, 0x7f03029e, 0x7f03038c, 0x7f03038e }
int styleable ConstraintSet_android_orientation 0
int styleable ConstraintSet_android_id 1
int styleable ConstraintSet_android_visibility 2
int styleable ConstraintSet_android_layout_width 3
int styleable ConstraintSet_android_layout_height 4
int styleable ConstraintSet_android_layout_marginLeft 5
int styleable ConstraintSet_android_layout_marginTop 6
int styleable ConstraintSet_android_layout_marginRight 7
int styleable ConstraintSet_android_layout_marginBottom 8
int styleable ConstraintSet_android_maxWidth 9
int styleable ConstraintSet_android_maxHeight 10
int styleable ConstraintSet_android_minWidth 11
int styleable ConstraintSet_android_minHeight 12
int styleable ConstraintSet_android_pivotX 13
int styleable ConstraintSet_android_pivotY 14
int styleable ConstraintSet_android_alpha 15
int styleable ConstraintSet_android_transformPivotX 16
int styleable ConstraintSet_android_transformPivotY 17
int styleable ConstraintSet_android_translationX 18
int styleable ConstraintSet_android_translationY 19
int styleable ConstraintSet_android_scaleX 20
int styleable ConstraintSet_android_scaleY 21
int styleable ConstraintSet_android_rotation 22
int styleable ConstraintSet_android_rotationX 23
int styleable ConstraintSet_android_rotationY 24
int styleable ConstraintSet_android_layout_marginStart 25
int styleable ConstraintSet_android_layout_marginEnd 26
int styleable ConstraintSet_android_translationZ 27
int styleable ConstraintSet_android_elevation 28
int styleable ConstraintSet_animate_relativeTo 29
int styleable ConstraintSet_barrierAllowsGoneWidgets 30
int styleable ConstraintSet_barrierDirection 31
int styleable ConstraintSet_barrierMargin 32
int styleable ConstraintSet_chainUseRtl 33
int styleable ConstraintSet_constraint_referenced_ids 34
int styleable ConstraintSet_deriveConstraintsFrom 35
int styleable ConstraintSet_drawPath 36
int styleable ConstraintSet_flow_firstHorizontalBias 37
int styleable ConstraintSet_flow_firstHorizontalStyle 38
int styleable ConstraintSet_flow_firstVerticalBias 39
int styleable ConstraintSet_flow_firstVerticalStyle 40
int styleable ConstraintSet_flow_horizontalAlign 41
int styleable ConstraintSet_flow_horizontalBias 42
int styleable ConstraintSet_flow_horizontalGap 43
int styleable ConstraintSet_flow_horizontalStyle 44
int styleable ConstraintSet_flow_lastHorizontalBias 45
int styleable ConstraintSet_flow_lastHorizontalStyle 46
int styleable ConstraintSet_flow_lastVerticalBias 47
int styleable ConstraintSet_flow_lastVerticalStyle 48
int styleable ConstraintSet_flow_maxElementsWrap 49
int styleable ConstraintSet_flow_verticalAlign 50
int styleable ConstraintSet_flow_verticalBias 51
int styleable ConstraintSet_flow_verticalGap 52
int styleable ConstraintSet_flow_verticalStyle 53
int styleable ConstraintSet_flow_wrapMode 54
int styleable ConstraintSet_layout_constrainedHeight 55
int styleable ConstraintSet_layout_constrainedWidth 56
int styleable ConstraintSet_layout_constraintBaseline_creator 57
int styleable ConstraintSet_layout_constraintBaseline_toBaselineOf 58
int styleable ConstraintSet_layout_constraintBottom_creator 59
int styleable ConstraintSet_layout_constraintBottom_toBottomOf 60
int styleable ConstraintSet_layout_constraintBottom_toTopOf 61
int styleable ConstraintSet_layout_constraintCircle 62
int styleable ConstraintSet_layout_constraintCircleAngle 63
int styleable ConstraintSet_layout_constraintCircleRadius 64
int styleable ConstraintSet_layout_constraintDimensionRatio 65
int styleable ConstraintSet_layout_constraintEnd_toEndOf 66
int styleable ConstraintSet_layout_constraintEnd_toStartOf 67
int styleable ConstraintSet_layout_constraintGuide_begin 68
int styleable ConstraintSet_layout_constraintGuide_end 69
int styleable ConstraintSet_layout_constraintGuide_percent 70
int styleable ConstraintSet_layout_constraintHeight_default 71
int styleable ConstraintSet_layout_constraintHeight_max 72
int styleable ConstraintSet_layout_constraintHeight_min 73
int styleable ConstraintSet_layout_constraintHeight_percent 74
int styleable ConstraintSet_layout_constraintHorizontal_bias 75
int styleable ConstraintSet_layout_constraintHorizontal_chainStyle 76
int styleable ConstraintSet_layout_constraintHorizontal_weight 77
int styleable ConstraintSet_layout_constraintLeft_creator 78
int styleable ConstraintSet_layout_constraintLeft_toLeftOf 79
int styleable ConstraintSet_layout_constraintLeft_toRightOf 80
int styleable ConstraintSet_layout_constraintRight_creator 81
int styleable ConstraintSet_layout_constraintRight_toLeftOf 82
int styleable ConstraintSet_layout_constraintRight_toRightOf 83
int styleable ConstraintSet_layout_constraintStart_toEndOf 84
int styleable ConstraintSet_layout_constraintStart_toStartOf 85
int styleable ConstraintSet_layout_constraintTag 86
int styleable ConstraintSet_layout_constraintTop_creator 87
int styleable ConstraintSet_layout_constraintTop_toBottomOf 88
int styleable ConstraintSet_layout_constraintTop_toTopOf 89
int styleable ConstraintSet_layout_constraintVertical_bias 90
int styleable ConstraintSet_layout_constraintVertical_chainStyle 91
int styleable ConstraintSet_layout_constraintVertical_weight 92
int styleable ConstraintSet_layout_constraintWidth_default 93
int styleable ConstraintSet_layout_constraintWidth_max 94
int styleable ConstraintSet_layout_constraintWidth_min 95
int styleable ConstraintSet_layout_constraintWidth_percent 96
int styleable ConstraintSet_layout_editor_absoluteX 97
int styleable ConstraintSet_layout_editor_absoluteY 98
int styleable ConstraintSet_layout_goneMarginBottom 99
int styleable ConstraintSet_layout_goneMarginEnd 100
int styleable ConstraintSet_layout_goneMarginLeft 101
int styleable ConstraintSet_layout_goneMarginRight 102
int styleable ConstraintSet_layout_goneMarginStart 103
int styleable ConstraintSet_layout_goneMarginTop 104
int styleable ConstraintSet_motionProgress 105
int styleable ConstraintSet_motionStagger 106
int styleable ConstraintSet_pathMotionArc 107
int styleable ConstraintSet_pivotAnchor 108
int styleable ConstraintSet_transitionEasing 109
int styleable ConstraintSet_transitionPathRotate 110
int[] styleable CoordinatorLayout { 0x7f0301c9, 0x7f030305 }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x010100b3, 0x7f0301d4, 0x7f0301d5, 0x7f0301d6, 0x7f030203, 0x7f03020c, 0x7f03020d }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable CustomAttribute { 0x7f030037, 0x7f0300fb, 0x7f0300fc, 0x7f0300fd, 0x7f0300fe, 0x7f0300ff, 0x7f030100, 0x7f030102, 0x7f030103 }
int styleable CustomAttribute_attributeName 0
int styleable CustomAttribute_customBoolean 1
int styleable CustomAttribute_customColorDrawableValue 2
int styleable CustomAttribute_customColorValue 3
int styleable CustomAttribute_customDimension 4
int styleable CustomAttribute_customFloatValue 5
int styleable CustomAttribute_customIntegerValue 6
int styleable CustomAttribute_customPixelDimension 7
int styleable CustomAttribute_customStringValue 8
int[] styleable DialogFragmentNavigator { 0x01010003 }
int styleable DialogFragmentNavigator_android_name 0
int[] styleable DrawerArrowToggle { 0x7f030035, 0x7f030036, 0x7f03004d, 0x7f0300b8, 0x7f030122, 0x7f030182, 0x7f0302ee, 0x7f030359 }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable DrawerLayout { 0x7f03012f }
int styleable DrawerLayout_elevation 0
int[] styleable ExtendedFloatingActionButton { 0x7f0300b4, 0x7f03012f, 0x7f03014f, 0x7f03018f, 0x7f0302e0, 0x7f0302e4 }
int styleable ExtendedFloatingActionButton_collapsedSize 0
int styleable ExtendedFloatingActionButton_elevation 1
int styleable ExtendedFloatingActionButton_extendMotionSpec 2
int styleable ExtendedFloatingActionButton_hideMotionSpec 3
int styleable ExtendedFloatingActionButton_showMotionSpec 4
int styleable ExtendedFloatingActionButton_shrinkMotionSpec 5
int[] styleable ExtendedFloatingActionButton_Behavior_Layout { 0x7f030051, 0x7f030052 }
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoHide 0
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoShrink 1
int[] styleable FloatingActionButton { 0x0101000e, 0x7f030048, 0x7f030049, 0x7f03005c, 0x7f03012f, 0x7f03013b, 0x7f030156, 0x7f030157, 0x7f03018f, 0x7f030199, 0x7f030249, 0x7f0302b1, 0x7f0302c4, 0x7f0302d6, 0x7f0302d9, 0x7f0302e0, 0x7f030395 }
int styleable FloatingActionButton_android_enabled 0
int styleable FloatingActionButton_backgroundTint 1
int styleable FloatingActionButton_backgroundTintMode 2
int styleable FloatingActionButton_borderWidth 3
int styleable FloatingActionButton_elevation 4
int styleable FloatingActionButton_ensureMinTouchTargetSize 5
int styleable FloatingActionButton_fabCustomSize 6
int styleable FloatingActionButton_fabSize 7
int styleable FloatingActionButton_hideMotionSpec 8
int styleable FloatingActionButton_hoveredFocusedTranslationZ 9
int styleable FloatingActionButton_maxImageSize 10
int styleable FloatingActionButton_pressedTranslationZ 11
int styleable FloatingActionButton_rippleColor 12
int styleable FloatingActionButton_shapeAppearance 13
int styleable FloatingActionButton_shapeAppearanceOverlay 14
int styleable FloatingActionButton_showMotionSpec 15
int styleable FloatingActionButton_useCompatPadding 16
int[] styleable FloatingActionButton_Behavior_Layout { 0x7f030051 }
int styleable FloatingActionButton_Behavior_Layout_behavior_autoHide 0
int[] styleable FlowLayout { 0x7f0301c0, 0x7f030215 }
int styleable FlowLayout_itemSpacing 0
int styleable FlowLayout_lineSpacing 1
int[] styleable FontFamily { 0x7f030176, 0x7f030177, 0x7f030178, 0x7f030179, 0x7f03017a, 0x7f03017b, 0x7f03017c }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f030174, 0x7f03017d, 0x7f03017e, 0x7f03017f, 0x7f030393 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable ForegroundLinearLayout { 0x01010109, 0x01010200, 0x7f030180 }
int styleable ForegroundLinearLayout_android_foreground 0
int styleable ForegroundLinearLayout_android_foregroundGravity 1
int styleable ForegroundLinearLayout_foregroundInsidePadding 2
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable FragmentNavigator { 0x01010003 }
int styleable FragmentNavigator_android_name 0
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable ImageFilterView { 0x7f03002d, 0x7f03006d, 0x7f0300e4, 0x7f0300f8, 0x7f030286, 0x7f0302c5, 0x7f0302c6, 0x7f0302c8, 0x7f03039c }
int styleable ImageFilterView_altSrc 0
int styleable ImageFilterView_brightness 1
int styleable ImageFilterView_contrast 2
int styleable ImageFilterView_crossfade 3
int styleable ImageFilterView_overlay 4
int styleable ImageFilterView_round 5
int styleable ImageFilterView_roundPercent 6
int styleable ImageFilterView_saturation 7
int styleable ImageFilterView_warmth 8
int[] styleable Insets { 0x7f030288, 0x7f03028a, 0x7f03028b, 0x7f03028e }
int styleable Insets_paddingBottomSystemWindowInsets 0
int styleable Insets_paddingLeftSystemWindowInsets 1
int styleable Insets_paddingRightSystemWindowInsets 2
int styleable Insets_paddingTopSystemWindowInsets 3
int[] styleable KeyAttribute { 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f0300fa, 0x7f030181, 0x7f03026b, 0x7f03026d, 0x7f03038c, 0x7f03038e }
int styleable KeyAttribute_android_alpha 0
int styleable KeyAttribute_android_transformPivotX 1
int styleable KeyAttribute_android_transformPivotY 2
int styleable KeyAttribute_android_translationX 3
int styleable KeyAttribute_android_translationY 4
int styleable KeyAttribute_android_scaleX 5
int styleable KeyAttribute_android_scaleY 6
int styleable KeyAttribute_android_rotation 7
int styleable KeyAttribute_android_rotationX 8
int styleable KeyAttribute_android_rotationY 9
int styleable KeyAttribute_android_translationZ 10
int styleable KeyAttribute_android_elevation 11
int styleable KeyAttribute_curveFit 12
int styleable KeyAttribute_framePosition 13
int styleable KeyAttribute_motionProgress 14
int styleable KeyAttribute_motionTarget 15
int styleable KeyAttribute_transitionEasing 16
int styleable KeyAttribute_transitionPathRotate 17
int[] styleable KeyCycle { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f0300fa, 0x7f030181, 0x7f03026b, 0x7f03026d, 0x7f03038c, 0x7f03038e, 0x7f03039e, 0x7f03039f, 0x7f0303a0, 0x7f0303a1 }
int styleable KeyCycle_android_alpha 0
int styleable KeyCycle_android_translationX 1
int styleable KeyCycle_android_translationY 2
int styleable KeyCycle_android_scaleX 3
int styleable KeyCycle_android_scaleY 4
int styleable KeyCycle_android_rotation 5
int styleable KeyCycle_android_rotationX 6
int styleable KeyCycle_android_rotationY 7
int styleable KeyCycle_android_translationZ 8
int styleable KeyCycle_android_elevation 9
int styleable KeyCycle_curveFit 10
int styleable KeyCycle_framePosition 11
int styleable KeyCycle_motionProgress 12
int styleable KeyCycle_motionTarget 13
int styleable KeyCycle_transitionEasing 14
int styleable KeyCycle_transitionPathRotate 15
int styleable KeyCycle_waveOffset 16
int styleable KeyCycle_wavePeriod 17
int styleable KeyCycle_waveShape 18
int styleable KeyCycle_waveVariesBy 19
int[] styleable KeyFrame { }
int[] styleable KeyFramesAcceleration { }
int[] styleable KeyFramesVelocity { }
int[] styleable KeyPosition { 0x7f0300fa, 0x7f03011d, 0x7f030181, 0x7f0301c7, 0x7f03026d, 0x7f030297, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029c, 0x7f0302e8, 0x7f03038c }
int styleable KeyPosition_curveFit 0
int styleable KeyPosition_drawPath 1
int styleable KeyPosition_framePosition 2
int styleable KeyPosition_keyPositionType 3
int styleable KeyPosition_motionTarget 4
int styleable KeyPosition_pathMotionArc 5
int styleable KeyPosition_percentHeight 6
int styleable KeyPosition_percentWidth 7
int styleable KeyPosition_percentX 8
int styleable KeyPosition_percentY 9
int styleable KeyPosition_sizePercent 10
int styleable KeyPosition_transitionEasing 11
int[] styleable KeyTimeCycle { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f0300fa, 0x7f030181, 0x7f03026b, 0x7f03026d, 0x7f03038c, 0x7f03038e, 0x7f03039d, 0x7f03039e, 0x7f03039f, 0x7f0303a0 }
int styleable KeyTimeCycle_android_alpha 0
int styleable KeyTimeCycle_android_translationX 1
int styleable KeyTimeCycle_android_translationY 2
int styleable KeyTimeCycle_android_scaleX 3
int styleable KeyTimeCycle_android_scaleY 4
int styleable KeyTimeCycle_android_rotation 5
int styleable KeyTimeCycle_android_rotationX 6
int styleable KeyTimeCycle_android_rotationY 7
int styleable KeyTimeCycle_android_translationZ 8
int styleable KeyTimeCycle_android_elevation 9
int styleable KeyTimeCycle_curveFit 10
int styleable KeyTimeCycle_framePosition 11
int styleable KeyTimeCycle_motionProgress 12
int styleable KeyTimeCycle_motionTarget 13
int styleable KeyTimeCycle_transitionEasing 14
int styleable KeyTimeCycle_transitionPathRotate 15
int styleable KeyTimeCycle_waveDecay 16
int styleable KeyTimeCycle_waveOffset 17
int styleable KeyTimeCycle_wavePeriod 18
int styleable KeyTimeCycle_waveShape 19
int[] styleable KeyTrigger { 0x7f030181, 0x7f03026d, 0x7f03026e, 0x7f03026f, 0x7f03027f, 0x7f030281, 0x7f030282, 0x7f030390, 0x7f030391, 0x7f030392 }
int styleable KeyTrigger_framePosition 0
int styleable KeyTrigger_motionTarget 1
int styleable KeyTrigger_motion_postLayoutCollision 2
int styleable KeyTrigger_motion_triggerOnCollision 3
int styleable KeyTrigger_onCross 4
int styleable KeyTrigger_onNegativeCross 5
int styleable KeyTrigger_onPositiveCross 6
int styleable KeyTrigger_triggerId 7
int styleable KeyTrigger_triggerReceiver 8
int styleable KeyTrigger_triggerSlack 9
int[] styleable Layout { 0x010100c4, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x010103b5, 0x010103b6, 0x7f03004e, 0x7f03004f, 0x7f030050, 0x7f030083, 0x7f0300d2, 0x7f0301d9, 0x7f0301da, 0x7f0301db, 0x7f0301dc, 0x7f0301dd, 0x7f0301de, 0x7f0301df, 0x7f0301e0, 0x7f0301e1, 0x7f0301e2, 0x7f0301e3, 0x7f0301e4, 0x7f0301e5, 0x7f0301e6, 0x7f0301e7, 0x7f0301e8, 0x7f0301e9, 0x7f0301ea, 0x7f0301eb, 0x7f0301ec, 0x7f0301ed, 0x7f0301ee, 0x7f0301ef, 0x7f0301f0, 0x7f0301f1, 0x7f0301f2, 0x7f0301f3, 0x7f0301f4, 0x7f0301f5, 0x7f0301f6, 0x7f0301f7, 0x7f0301f9, 0x7f0301fa, 0x7f0301fb, 0x7f0301fc, 0x7f0301fd, 0x7f0301fe, 0x7f0301ff, 0x7f030200, 0x7f030201, 0x7f030202, 0x7f030204, 0x7f030205, 0x7f030206, 0x7f030207, 0x7f030208, 0x7f030209, 0x7f03020a, 0x7f03020b, 0x7f030248, 0x7f03024c, 0x7f030251, 0x7f030255 }
int styleable Layout_android_orientation 0
int styleable Layout_android_layout_width 1
int styleable Layout_android_layout_height 2
int styleable Layout_android_layout_marginLeft 3
int styleable Layout_android_layout_marginTop 4
int styleable Layout_android_layout_marginRight 5
int styleable Layout_android_layout_marginBottom 6
int styleable Layout_android_layout_marginStart 7
int styleable Layout_android_layout_marginEnd 8
int styleable Layout_barrierAllowsGoneWidgets 9
int styleable Layout_barrierDirection 10
int styleable Layout_barrierMargin 11
int styleable Layout_chainUseRtl 12
int styleable Layout_constraint_referenced_ids 13
int styleable Layout_layout_constrainedHeight 14
int styleable Layout_layout_constrainedWidth 15
int styleable Layout_layout_constraintBaseline_creator 16
int styleable Layout_layout_constraintBaseline_toBaselineOf 17
int styleable Layout_layout_constraintBottom_creator 18
int styleable Layout_layout_constraintBottom_toBottomOf 19
int styleable Layout_layout_constraintBottom_toTopOf 20
int styleable Layout_layout_constraintCircle 21
int styleable Layout_layout_constraintCircleAngle 22
int styleable Layout_layout_constraintCircleRadius 23
int styleable Layout_layout_constraintDimensionRatio 24
int styleable Layout_layout_constraintEnd_toEndOf 25
int styleable Layout_layout_constraintEnd_toStartOf 26
int styleable Layout_layout_constraintGuide_begin 27
int styleable Layout_layout_constraintGuide_end 28
int styleable Layout_layout_constraintGuide_percent 29
int styleable Layout_layout_constraintHeight_default 30
int styleable Layout_layout_constraintHeight_max 31
int styleable Layout_layout_constraintHeight_min 32
int styleable Layout_layout_constraintHeight_percent 33
int styleable Layout_layout_constraintHorizontal_bias 34
int styleable Layout_layout_constraintHorizontal_chainStyle 35
int styleable Layout_layout_constraintHorizontal_weight 36
int styleable Layout_layout_constraintLeft_creator 37
int styleable Layout_layout_constraintLeft_toLeftOf 38
int styleable Layout_layout_constraintLeft_toRightOf 39
int styleable Layout_layout_constraintRight_creator 40
int styleable Layout_layout_constraintRight_toLeftOf 41
int styleable Layout_layout_constraintRight_toRightOf 42
int styleable Layout_layout_constraintStart_toEndOf 43
int styleable Layout_layout_constraintStart_toStartOf 44
int styleable Layout_layout_constraintTop_creator 45
int styleable Layout_layout_constraintTop_toBottomOf 46
int styleable Layout_layout_constraintTop_toTopOf 47
int styleable Layout_layout_constraintVertical_bias 48
int styleable Layout_layout_constraintVertical_chainStyle 49
int styleable Layout_layout_constraintVertical_weight 50
int styleable Layout_layout_constraintWidth_default 51
int styleable Layout_layout_constraintWidth_max 52
int styleable Layout_layout_constraintWidth_min 53
int styleable Layout_layout_constraintWidth_percent 54
int styleable Layout_layout_editor_absoluteX 55
int styleable Layout_layout_editor_absoluteY 56
int styleable Layout_layout_goneMarginBottom 57
int styleable Layout_layout_goneMarginEnd 58
int styleable Layout_layout_goneMarginLeft 59
int styleable Layout_layout_goneMarginRight 60
int styleable Layout_layout_goneMarginStart 61
int styleable Layout_layout_goneMarginTop 62
int styleable Layout_maxHeight 63
int styleable Layout_maxWidth 64
int styleable Layout_minHeight 65
int styleable Layout_minWidth 66
int[] styleable LinearLayoutCompat { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f030116, 0x7f030118, 0x7f03024d, 0x7f0302df }
int styleable LinearLayoutCompat_android_gravity 0
int styleable LinearLayoutCompat_android_orientation 1
int styleable LinearLayoutCompat_android_baselineAligned 2
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_width 1
int styleable LinearLayoutCompat_Layout_android_layout_height 2
int styleable LinearLayoutCompat_Layout_android_layout_weight 3
int[] styleable LinearProgressIndicator { 0x7f0301a4, 0x7f0301a8 }
int styleable LinearProgressIndicator_indeterminateAnimationType 0
int styleable LinearProgressIndicator_indicatorDirectionLinear 1
int[] styleable ListPopupWindow { 0x010102ac, 0x010102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable MaterialAlertDialog { 0x7f030041, 0x7f030042, 0x7f030043, 0x7f030044 }
int styleable MaterialAlertDialog_backgroundInsetBottom 0
int styleable MaterialAlertDialog_backgroundInsetEnd 1
int styleable MaterialAlertDialog_backgroundInsetStart 2
int styleable MaterialAlertDialog_backgroundInsetTop 3
int[] styleable MaterialAlertDialogTheme { 0x7f030228, 0x7f030229, 0x7f03022a, 0x7f03022b, 0x7f03022c }
int styleable MaterialAlertDialogTheme_materialAlertDialogBodyTextStyle 0
int styleable MaterialAlertDialogTheme_materialAlertDialogTheme 1
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleIconStyle 2
int styleable MaterialAlertDialogTheme_materialAlertDialogTitlePanelStyle 3
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleTextStyle 4
int[] styleable MaterialAutoCompleteTextView { 0x01010220 }
int styleable MaterialAutoCompleteTextView_android_inputType 0
int[] styleable MaterialButton { 0x010100d4, 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x010101e5, 0x7f030048, 0x7f030049, 0x7f0300ec, 0x7f03012f, 0x7f03019a, 0x7f03019c, 0x7f03019d, 0x7f03019e, 0x7f0301a0, 0x7f0301a1, 0x7f0302c4, 0x7f0302d6, 0x7f0302d9, 0x7f030308, 0x7f030309 }
int styleable MaterialButton_android_background 0
int styleable MaterialButton_android_insetLeft 1
int styleable MaterialButton_android_insetRight 2
int styleable MaterialButton_android_insetTop 3
int styleable MaterialButton_android_insetBottom 4
int styleable MaterialButton_android_checkable 5
int styleable MaterialButton_backgroundTint 6
int styleable MaterialButton_backgroundTintMode 7
int styleable MaterialButton_cornerRadius 8
int styleable MaterialButton_elevation 9
int styleable MaterialButton_icon 10
int styleable MaterialButton_iconGravity 11
int styleable MaterialButton_iconPadding 12
int styleable MaterialButton_iconSize 13
int styleable MaterialButton_iconTint 14
int styleable MaterialButton_iconTintMode 15
int styleable MaterialButton_rippleColor 16
int styleable MaterialButton_shapeAppearance 17
int styleable MaterialButton_shapeAppearanceOverlay 18
int styleable MaterialButton_strokeColor 19
int styleable MaterialButton_strokeWidth 20
int[] styleable MaterialButtonToggleGroup { 0x7f030085, 0x7f0302d4, 0x7f0302e7 }
int styleable MaterialButtonToggleGroup_checkedButton 0
int styleable MaterialButtonToggleGroup_selectionRequired 1
int styleable MaterialButtonToggleGroup_singleSelection 2
int[] styleable MaterialCalendar { 0x0101020d, 0x7f030106, 0x7f030107, 0x7f030108, 0x7f030109, 0x7f03027b, 0x7f0302b9, 0x7f0303ac, 0x7f0303ad, 0x7f0303ae }
int styleable MaterialCalendar_android_windowFullscreen 0
int styleable MaterialCalendar_dayInvalidStyle 1
int styleable MaterialCalendar_daySelectedStyle 2
int styleable MaterialCalendar_dayStyle 3
int styleable MaterialCalendar_dayTodayStyle 4
int styleable MaterialCalendar_nestedScrollable 5
int styleable MaterialCalendar_rangeFillColor 6
int styleable MaterialCalendar_yearSelectedStyle 7
int styleable MaterialCalendar_yearStyle 8
int styleable MaterialCalendar_yearTodayStyle 9
int[] styleable MaterialCalendarItem { 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x7f0301b0, 0x7f0301b9, 0x7f0301ba, 0x7f0301c1, 0x7f0301c2, 0x7f0301c6 }
int styleable MaterialCalendarItem_android_insetLeft 0
int styleable MaterialCalendarItem_android_insetRight 1
int styleable MaterialCalendarItem_android_insetTop 2
int styleable MaterialCalendarItem_android_insetBottom 3
int styleable MaterialCalendarItem_itemFillColor 4
int styleable MaterialCalendarItem_itemShapeAppearance 5
int styleable MaterialCalendarItem_itemShapeAppearanceOverlay 6
int styleable MaterialCalendarItem_itemStrokeColor 7
int styleable MaterialCalendarItem_itemStrokeWidth 8
int styleable MaterialCalendarItem_itemTextColor 9
int[] styleable MaterialCardView { 0x010101e5, 0x7f03007e, 0x7f030087, 0x7f030089, 0x7f03008a, 0x7f03008b, 0x7f0302c4, 0x7f0302d6, 0x7f0302d9, 0x7f030302, 0x7f030308, 0x7f030309 }
int styleable MaterialCardView_android_checkable 0
int styleable MaterialCardView_cardForegroundColor 1
int styleable MaterialCardView_checkedIcon 2
int styleable MaterialCardView_checkedIconMargin 3
int styleable MaterialCardView_checkedIconSize 4
int styleable MaterialCardView_checkedIconTint 5
int styleable MaterialCardView_rippleColor 6
int styleable MaterialCardView_shapeAppearance 7
int styleable MaterialCardView_shapeAppearanceOverlay 8
int styleable MaterialCardView_state_dragged 9
int styleable MaterialCardView_strokeColor 10
int styleable MaterialCardView_strokeWidth 11
int[] styleable MaterialCheckBox { 0x7f030079, 0x7f030396 }
int styleable MaterialCheckBox_buttonTint 0
int styleable MaterialCheckBox_useMaterialThemeColors 1
int[] styleable MaterialRadioButton { 0x7f030079, 0x7f030396 }
int styleable MaterialRadioButton_buttonTint 0
int styleable MaterialRadioButton_useMaterialThemeColors 1
int[] styleable MaterialShape { 0x7f0302d6, 0x7f0302d9 }
int styleable MaterialShape_shapeAppearance 0
int styleable MaterialShape_shapeAppearanceOverlay 1
int[] styleable MaterialTextAppearance { 0x010104b6, 0x0101057f, 0x7f030214 }
int styleable MaterialTextAppearance_android_letterSpacing 0
int styleable MaterialTextAppearance_android_lineHeight 1
int styleable MaterialTextAppearance_lineHeight 2
int[] styleable MaterialTextView { 0x01010034, 0x0101057f, 0x7f030214 }
int styleable MaterialTextView_android_textAppearance 0
int styleable MaterialTextView_android_lineHeight 1
int styleable MaterialTextView_lineHeight 2
int[] styleable MaterialTimePicker { 0x7f0300a8, 0x7f0301c8 }
int styleable MaterialTimePicker_clockIcon 0
int styleable MaterialTimePicker_keyboardIcon 1
int[] styleable MaterialToolbar { 0x7f030275, 0x7f03030d, 0x7f03036c }
int styleable MaterialToolbar_navigationIconTint 0
int styleable MaterialToolbar_subtitleCentered 1
int styleable MaterialToolbar_titleCentered 2
int[] styleable MenuGroup { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 }
int styleable MenuGroup_android_enabled 0
int styleable MenuGroup_android_id 1
int styleable MenuGroup_android_visible 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_checkableBehavior 5
int[] styleable MenuItem { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f03000e, 0x7f030020, 0x7f030022, 0x7f03002c, 0x7f0300d5, 0x7f0301a0, 0x7f0301a1, 0x7f03027e, 0x7f0302dd, 0x7f03037e }
int styleable MenuItem_android_icon 0
int styleable MenuItem_android_enabled 1
int styleable MenuItem_android_id 2
int styleable MenuItem_android_checked 3
int styleable MenuItem_android_visible 4
int styleable MenuItem_android_menuCategory 5
int styleable MenuItem_android_orderInCategory 6
int styleable MenuItem_android_title 7
int styleable MenuItem_android_titleCondensed 8
int styleable MenuItem_android_alphabeticShortcut 9
int styleable MenuItem_android_numericShortcut 10
int styleable MenuItem_android_checkable 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_actionLayout 13
int styleable MenuItem_actionProviderClass 14
int styleable MenuItem_actionViewClass 15
int styleable MenuItem_alphabeticModifiers 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f0302b0, 0x7f03030a }
int styleable MenuView_android_windowAnimationStyle 0
int styleable MenuView_android_itemTextAppearance 1
int styleable MenuView_android_horizontalDivider 2
int styleable MenuView_android_verticalDivider 3
int styleable MenuView_android_headerBackground 4
int styleable MenuView_android_itemBackground 5
int styleable MenuView_android_itemIconDisabledAlpha 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable MockView { 0x7f030256, 0x7f030257, 0x7f030258, 0x7f030259, 0x7f03025a, 0x7f03025b }
int styleable MockView_mock_diagonalsColor 0
int styleable MockView_mock_label 1
int styleable MockView_mock_labelBackgroundColor 2
int styleable MockView_mock_labelColor 3
int styleable MockView_mock_showDiagonals 4
int styleable MockView_mock_showLabel 5
int[] styleable Motion { 0x7f03002f, 0x7f03011d, 0x7f03026a, 0x7f03026c, 0x7f030297, 0x7f03038c }
int styleable Motion_animate_relativeTo 0
int styleable Motion_drawPath 1
int styleable Motion_motionPathRotate 2
int styleable Motion_motionStagger 3
int styleable Motion_pathMotionArc 4
int styleable Motion_transitionEasing 5
int[] styleable MotionHelper { 0x7f030280, 0x7f030283 }
int styleable MotionHelper_onHide 0
int styleable MotionHelper_onShow 1
int[] styleable MotionLayout { 0x7f030032, 0x7f0300f9, 0x7f0301d1, 0x7f03025c, 0x7f03026b, 0x7f0302e1 }
int styleable MotionLayout_applyMotionScene 0
int styleable MotionLayout_currentState 1
int styleable MotionLayout_layoutDescription 2
int styleable MotionLayout_motionDebug 3
int styleable MotionLayout_motionProgress 4
int styleable MotionLayout_showPaths 5
int[] styleable MotionScene { 0x7f03010a, 0x7f0301d2 }
int styleable MotionScene_defaultDuration 0
int styleable MotionScene_layoutDuringTransition 1
int[] styleable MotionTelltales { 0x7f030336, 0x7f030337, 0x7f030338 }
int styleable MotionTelltales_telltales_tailColor 0
int styleable MotionTelltales_telltales_tailScale 1
int styleable MotionTelltales_telltales_velocityMode 2
int[] styleable NavAction { 0x010100d0, 0x7f030111, 0x7f03013c, 0x7f030144, 0x7f0301cf, 0x7f0302a4, 0x7f0302a5, 0x7f0302a6, 0x7f0302a7, 0x7f0302a8, 0x7f0302c2 }
int styleable NavAction_android_id 0
int styleable NavAction_destination 1
int styleable NavAction_enterAnim 2
int styleable NavAction_exitAnim 3
int styleable NavAction_launchSingleTop 4
int styleable NavAction_popEnterAnim 5
int styleable NavAction_popExitAnim 6
int styleable NavAction_popUpTo 7
int styleable NavAction_popUpToInclusive 8
int styleable NavAction_popUpToSaveState 9
int styleable NavAction_restoreState 10
int[] styleable NavArgument { 0x01010003, 0x010101ed, 0x7f030034, 0x7f03027c }
int styleable NavArgument_android_name 0
int styleable NavArgument_android_defaultValue 1
int styleable NavArgument_argType 2
int styleable NavArgument_nullable 3
int[] styleable NavDeepLink { 0x010104ee, 0x7f030000, 0x7f030250, 0x7f030394 }
int styleable NavDeepLink_android_autoVerify 0
int styleable NavDeepLink_action 1
int styleable NavDeepLink_mimeType 2
int styleable NavDeepLink_uri 3
int[] styleable NavGraphNavigator { 0x7f0302f9 }
int styleable NavGraphNavigator_startDestination 0
int[] styleable NavHost { 0x7f030272 }
int styleable NavHost_navGraph 0
int[] styleable NavHostFragment { 0x7f03010b }
int styleable NavHostFragment_defaultNavHost 0
int[] styleable NavInclude { 0x7f030185 }
int styleable NavInclude_graph 0
int[] styleable NavigationBarView { 0x7f030048, 0x7f03012f, 0x7f0301af, 0x7f0301b4, 0x7f0301b5, 0x7f0301b8, 0x7f0301c4, 0x7f0301c5, 0x7f0301c6, 0x7f0301cd, 0x7f03024e }
int styleable NavigationBarView_backgroundTint 0
int styleable NavigationBarView_elevation 1
int styleable NavigationBarView_itemBackground 2
int styleable NavigationBarView_itemIconSize 3
int styleable NavigationBarView_itemIconTint 4
int styleable NavigationBarView_itemRippleColor 5
int styleable NavigationBarView_itemTextAppearanceActive 6
int styleable NavigationBarView_itemTextAppearanceInactive 7
int styleable NavigationBarView_itemTextColor 8
int styleable NavigationBarView_labelVisibilityMode 9
int styleable NavigationBarView_menu 10
int[] styleable NavigationRailView { 0x7f030188, 0x7f03024f }
int styleable NavigationRailView_headerLayout 0
int styleable NavigationRailView_menuGravity 1
int[] styleable NavigationView { 0x010100d4, 0x010100dd, 0x0101011f, 0x7f03012f, 0x7f030188, 0x7f0301af, 0x7f0301b1, 0x7f0301b3, 0x7f0301b4, 0x7f0301b5, 0x7f0301b6, 0x7f0301b9, 0x7f0301ba, 0x7f0301bb, 0x7f0301bc, 0x7f0301bd, 0x7f0301be, 0x7f0301bf, 0x7f0301c3, 0x7f0301c6, 0x7f03024e, 0x7f0302d6, 0x7f0302d9 }
int styleable NavigationView_android_background 0
int styleable NavigationView_android_fitsSystemWindows 1
int styleable NavigationView_android_maxWidth 2
int styleable NavigationView_elevation 3
int styleable NavigationView_headerLayout 4
int styleable NavigationView_itemBackground 5
int styleable NavigationView_itemHorizontalPadding 6
int styleable NavigationView_itemIconPadding 7
int styleable NavigationView_itemIconSize 8
int styleable NavigationView_itemIconTint 9
int styleable NavigationView_itemMaxLines 10
int styleable NavigationView_itemShapeAppearance 11
int styleable NavigationView_itemShapeAppearanceOverlay 12
int styleable NavigationView_itemShapeFillColor 13
int styleable NavigationView_itemShapeInsetBottom 14
int styleable NavigationView_itemShapeInsetEnd 15
int styleable NavigationView_itemShapeInsetStart 16
int styleable NavigationView_itemShapeInsetTop 17
int styleable NavigationView_itemTextAppearance 18
int styleable NavigationView_itemTextColor 19
int styleable NavigationView_menu 20
int styleable NavigationView_shapeAppearance 21
int styleable NavigationView_shapeAppearanceOverlay 22
int[] styleable Navigator { 0x01010001, 0x010100d0, 0x7f0302c7 }
int styleable Navigator_android_label 0
int styleable Navigator_android_id 1
int styleable Navigator_route 2
int[] styleable OnClick { 0x7f0300a5, 0x7f030334 }
int styleable OnClick_clickAction 0
int styleable OnClick_targetId 1
int[] styleable OnSwipe { 0x7f03011a, 0x7f03011b, 0x7f03011c, 0x7f030213, 0x7f030244, 0x7f03024b, 0x7f030270, 0x7f030279, 0x7f030284, 0x7f03037f, 0x7f030380, 0x7f030381 }
int styleable OnSwipe_dragDirection 0
int styleable OnSwipe_dragScale 1
int styleable OnSwipe_dragThreshold 2
int styleable OnSwipe_limitBoundsTo 3
int styleable OnSwipe_maxAcceleration 4
int styleable OnSwipe_maxVelocity 5
int styleable OnSwipe_moveWhenScrollAtTop 6
int styleable OnSwipe_nestedScrollFlags 7
int styleable OnSwipe_onTouchUp 8
int styleable OnSwipe_touchAnchorId 9
int styleable OnSwipe_touchAnchorSide 10
int styleable OnSwipe_touchRegionId 11
int[] styleable PopupWindow { 0x01010176, 0x010102c9, 0x7f030285 }
int styleable PopupWindow_android_popupBackground 0
int styleable PopupWindow_android_popupAnimationStyle 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f0302ff }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable PropertySet { 0x010100dc, 0x0101031f, 0x7f0301f8, 0x7f03026b, 0x7f03039a }
int styleable PropertySet_android_visibility 0
int styleable PropertySet_android_alpha 1
int styleable PropertySet_layout_constraintTag 2
int styleable PropertySet_motionProgress 3
int styleable PropertySet_visibilityMode 4
int[] styleable RadialViewGroup { 0x7f03023f }
int styleable RadialViewGroup_materialCircleRadius 0
int[] styleable RangeSlider { 0x7f030253, 0x7f030397 }
int styleable RangeSlider_minSeparation 0
int styleable RangeSlider_values 1
int[] styleable RecycleListView { 0x7f030287, 0x7f03028d }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable RecyclerView { 0x010100c4, 0x010100eb, 0x010100f1, 0x7f030158, 0x7f030159, 0x7f03015a, 0x7f03015b, 0x7f03015c, 0x7f0301d3, 0x7f0302c3, 0x7f0302ed, 0x7f0302f7 }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_clipToPadding 1
int styleable RecyclerView_android_descendantFocusability 2
int styleable RecyclerView_fastScrollEnabled 3
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 4
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 5
int styleable RecyclerView_fastScrollVerticalThumbDrawable 6
int styleable RecyclerView_fastScrollVerticalTrackDrawable 7
int styleable RecyclerView_layoutManager 8
int styleable RecyclerView_reverseLayout 9
int styleable RecyclerView_spanCount 10
int styleable RecyclerView_stackFromEnd 11
int[] styleable ScrimInsetsFrameLayout { 0x7f0301ac }
int styleable ScrimInsetsFrameLayout_insetForeground 0
int[] styleable ScrollingViewBehavior_Layout { 0x7f030058 }
int styleable ScrollingViewBehavior_Layout_behavior_overlapTop 0
int[] styleable SearchView { 0x010100da, 0x0101011f, 0x01010220, 0x01010264, 0x7f0300aa, 0x7f0300ce, 0x7f03010c, 0x7f030184, 0x7f0301a2, 0x7f0301d0, 0x7f0302b5, 0x7f0302b6, 0x7f0302cc, 0x7f0302cd, 0x7f03030b, 0x7f030314, 0x7f03039b }
int styleable SearchView_android_focusable 0
int styleable SearchView_android_maxWidth 1
int styleable SearchView_android_inputType 2
int styleable SearchView_android_imeOptions 3
int styleable SearchView_closeIcon 4
int styleable SearchView_commitIcon 5
int styleable SearchView_defaultQueryHint 6
int styleable SearchView_goIcon 7
int styleable SearchView_iconifiedByDefault 8
int styleable SearchView_layout 9
int styleable SearchView_queryBackground 10
int styleable SearchView_queryHint 11
int styleable SearchView_searchHintIcon 12
int styleable SearchView_searchIcon 13
int styleable SearchView_submitBackground 14
int styleable SearchView_suggestionRowLayout 15
int styleable SearchView_voiceIcon 16
int[] styleable ShapeAppearance { 0x7f0300e7, 0x7f0300e8, 0x7f0300e9, 0x7f0300ea, 0x7f0300eb, 0x7f0300ed, 0x7f0300ee, 0x7f0300ef, 0x7f0300f0, 0x7f0300f1 }
int styleable ShapeAppearance_cornerFamily 0
int styleable ShapeAppearance_cornerFamilyBottomLeft 1
int styleable ShapeAppearance_cornerFamilyBottomRight 2
int styleable ShapeAppearance_cornerFamilyTopLeft 3
int styleable ShapeAppearance_cornerFamilyTopRight 4
int styleable ShapeAppearance_cornerSize 5
int styleable ShapeAppearance_cornerSizeBottomLeft 6
int styleable ShapeAppearance_cornerSizeBottomRight 7
int styleable ShapeAppearance_cornerSizeTopLeft 8
int styleable ShapeAppearance_cornerSizeTopRight 9
int[] styleable ShapeableImageView { 0x7f0300dc, 0x7f0300dd, 0x7f0300de, 0x7f0300df, 0x7f0300e0, 0x7f0300e1, 0x7f0300e2, 0x7f0302d6, 0x7f0302d9, 0x7f030308, 0x7f030309 }
int styleable ShapeableImageView_contentPadding 0
int styleable ShapeableImageView_contentPaddingBottom 1
int styleable ShapeableImageView_contentPaddingEnd 2
int styleable ShapeableImageView_contentPaddingLeft 3
int styleable ShapeableImageView_contentPaddingRight 4
int styleable ShapeableImageView_contentPaddingStart 5
int styleable ShapeableImageView_contentPaddingTop 6
int styleable ShapeableImageView_shapeAppearance 7
int styleable ShapeableImageView_shapeAppearanceOverlay 8
int styleable ShapeableImageView_strokeColor 9
int styleable ShapeableImageView_strokeWidth 10
int[] styleable Slider { 0x0101000e, 0x01010024, 0x01010146, 0x010102de, 0x010102df, 0x7f030186, 0x7f030187, 0x7f0301cb, 0x7f0301cc, 0x7f03035a, 0x7f03035b, 0x7f03035c, 0x7f03035d, 0x7f03035e, 0x7f030362, 0x7f030363, 0x7f030364, 0x7f030368, 0x7f030383, 0x7f030384, 0x7f030385, 0x7f030387 }
int styleable Slider_android_enabled 0
int styleable Slider_android_value 1
int styleable Slider_android_stepSize 2
int styleable Slider_android_valueFrom 3
int styleable Slider_android_valueTo 4
int styleable Slider_haloColor 5
int styleable Slider_haloRadius 6
int styleable Slider_labelBehavior 7
int styleable Slider_labelStyle 8
int styleable Slider_thumbColor 9
int styleable Slider_thumbElevation 10
int styleable Slider_thumbRadius 11
int styleable Slider_thumbStrokeColor 12
int styleable Slider_thumbStrokeWidth 13
int styleable Slider_tickColor 14
int styleable Slider_tickColorActive 15
int styleable Slider_tickColorInactive 16
int styleable Slider_tickVisible 17
int styleable Slider_trackColor 18
int styleable Slider_trackColorActive 19
int styleable Slider_trackColorInactive 20
int styleable Slider_trackHeight 21
int[] styleable Snackbar { 0x7f0302ea, 0x7f0302eb, 0x7f0302ec }
int styleable Snackbar_snackbarButtonStyle 0
int styleable Snackbar_snackbarStyle 1
int styleable Snackbar_snackbarTextViewStyle 2
int[] styleable SnackbarLayout { 0x0101011f, 0x7f030021, 0x7f030030, 0x7f030045, 0x7f030048, 0x7f030049, 0x7f03012f, 0x7f030245 }
int styleable SnackbarLayout_android_maxWidth 0
int styleable SnackbarLayout_actionTextColorAlpha 1
int styleable SnackbarLayout_animationMode 2
int styleable SnackbarLayout_backgroundOverlayColorAlpha 3
int styleable SnackbarLayout_backgroundTint 4
int styleable SnackbarLayout_backgroundTintMode 5
int styleable SnackbarLayout_elevation 6
int styleable SnackbarLayout_maxActionInlineWidth 7
int[] styleable Spinner { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f0302ab }
int styleable Spinner_android_entries 0
int styleable Spinner_android_popupBackground 1
int styleable Spinner_android_prompt 2
int styleable Spinner_android_dropDownWidth 3
int styleable Spinner_popupTheme 4
int[] styleable SplitPairFilter { 0x7f0302b2, 0x7f0302cf, 0x7f0302d0 }
int styleable SplitPairFilter_primaryActivityName 0
int styleable SplitPairFilter_secondaryActivityAction 1
int styleable SplitPairFilter_secondaryActivityName 2
int[] styleable SplitPairRule { 0x7f0300a4, 0x7f03015d, 0x7f03015e, 0x7f0302f1, 0x7f0302f2, 0x7f0302f3, 0x7f0302f4 }
int styleable SplitPairRule_clearTop 0
int styleable SplitPairRule_finishPrimaryWithSecondary 1
int styleable SplitPairRule_finishSecondaryWithPrimary 2
int styleable SplitPairRule_splitLayoutDirection 3
int styleable SplitPairRule_splitMinSmallestWidth 4
int styleable SplitPairRule_splitMinWidth 5
int styleable SplitPairRule_splitRatio 6
int[] styleable SplitPlaceholderRule { 0x7f03029f, 0x7f0302f1, 0x7f0302f2, 0x7f0302f3, 0x7f0302f4 }
int styleable SplitPlaceholderRule_placeholderActivityName 0
int styleable SplitPlaceholderRule_splitLayoutDirection 1
int styleable SplitPlaceholderRule_splitMinSmallestWidth 2
int styleable SplitPlaceholderRule_splitMinWidth 3
int styleable SplitPlaceholderRule_splitRatio 4
int[] styleable State { 0x010100d0, 0x7f0300d3 }
int styleable State_android_id 0
int styleable State_constraints 1
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable StateSet { 0x7f03010d }
int styleable StateSet_defaultState 0
int[] styleable SwitchCompat { 0x01010124, 0x01010125, 0x01010142, 0x7f0302e2, 0x7f0302f5, 0x7f030315, 0x7f030316, 0x7f030318, 0x7f03035f, 0x7f030360, 0x7f030361, 0x7f030382, 0x7f030389, 0x7f03038a }
int styleable SwitchCompat_android_textOn 0
int styleable SwitchCompat_android_textOff 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable SwitchMaterial { 0x7f030396 }
int styleable SwitchMaterial_useMaterialThemeColors 0
int[] styleable TabItem { 0x01010002, 0x010100f2, 0x0101014f }
int styleable TabItem_android_icon 0
int styleable TabItem_android_layout 1
int styleable TabItem_android_text 2
int[] styleable TabLayout { 0x7f030319, 0x7f03031a, 0x7f03031b, 0x7f03031c, 0x7f03031d, 0x7f03031e, 0x7f03031f, 0x7f030320, 0x7f030321, 0x7f030322, 0x7f030323, 0x7f030324, 0x7f030325, 0x7f030326, 0x7f030327, 0x7f030328, 0x7f030329, 0x7f03032a, 0x7f03032b, 0x7f03032c, 0x7f03032d, 0x7f03032e, 0x7f03032f, 0x7f030331, 0x7f030332, 0x7f030333 }
int styleable TabLayout_tabBackground 0
int styleable TabLayout_tabContentStart 1
int styleable TabLayout_tabGravity 2
int styleable TabLayout_tabIconTint 3
int styleable TabLayout_tabIconTintMode 4
int styleable TabLayout_tabIndicator 5
int styleable TabLayout_tabIndicatorAnimationDuration 6
int styleable TabLayout_tabIndicatorAnimationMode 7
int styleable TabLayout_tabIndicatorColor 8
int styleable TabLayout_tabIndicatorFullWidth 9
int styleable TabLayout_tabIndicatorGravity 10
int styleable TabLayout_tabIndicatorHeight 11
int styleable TabLayout_tabInlineLabel 12
int styleable TabLayout_tabMaxWidth 13
int styleable TabLayout_tabMinWidth 14
int styleable TabLayout_tabMode 15
int styleable TabLayout_tabPadding 16
int styleable TabLayout_tabPaddingBottom 17
int styleable TabLayout_tabPaddingEnd 18
int styleable TabLayout_tabPaddingStart 19
int styleable TabLayout_tabPaddingTop 20
int styleable TabLayout_tabRippleColor 21
int styleable TabLayout_tabSelectedTextColor 22
int styleable TabLayout_tabTextAppearance 23
int styleable TabLayout_tabTextColor 24
int styleable TabLayout_tabUnboundedRipple 25
int[] styleable TextAppearance { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x01010585, 0x7f030175, 0x7f03017e, 0x7f030339, 0x7f030355 }
int styleable TextAppearance_android_textSize 0
int styleable TextAppearance_android_typeface 1
int styleable TextAppearance_android_textStyle 2
int styleable TextAppearance_android_textColor 3
int styleable TextAppearance_android_textColorHint 4
int styleable TextAppearance_android_textColorLink 5
int styleable TextAppearance_android_shadowColor 6
int styleable TextAppearance_android_shadowDx 7
int styleable TextAppearance_android_shadowDy 8
int styleable TextAppearance_android_shadowRadius 9
int styleable TextAppearance_android_fontFamily 10
int styleable TextAppearance_android_textFontWeight 11
int styleable TextAppearance_fontFamily 12
int styleable TextAppearance_fontVariationSettings 13
int styleable TextAppearance_textAllCaps 14
int styleable TextAppearance_textLocale 15
int[] styleable TextInputEditText { 0x7f030353 }
int styleable TextInputEditText_textInputLayoutFocusedRectEnabled 0
int[] styleable TextInputLayout { 0x0101000e, 0x0101009a, 0x0101011f, 0x0101013f, 0x01010150, 0x7f030062, 0x7f030063, 0x7f030064, 0x7f030065, 0x7f030066, 0x7f030067, 0x7f030068, 0x7f030069, 0x7f03006a, 0x7f03006b, 0x7f03006c, 0x7f0300f2, 0x7f0300f3, 0x7f0300f4, 0x7f0300f5, 0x7f0300f6, 0x7f0300f7, 0x7f030133, 0x7f030134, 0x7f030135, 0x7f030136, 0x7f030137, 0x7f030138, 0x7f03013d, 0x7f03013e, 0x7f03013f, 0x7f030140, 0x7f030141, 0x7f030142, 0x7f030143, 0x7f030147, 0x7f03018a, 0x7f03018b, 0x7f03018c, 0x7f03018d, 0x7f030192, 0x7f030193, 0x7f030194, 0x7f030195, 0x7f030292, 0x7f030293, 0x7f030294, 0x7f030295, 0x7f030296, 0x7f0302a0, 0x7f0302a1, 0x7f0302a2, 0x7f0302ad, 0x7f0302ae, 0x7f0302af, 0x7f0302d6, 0x7f0302d9, 0x7f0302fa, 0x7f0302fb, 0x7f0302fc, 0x7f0302fd, 0x7f0302fe, 0x7f030311, 0x7f030312, 0x7f030313 }
int styleable TextInputLayout_android_enabled 0
int styleable TextInputLayout_android_textColorHint 1
int styleable TextInputLayout_android_maxWidth 2
int styleable TextInputLayout_android_minWidth 3
int styleable TextInputLayout_android_hint 4
int styleable TextInputLayout_boxBackgroundColor 5
int styleable TextInputLayout_boxBackgroundMode 6
int styleable TextInputLayout_boxCollapsedPaddingTop 7
int styleable TextInputLayout_boxCornerRadiusBottomEnd 8
int styleable TextInputLayout_boxCornerRadiusBottomStart 9
int styleable TextInputLayout_boxCornerRadiusTopEnd 10
int styleable TextInputLayout_boxCornerRadiusTopStart 11
int styleable TextInputLayout_boxStrokeColor 12
int styleable TextInputLayout_boxStrokeErrorColor 13
int styleable TextInputLayout_boxStrokeWidth 14
int styleable TextInputLayout_boxStrokeWidthFocused 15
int styleable TextInputLayout_counterEnabled 16
int styleable TextInputLayout_counterMaxLength 17
int styleable TextInputLayout_counterOverflowTextAppearance 18
int styleable TextInputLayout_counterOverflowTextColor 19
int styleable TextInputLayout_counterTextAppearance 20
int styleable TextInputLayout_counterTextColor 21
int styleable TextInputLayout_endIconCheckable 22
int styleable TextInputLayout_endIconContentDescription 23
int styleable TextInputLayout_endIconDrawable 24
int styleable TextInputLayout_endIconMode 25
int styleable TextInputLayout_endIconTint 26
int styleable TextInputLayout_endIconTintMode 27
int styleable TextInputLayout_errorContentDescription 28
int styleable TextInputLayout_errorEnabled 29
int styleable TextInputLayout_errorIconDrawable 30
int styleable TextInputLayout_errorIconTint 31
int styleable TextInputLayout_errorIconTintMode 32
int styleable TextInputLayout_errorTextAppearance 33
int styleable TextInputLayout_errorTextColor 34
int styleable TextInputLayout_expandedHintEnabled 35
int styleable TextInputLayout_helperText 36
int styleable TextInputLayout_helperTextEnabled 37
int styleable TextInputLayout_helperTextTextAppearance 38
int styleable TextInputLayout_helperTextTextColor 39
int styleable TextInputLayout_hintAnimationEnabled 40
int styleable TextInputLayout_hintEnabled 41
int styleable TextInputLayout_hintTextAppearance 42
int styleable TextInputLayout_hintTextColor 43
int styleable TextInputLayout_passwordToggleContentDescription 44
int styleable TextInputLayout_passwordToggleDrawable 45
int styleable TextInputLayout_passwordToggleEnabled 46
int styleable TextInputLayout_passwordToggleTint 47
int styleable TextInputLayout_passwordToggleTintMode 48
int styleable TextInputLayout_placeholderText 49
int styleable TextInputLayout_placeholderTextAppearance 50
int styleable TextInputLayout_placeholderTextColor 51
int styleable TextInputLayout_prefixText 52
int styleable TextInputLayout_prefixTextAppearance 53
int styleable TextInputLayout_prefixTextColor 54
int styleable TextInputLayout_shapeAppearance 55
int styleable TextInputLayout_shapeAppearanceOverlay 56
int styleable TextInputLayout_startIconCheckable 57
int styleable TextInputLayout_startIconContentDescription 58
int styleable TextInputLayout_startIconDrawable 59
int styleable TextInputLayout_startIconTint 60
int styleable TextInputLayout_startIconTintMode 61
int styleable TextInputLayout_suffixText 62
int styleable TextInputLayout_suffixTextAppearance 63
int styleable TextInputLayout_suffixTextColor 64
int[] styleable ThemeEnforcement { 0x01010034, 0x7f030139, 0x7f03013a }
int styleable ThemeEnforcement_android_textAppearance 0
int styleable ThemeEnforcement_enforceMaterialTheme 1
int styleable ThemeEnforcement_enforceTextAppearance 2
int[] styleable Toolbar { 0x010100af, 0x01010140, 0x7f030074, 0x7f0300b2, 0x7f0300b3, 0x7f0300d6, 0x7f0300d7, 0x7f0300d8, 0x7f0300d9, 0x7f0300da, 0x7f0300db, 0x7f030226, 0x7f030227, 0x7f030246, 0x7f03024e, 0x7f030273, 0x7f030274, 0x7f0302ab, 0x7f03030c, 0x7f03030e, 0x7f03030f, 0x7f03036b, 0x7f03036f, 0x7f030370, 0x7f030371, 0x7f030372, 0x7f030373, 0x7f030374, 0x7f030375, 0x7f030376 }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_menu 14
int styleable Toolbar_navigationContentDescription 15
int styleable Toolbar_navigationIcon 16
int styleable Toolbar_popupTheme 17
int styleable Toolbar_subtitle 18
int styleable Toolbar_subtitleTextAppearance 19
int styleable Toolbar_subtitleTextColor 20
int styleable Toolbar_title 21
int styleable Toolbar_titleMargin 22
int styleable Toolbar_titleMarginBottom 23
int styleable Toolbar_titleMarginEnd 24
int styleable Toolbar_titleMarginStart 25
int styleable Toolbar_titleMarginTop 26
int styleable Toolbar_titleMargins 27
int styleable Toolbar_titleTextAppearance 28
int styleable Toolbar_titleTextColor 29
int[] styleable Tooltip { 0x01010034, 0x010100d5, 0x010100f6, 0x0101013f, 0x01010140, 0x0101014f, 0x7f030048 }
int styleable Tooltip_android_textAppearance 0
int styleable Tooltip_android_padding 1
int styleable Tooltip_android_layout_margin 2
int styleable Tooltip_android_minWidth 3
int styleable Tooltip_android_minHeight 4
int styleable Tooltip_android_text 5
int styleable Tooltip_backgroundTint 6
int[] styleable Transform { 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440 }
int styleable Transform_android_transformPivotX 0
int styleable Transform_android_transformPivotY 1
int styleable Transform_android_translationX 2
int styleable Transform_android_translationY 3
int styleable Transform_android_scaleX 4
int styleable Transform_android_scaleY 5
int styleable Transform_android_rotation 6
int styleable Transform_android_rotationX 7
int styleable Transform_android_rotationY 8
int styleable Transform_android_translationZ 9
int styleable Transform_android_elevation 10
int[] styleable Transition { 0x010100d0, 0x7f03003e, 0x7f0300d0, 0x7f0300d1, 0x7f03012b, 0x7f0301d2, 0x7f030268, 0x7f030297, 0x7f0302f8, 0x7f03038b, 0x7f03038d }
int styleable Transition_android_id 0
int styleable Transition_autoTransition 1
int styleable Transition_constraintSetEnd 2
int styleable Transition_constraintSetStart 3
int styleable Transition_duration 4
int styleable Transition_layoutDuringTransition 5
int styleable Transition_motionInterpolator 6
int styleable Transition_pathMotionArc 7
int styleable Transition_staggered 8
int styleable Transition_transitionDisable 9
int styleable Transition_transitionFlags 10
int[] styleable Variant { 0x7f0300d3, 0x7f0302be, 0x7f0302bf, 0x7f0302c0, 0x7f0302c1 }
int styleable Variant_constraints 0
int styleable Variant_region_heightLessThan 1
int styleable Variant_region_heightMoreThan 2
int styleable Variant_region_widthLessThan 3
int styleable Variant_region_widthMoreThan 4
int[] styleable View { 0x01010000, 0x010100da, 0x7f030289, 0x7f03028c, 0x7f030357 }
int styleable View_android_theme 0
int styleable View_android_focusable 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x010100d4, 0x7f030048, 0x7f030049 }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewPager2 { 0x010100c4 }
int styleable ViewPager2_android_orientation 0
int[] styleable ViewStubCompat { 0x010100d0, 0x010100f2, 0x010100f3 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_layout 1
int styleable ViewStubCompat_android_inflatedId 2
int xml backup_rules 0x7f110000
int xml data_extraction_rules 0x7f110001
int xml standalone_badge 0x7f110002
int xml standalone_badge_gravity_bottom_end 0x7f110003
int xml standalone_badge_gravity_bottom_start 0x7f110004
int xml standalone_badge_gravity_top_start 0x7f110005
int xml standalone_badge_offset 0x7f110006
