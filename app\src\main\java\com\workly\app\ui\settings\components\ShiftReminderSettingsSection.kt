package com.workly.app.ui.settings.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.workly.app.data.model.UserSettings

@Composable
fun ShiftReminderSettingsSection(
    settings: UserSettings,
    onShiftReminderModeChange: (String) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Nhắc đổi ca tuần",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            Text(
                text = "Nhắc nhở thay đổi ca làm việc cho tuần mới",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            val reminderOptions = mapOf(
                "disabled" to "Tắt",
                "weekly" to "Hàng tuần",
                "monthly" to "Hàng tháng"
            )
            
            reminderOptions.forEach { (mode, displayName) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = settings.changeShiftReminderMode == mode,
                        onClick = { onShiftReminderModeChange(mode) }
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Column {
                        Text(
                            text = displayName,
                            style = MaterialTheme.typography.bodyMedium
                        )
                        
                        Text(
                            text = when (mode) {
                                "disabled" -> "Không nhắc nhở"
                                "weekly" -> "Nhắc mỗi cuối tuần"
                                "monthly" -> "Nhắc mỗi cuối tháng"
                                else -> ""
                            },
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}
