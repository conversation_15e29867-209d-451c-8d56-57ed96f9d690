package com.workly.app.notification

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent

class NotificationDismissReceiver : BroadcastReceiver() {
    
    override fun onReceive(context: Context, intent: Intent) {
        val notificationId = intent.getIntExtra("notification_id", -1)
        
        if (notificationId != -1) {
            val notificationManager = NotificationManager(context)
            
            when (notificationId) {
                1001 -> notificationManager.dismissWeatherWarning()
                1002 -> notificationManager.dismissShiftReminder()
                else -> {
                    // Dismiss specific notification by ID
                    val systemNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
                    systemNotificationManager.cancel(notificationId)
                }
            }
        }
    }
}
