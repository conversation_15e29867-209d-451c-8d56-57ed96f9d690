package com.workly.app.ui.home.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.workly.app.data.model.Note

@Composable
fun WorkNotesSection(
    onAddNoteClick: () -> Unit,
    onEditNoteClick: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    // TODO: Get actual notes from ViewModel
    val notes = getDummyNotes()
    
    Card(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Ghi chú công việc",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                
                IconButton(
                    onClick = onAddNoteClick
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Thêm ghi chú"
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            if (notes.isEmpty()) {
                EmptyNotesState(onAddNoteClick)
            } else {
                NotesGrid(
                    notes = notes,
                    onEditClick = onEditNoteClick
                )
            }
        }
    }
}

@Composable
private fun EmptyNotesState(
    onAddNoteClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "📝",
            style = MaterialTheme.typography.headlineLarge
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = "Chưa có ghi chú nào",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer(modifier = Modifier.height(8.dp))
        TextButton(onClick = onAddNoteClick) {
            Text("Thêm ghi chú đầu tiên")
        }
    }
}

@Composable
private fun NotesGrid(
    notes: List<Note>,
    onEditClick: (String) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        notes.take(3).forEach { note ->
            NoteItem(
                note = note,
                onEditClick = { onEditClick(note.id) }
            )
        }
        
        if (notes.size > 3) {
            Text(
                text = "và ${notes.size - 3} ghi chú khác...",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(top = 4.dp)
            )
        }
    }
}

@Composable
private fun NoteItem(
    note: Note,
    onEditClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onEditClick() },
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.Top
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = note.title,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                if (note.content.isNotBlank()) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = note.content,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                
                if (note.reminderTime != null) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "⏰",
                            style = MaterialTheme.typography.bodySmall
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = note.reminderTime,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }
            
            IconButton(
                onClick = onEditClick,
                modifier = Modifier.size(24.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Edit,
                    contentDescription = "Sửa ghi chú",
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

// Dummy data for testing
private fun getDummyNotes(): List<Note> {
    return listOf(
        Note.createNew(
            title = "Họp team hàng tuần",
            content = "Chuẩn bị báo cáo tiến độ dự án",
            reminderTime = "09:00"
        ),
        Note.createNew(
            title = "Nộp báo cáo tháng",
            content = "Deadline: cuối tuần này",
            reminderTime = "14:00"
        )
    )
}
