package com.workly.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.workly.app.data.model.Note
import com.workly.app.data.model.NotePriority
import com.workly.app.data.model.Shift
import com.workly.app.data.repository.NotesRepository
import com.workly.app.data.repository.ShiftRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

class NoteDetailViewModel(
    private val notesRepository: NotesRepository,
    private val shiftRepository: ShiftRepository
) : ViewModel() {

    // State flows
    private val _note = MutableStateFlow<Note?>(null)
    val note: StateFlow<Note?> = _note.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    private val _successMessage = MutableStateFlow<String?>(null)
    val successMessage: StateFlow<String?> = _successMessage.asStateFlow()

    // Available shifts for selection
    val availableShifts = shiftRepository.getShiftsFlow()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    /**
     * Load note by ID for editing
     */
    fun loadNote(noteId: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val notes = notesRepository.getNotes()
                val note = notes.find { it.id == noteId }
                
                if (note != null) {
                    _note.value = note
                } else {
                    _error.value = "Không tìm thấy ghi chú"
                }
            } catch (e: Exception) {
                _error.value = "Lỗi khi tải ghi chú: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Create new note
     */
    fun createNote(
        title: String,
        content: String,
        priority: NotePriority,
        reminderTime: String?,
        associatedShiftIds: List<String>
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                if (title.isBlank()) {
                    _error.value = "Tiêu đề không được để trống"
                    return@launch
                }
                
                // Validate reminder time format if provided
                if (!reminderTime.isNullOrBlank() && !isValidTimeFormat(reminderTime)) {
                    _error.value = "Định dạng thời gian không hợp lệ (HH:mm)"
                    return@launch
                }
                
                val note = Note.createNew(
                    title = title.trim(),
                    content = content.trim(),
                    reminderTime = reminderTime?.takeIf { it.isNotBlank() },
                    associatedShiftIds = associatedShiftIds,
                    priority = priority
                )
                
                notesRepository.addNote(note)
                _successMessage.value = "Đã tạo ghi chú mới"
                
            } catch (e: Exception) {
                _error.value = "Lỗi khi tạo ghi chú: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Update existing note
     */
    fun updateNote(
        noteId: String,
        title: String,
        content: String,
        priority: NotePriority,
        reminderTime: String?,
        associatedShiftIds: List<String>
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                if (title.isBlank()) {
                    _error.value = "Tiêu đề không được để trống"
                    return@launch
                }
                
                // Validate reminder time format if provided
                if (!reminderTime.isNullOrBlank() && !isValidTimeFormat(reminderTime)) {
                    _error.value = "Định dạng thời gian không hợp lệ (HH:mm)"
                    return@launch
                }
                
                val currentNote = _note.value
                if (currentNote == null) {
                    _error.value = "Không tìm thấy ghi chú để cập nhật"
                    return@launch
                }
                
                val updatedNote = currentNote.copy(
                    title = title.trim(),
                    content = content.trim(),
                    reminderTime = reminderTime?.takeIf { it.isNotBlank() },
                    associatedShiftIds = associatedShiftIds,
                    priority = priority,
                    updatedAt = getCurrentTimestamp()
                )
                
                notesRepository.updateNote(updatedNote)
                _successMessage.value = "Đã cập nhật ghi chú"
                
            } catch (e: Exception) {
                _error.value = "Lỗi khi cập nhật ghi chú: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Delete note
     */
    fun deleteNote(noteId: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                notesRepository.deleteNote(noteId)
                _successMessage.value = "Đã xóa ghi chú"
            } catch (e: Exception) {
                _error.value = "Lỗi khi xóa ghi chú: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Validate time format (HH:mm)
     */
    private fun isValidTimeFormat(time: String): Boolean {
        return try {
            val parts = time.split(":")
            if (parts.size != 2) return false
            
            val hour = parts[0].toIntOrNull() ?: return false
            val minute = parts[1].toIntOrNull() ?: return false
            
            hour in 0..23 && minute in 0..59
        } catch (e: Exception) {
            false
        }
    }

    /**
     * Get current timestamp
     */
    private fun getCurrentTimestamp(): String {
        return kotlinx.datetime.Clock.System.now().toString()
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _error.value = null
    }

    /**
     * Clear success message
     */
    fun clearSuccessMessage() {
        _successMessage.value = null
    }

    /**
     * Get note validation errors
     */
    fun validateNote(
        title: String,
        reminderTime: String?
    ): List<String> {
        val errors = mutableListOf<String>()
        
        if (title.isBlank()) {
            errors.add("Tiêu đề không được để trống")
        }
        
        if (!reminderTime.isNullOrBlank() && !isValidTimeFormat(reminderTime)) {
            errors.add("Định dạng thời gian không hợp lệ (HH:mm)")
        }
        
        return errors
    }

    /**
     * Get suggested reminder times based on shifts
     */
    fun getSuggestedReminderTimes(shifts: List<Shift>): List<String> {
        return shifts.mapNotNull { shift ->
            // Suggest 30 minutes before shift start
            try {
                val parts = shift.startTime.split(":")
                val hour = parts[0].toInt()
                val minute = parts[1].toInt()
                
                val reminderMinute = minute - 30
                val reminderHour = if (reminderMinute < 0) {
                    if (hour == 0) 23 else hour - 1
                } else {
                    hour
                }
                val adjustedMinute = if (reminderMinute < 0) reminderMinute + 60 else reminderMinute
                
                "${reminderHour.toString().padStart(2, '0')}:${adjustedMinute.toString().padStart(2, '0')}"
            } catch (e: Exception) {
                null
            }
        }.distinct().sorted()
    }
}
