package com.workly.app.business

import com.workly.app.data.model.Shift
import kotlinx.datetime.LocalTime

class ShiftValidator {
    
    data class ValidationResult(
        val isValid: Boolean,
        val errors: List<ValidationError>
    )
    
    data class ValidationError(
        val field: String,
        val message: String
    )
    
    /**
     * Validate shift data before saving
     */
    fun validateShift(
        shift: Shift,
        existingShifts: List<Shift> = emptyList(),
        isEditing: Boolean = false
    ): ValidationResult {
        val errors = mutableListOf<ValidationError>()
        
        // Validate name
        validateName(shift.name, existingShifts, shift.id, isEditing)?.let { errors.add(it) }
        
        // Validate times
        validateTimes(shift)?.let { errors.addAll(it) }
        
        // Validate days
        validateDays(shift.daysApplied)?.let { errors.add(it) }
        
        // Validate numeric values
        validateNumericValues(shift)?.let { errors.addAll(it) }
        
        return ValidationResult(
            isValid = errors.isEmpty(),
            errors = errors
        )
    }
    
    private fun validateName(
        name: String,
        existingShifts: List<Shift>,
        currentShiftId: String,
        isEditing: <PERSON><PERSON><PERSON>
    ): ValidationError? {
        // Check if name is empty
        if (name.isBlank()) {
            return ValidationError("name", "Tên ca không được để trống")
        }
        
        // Check name length
        if (name.length > 50) {
            return ValidationError("name", "Tên ca không được quá 50 ký tự")
        }
        
        // Check for duplicate names
        val isDuplicate = existingShifts.any { shift ->
            shift.name.equals(name, ignoreCase = true) && 
            (!isEditing || shift.id != currentShiftId)
        }
        
        if (isDuplicate) {
            return ValidationError("name", "Tên ca đã tồn tại")
        }
        
        return null
    }
    
    private fun validateTimes(shift: Shift): List<ValidationError>? {
        val errors = mutableListOf<ValidationError>()
        
        // Validate time format
        val timeFields = mapOf(
            "startTime" to shift.startTime,
            "endTime" to shift.endTime,
            "departureTime" to shift.departureTime,
            "officeEndTime" to shift.officeEndTime
        )
        
        timeFields.forEach { (field, timeString) ->
            if (!isValidTimeFormat(timeString)) {
                errors.add(ValidationError(field, "Định dạng thời gian không hợp lệ (HH:mm)"))
            }
        }
        
        if (errors.isNotEmpty()) return errors
        
        // Parse times for logical validation
        try {
            val startTime = LocalTime.parse(shift.startTime)
            val endTime = LocalTime.parse(shift.endTime)
            val departureTime = LocalTime.parse(shift.departureTime)
            val officeEndTime = LocalTime.parse(shift.officeEndTime)
            
            // Validate departure time is before start time (for same day shifts)
            if (!shift.isOvernightShift() && departureTime >= startTime) {
                errors.add(ValidationError("departureTime", "Giờ xuất phát phải trước giờ bắt đầu"))
            }
            
            // Validate office end time is between start and end time
            if (!shift.isOvernightShift()) {
                if (officeEndTime <= startTime || officeEndTime > endTime) {
                    errors.add(ValidationError("officeEndTime", "Giờ kết thúc hành chính phải nằm trong ca làm việc"))
                }
            }
            
            // For overnight shifts, validate logic differently
            if (shift.isOvernightShift()) {
                // Start time should be later in the day than end time
                if (startTime <= endTime) {
                    errors.add(ValidationError("endTime", "Ca qua đêm: Giờ kết thúc phải nhỏ hơn giờ bắt đầu"))
                }
            }
            
        } catch (e: Exception) {
            errors.add(ValidationError("time", "Lỗi xử lý thời gian: ${e.message}"))
        }
        
        return if (errors.isEmpty()) null else errors
    }
    
    private fun validateDays(daysApplied: List<String>): ValidationError? {
        if (daysApplied.isEmpty()) {
            return ValidationError("daysApplied", "Phải chọn ít nhất một ngày trong tuần")
        }
        
        val validDays = Shift.DAYS_OF_WEEK
        val invalidDays = daysApplied.filter { it !in validDays }
        
        if (invalidDays.isNotEmpty()) {
            return ValidationError("daysApplied", "Ngày không hợp lệ: ${invalidDays.joinToString()}")
        }
        
        return null
    }
    
    private fun validateNumericValues(shift: Shift): List<ValidationError>? {
        val errors = mutableListOf<ValidationError>()
        
        // Validate reminder times
        if (shift.remindBeforeStart < 0 || shift.remindBeforeStart > 120) {
            errors.add(ValidationError("remindBeforeStart", "Thời gian nhắc trước phải từ 0-120 phút"))
        }
        
        if (shift.remindAfterEnd < 0 || shift.remindAfterEnd > 120) {
            errors.add(ValidationError("remindAfterEnd", "Thời gian nhắc sau phải từ 0-120 phút"))
        }
        
        // Validate break time
        if (shift.breakMinutes < 0 || shift.breakMinutes > 480) {
            errors.add(ValidationError("breakMinutes", "Thời gian nghỉ phải từ 0-480 phút"))
        }
        
        // Validate thresholds
        if (shift.lateThresholdMinutes < 0 || shift.lateThresholdMinutes > 60) {
            errors.add(ValidationError("lateThresholdMinutes", "Ngưỡng muộn phải từ 0-60 phút"))
        }
        
        if (shift.earlyThresholdMinutes < 0 || shift.earlyThresholdMinutes > 60) {
            errors.add(ValidationError("earlyThresholdMinutes", "Ngưỡng sớm phải từ 0-60 phút"))
        }
        
        // Validate penalty rounding
        if (shift.penaltyRoundingMinutes < 0 || shift.penaltyRoundingMinutes > 60) {
            errors.add(ValidationError("penaltyRoundingMinutes", "Làm tròn phạt phải từ 0-60 phút"))
        }
        
        return if (errors.isEmpty()) null else errors
    }
    
    private fun isValidTimeFormat(timeString: String): Boolean {
        return try {
            LocalTime.parse(timeString)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Validate if a shift can be deleted
     */
    fun canDeleteShift(shift: Shift, isActiveShift: Boolean): ValidationResult {
        val errors = mutableListOf<ValidationError>()
        
        if (isActiveShift) {
            errors.add(ValidationError("delete", "Không thể xóa ca đang hoạt động. Vui lòng chọn ca khác trước."))
        }
        
        return ValidationResult(
            isValid = errors.isEmpty(),
            errors = errors
        )
    }
    
    /**
     * Get suggested values for a new shift
     */
    fun getDefaultShiftValues(): Shift {
        return Shift.createNew(
            name = "Ca mới",
            startTime = "08:00",
            officeEndTime = "17:00",
            endTime = "17:30",
            departureTime = "07:30",
            daysApplied = listOf("Mon", "Tue", "Wed", "Thu", "Fri"),
            remindBeforeStart = 15,
            remindAfterEnd = 15,
            showPunch = false,
            breakMinutes = 60,
            lateThresholdMinutes = 5,
            earlyThresholdMinutes = 5,
            penaltyRoundingMinutes = 30
        )
    }

    /**
     * Validate shift for specific shift types
     */
    fun validateShiftType(shift: Shift): ValidationResult {
        return when {
            isNightShift(shift) -> validateNightShift(shift)
            isLongShift(shift) -> validateLongShift(shift)
            isWeekendShift(shift) -> validateWeekendShift(shift)
            else -> validateStandardShift(shift)
        }
    }

    /**
     * Check if this is a night shift (crosses midnight)
     */
    fun isNightShift(shift: Shift): Boolean {
        val startHour = shift.startTime.split(":")[0].toIntOrNull() ?: 0
        val endHour = shift.officeEndTime.split(":")[0].toIntOrNull() ?: 0

        return startHour > endHour || startHour >= 22 || endHour <= 6
    }

    /**
     * Check if this is a long shift (more than 10 hours)
     */
    fun isLongShift(shift: Shift): Boolean {
        return calculateShiftDurationMinutes(shift) > 600 // 10 hours
    }

    /**
     * Check if this shift includes weekends
     */
    fun isWeekendShift(shift: Shift): Boolean {
        return shift.daysApplied.contains("Sat") || shift.daysApplied.contains("Sun")
    }

    /**
     * Validate night shift specific requirements
     */
    private fun validateNightShift(shift: Shift): ValidationResult {
        val errors = mutableListOf<String>()

        // Night shifts should have longer break times
        if (shift.breakMinutes < 60) {
            errors.add("Ca đêm nên có thời gian nghỉ ít nhất 60 phút")
        }

        // Night shifts should have longer reminder times
        if (shift.remindBeforeStart < 30) {
            errors.add("Ca đêm nên có thời gian nhắc nhở trước khi bắt đầu ít nhất 30 phút")
        }

        // Night shifts should have more lenient late threshold
        if (shift.lateThresholdMinutes < 10) {
            errors.add("Ca đêm nên có ngưỡng đi muộn ít nhất 10 phút")
        }

        return if (errors.isEmpty()) {
            ValidationResult.Success
        } else {
            ValidationResult.Error(errors)
        }
    }

    /**
     * Validate long shift specific requirements
     */
    private fun validateLongShift(shift: Shift): ValidationResult {
        val errors = mutableListOf<String>()

        // Long shifts should have longer break times
        if (shift.breakMinutes < 90) {
            errors.add("Ca dài (>10 giờ) nên có thời gian nghỉ ít nhất 90 phút")
        }

        // Long shifts should have longer reminder times
        if (shift.remindBeforeStart < 60) {
            errors.add("Ca dài nên có thời gian nhắc nhở trước khi bắt đầu ít nhất 60 phút")
        }

        return if (errors.isEmpty()) {
            ValidationResult.Success
        } else {
            ValidationResult.Error(errors)
        }
    }

    /**
     * Validate weekend shift specific requirements
     */
    private fun validateWeekendShift(shift: Shift): ValidationResult {
        val errors = mutableListOf<String>()

        // Weekend shifts might have different requirements
        // This is a placeholder for future weekend-specific validations

        return ValidationResult.Success
    }

    /**
     * Validate standard shift requirements
     */
    private fun validateStandardShift(shift: Shift): ValidationResult {
        // Use existing validation logic
        return validateShift(shift)
    }

    /**
     * Get recommendations for improving a shift
     */
    fun getShiftRecommendations(shift: Shift): List<String> {
        val recommendations = mutableListOf<String>()

        when {
            isNightShift(shift) -> {
                if (shift.breakMinutes < 60) {
                    recommendations.add("Tăng thời gian nghỉ lên 60-90 phút cho ca đêm")
                }
                if (shift.remindBeforeStart < 30) {
                    recommendations.add("Tăng thời gian nhắc nhở lên 30-60 phút cho ca đêm")
                }
            }
            isLongShift(shift) -> {
                if (shift.breakMinutes < 90) {
                    recommendations.add("Tăng thời gian nghỉ lên 90-120 phút cho ca dài")
                }
                recommendations.add("Xem xét chia ca dài thành 2 ca ngắn hơn")
            }
        }

        // General recommendations
        val duration = calculateShiftDurationMinutes(shift)
        if (duration < 240) { // Less than 4 hours
            recommendations.add("Ca làm việc quá ngắn, xem xét tăng thời gian")
        } else if (duration > 720) { // More than 12 hours
            recommendations.add("Ca làm việc quá dài, xem xét giảm thời gian hoặc tăng thời gian nghỉ")
        }

        return recommendations
    }
}
