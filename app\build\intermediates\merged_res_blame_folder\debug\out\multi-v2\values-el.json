{"logs": [{"outputFile": "com.workly.app-mergeDebugResources-77:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\631285464bc3699b5e0c057776a78762\\transformed\\ui-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,391,496,588,669,763,852,942,1012,1080,1161,1243,1318,1397,1467", "endColumns": "98,85,100,104,91,80,93,88,89,69,67,80,81,74,78,69,122", "endOffsets": "199,285,386,491,583,664,758,847,937,1007,1075,1156,1238,1313,1392,1462,1585"}, "to": {"startLines": "48,49,82,83,85,90,91,140,141,142,143,145,146,149,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4649,4748,8616,8717,8905,9324,9405,13504,13593,13683,13753,13912,13993,14247,14607,14686,14756", "endColumns": "98,85,100,104,91,80,93,88,89,69,67,80,81,74,78,69,122", "endOffsets": "4743,4829,8712,8817,8992,9400,9494,13588,13678,13748,13816,13988,14070,14317,14681,14751,14874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\870103ce12d4503c553c2f969e6a0d11\\transformed\\navigation-ui-2.7.5\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,172", "endColumns": "116,121", "endOffsets": "167,289"}, "to": {"startLines": "138,139", "startColumns": "4,4", "startOffsets": "13265,13382", "endColumns": "116,121", "endOffsets": "13377,13499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9209685f1972143d9ab1eeffcca4ab41\\transformed\\core-1.12.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3609,3707,3810,3910,4013,4121,4227,14407", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "3702,3805,3905,4008,4116,4222,4339,14503"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d56bc5ab2f06c8c057783f978bdff8f7\\transformed\\material3-1.1.2\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,295,405,523,603,702,819,966,1090,1244,1330,1426,1521,1622,1736,1858,1959,2097,2229,2369,2545,2679,2795,2919,3040,3136,3231,3363,3496,3598,3700,3806,3945,4094,4204,4305,4388,4467,4553,4638,4737,4813,4892,4987,5085,5178,5272,5355,5457,5552,5649,5766,5842,5944", "endColumns": "119,119,109,117,79,98,116,146,123,153,85,95,94,100,113,121,100,137,131,139,175,133,115,123,120,95,94,131,132,101,101,105,138,148,109,100,82,78,85,84,98,75,78,94,97,92,93,82,101,94,96,116,75,101,102", "endOffsets": "170,290,400,518,598,697,814,961,1085,1239,1325,1421,1516,1617,1731,1853,1954,2092,2224,2364,2540,2674,2790,2914,3035,3131,3226,3358,3491,3593,3695,3801,3940,4089,4199,4300,4383,4462,4548,4633,4732,4808,4887,4982,5080,5173,5267,5350,5452,5547,5644,5761,5837,5939,6042"}, "to": {"startLines": "33,34,35,36,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,84,87,147,150,152,156,157,158,159,160,161,162,163,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3056,3176,3296,3406,4834,4914,5013,5130,5277,5401,5555,5641,5737,5832,5933,6047,6169,6270,6408,6540,6680,6856,6990,7106,7230,7351,7447,7542,7674,7807,7909,8011,8117,8256,8405,8515,8822,9063,14075,14322,14508,14879,14955,15034,15129,15227,15320,15414,15497,15599,15694,15791,15908,15984,16086", "endColumns": "119,119,109,117,79,98,116,146,123,153,85,95,94,100,113,121,100,137,131,139,175,133,115,123,120,95,94,131,132,101,101,105,138,148,109,100,82,78,85,84,98,75,78,94,97,92,93,82,101,94,96,116,75,101,102", "endOffsets": "3171,3291,3401,3519,4909,5008,5125,5272,5396,5550,5636,5732,5827,5928,6042,6164,6265,6403,6535,6675,6851,6985,7101,7225,7346,7442,7537,7669,7802,7904,8006,8112,8251,8400,8510,8611,8900,9137,14156,14402,14602,14950,15029,15124,15222,15315,15409,15492,15594,15689,15786,15903,15979,16081,16184"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b5d7701da16b09423affca14e07f926c\\transformed\\material-1.4.0-beta01\\res\\values-el\\values-el.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,225,310,412,529,615,681,781,863,926,1017,1082,1144,1213,1275,1329,1467,1524,1585,1639,1712,1865,1950,2034,2143,2224,2309,2399,2466,2532,2610,2695,2780,2852,2932,3012,3083,3175,3247,3344,3441,3515,3589,3691,3747,3819,3907,3999,4061,4125,4188,4304,4412,4521,4629", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,84,101,116,85,65,99,81,62,90,64,61,68,61,53,137,56,60,53,72,152,84,83,108,80,84,89,66,65,77,84,84,71,79,79,70,91,71,96,96,73,73,101,55,71,87,91,61,63,62,115,107,108,107,90", "endOffsets": "220,305,407,524,610,676,776,858,921,1012,1077,1139,1208,1270,1324,1462,1519,1580,1634,1707,1860,1945,2029,2138,2219,2304,2394,2461,2527,2605,2690,2775,2847,2927,3007,3078,3170,3242,3339,3436,3510,3584,3686,3742,3814,3902,3994,4056,4120,4183,4299,4407,4516,4624,4715"}, "to": {"startLines": "2,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3524,4344,4446,4563,8997,9142,9242,9499,9562,9653,9718,9780,9849,9911,9965,10103,10160,10221,10275,10348,10501,10586,10670,10779,10860,10945,11035,11102,11168,11246,11331,11416,11488,11568,11648,11719,11811,11883,11980,12077,12151,12225,12327,12383,12455,12543,12635,12697,12761,12824,12940,13048,13157,13821", "endLines": "5,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "endColumns": "12,84,101,116,85,65,99,81,62,90,64,61,68,61,53,137,56,60,53,72,152,84,83,108,80,84,89,66,65,77,84,84,71,79,79,70,91,71,96,96,73,73,101,55,71,87,91,61,63,62,115,107,108,107,90", "endOffsets": "270,3604,4441,4558,4644,9058,9237,9319,9557,9648,9713,9775,9844,9906,9960,10098,10155,10216,10270,10343,10496,10581,10665,10774,10855,10940,11030,11097,11163,11241,11326,11411,11483,11563,11643,11714,11806,11878,11975,12072,12146,12220,12322,12378,12450,12538,12630,12692,12756,12819,12935,13043,13152,13260,13907"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\85666ebb484fc6a37ee9760c5f520352\\transformed\\appcompat-1.2.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "275,393,504,621,706,812,935,1024,1109,1200,1293,1388,1482,1582,1675,1770,1867,1958,2049,2134,2245,2354,2456,2567,2677,2785,2956,14161", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "388,499,616,701,807,930,1019,1104,1195,1288,1383,1477,1577,1670,1765,1862,1953,2044,2129,2240,2349,2451,2562,2672,2780,2951,3051,14242"}}]}]}