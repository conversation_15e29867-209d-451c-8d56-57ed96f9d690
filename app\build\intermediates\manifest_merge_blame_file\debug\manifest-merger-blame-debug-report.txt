1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.workly.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:6:5-67
12-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:7:5-79
13-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:8:5-66
14-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:8:22-63
15    <uses-permission android:name="android.permission.WAKE_LOCK" />
15-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:9:5-68
15-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:9:22-65
16    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
16-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:10:5-81
16-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:10:22-78
17    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
17-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:11:5-79
17-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:11:22-76
18    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
18-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:12:5-74
18-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:12:22-71
19    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
19-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:13:5-77
19-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:13:22-74
20    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
20-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:14:5-79
20-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:14:22-76
21    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
21-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:15:5-81
21-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:15:22-78
22    <uses-permission
22-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:16:5-17:38
23        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
23-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:16:22-78
24        android:maxSdkVersion="28" />
24-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:17:9-35
25    <uses-permission
25-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:18:5-19:38
26        android:name="android.permission.READ_EXTERNAL_STORAGE"
26-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:18:22-77
27        android:maxSdkVersion="32" />
27-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:19:9-35
28    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
28-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
28-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:22-74
29
30    <permission
30-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
31        android:name="com.workly.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
31-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
32        android:protectionLevel="signature" />
32-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
33
34    <uses-permission android:name="com.workly.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
34-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
34-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
35
36    <application
36-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:21:5-90:19
37        android:name="com.workly.app.WorklyApplication"
37-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:22:9-42
38        android:allowBackup="true"
38-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:23:9-35
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\9209685f1972143d9ab1eeffcca4ab41\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
40        android:dataExtractionRules="@xml/data_extraction_rules"
40-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:24:9-65
41        android:debuggable="true"
42        android:extractNativeLibs="false"
43        android:fullBackupContent="@xml/backup_rules"
43-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:25:9-54
44        android:icon="@mipmap/ic_launcher"
44-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:26:9-43
45        android:label="@string/app_name"
45-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:27:9-41
46        android:roundIcon="@mipmap/ic_launcher_round"
46-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:28:9-54
47        android:supportsRtl="true"
47-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:29:9-35
48        android:theme="@style/Theme.Workly" >
48-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:30:9-44
49        <activity
49-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:33:9-42:20
50            android:name="com.workly.app.ui.MainActivity"
50-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:34:13-44
51            android:exported="true"
51-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:35:13-36
52            android:label="@string/app_name"
52-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:36:13-45
53            android:theme="@style/Theme.Workly" >
53-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:37:13-48
54            <intent-filter>
54-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:38:13-41:29
55                <action android:name="android.intent.action.MAIN" />
55-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:39:17-69
55-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:39:25-66
56
57                <category android:name="android.intent.category.LAUNCHER" />
57-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:40:17-77
57-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:40:27-74
58            </intent-filter>
59        </activity>
60
61        <!-- Alarm Activity -->
62        <activity
62-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:45:9-51:57
63            android:name="com.workly.app.ui.alarm.AlarmActivity"
63-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:46:13-51
64            android:exported="false"
64-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:47:13-37
65            android:launchMode="singleTop"
65-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:48:13-43
66            android:showOnLockScreen="true"
66-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:49:13-44
67            android:theme="@style/Theme.Workly.Alarm"
67-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:51:13-54
68            android:turnScreenOn="true" />
68-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:50:13-40
69
70        <!-- Alarm Receiver -->
71        <receiver
71-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:54:9-60:20
72            android:name="com.workly.app.alarm.AlarmReceiver"
72-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:55:13-48
73            android:exported="false" >
73-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:56:13-37
74            <intent-filter>
74-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:57:13-59:29
75                <action android:name="com.workly.app.ALARM_ACTION" />
75-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:58:17-70
75-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:58:25-67
76            </intent-filter>
77        </receiver>
78
79        <!-- Boot Receiver -->
80        <receiver
80-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:63:9-72:20
81            android:name="com.workly.app.alarm.BootReceiver"
81-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:64:13-47
82            android:exported="false" >
82-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:65:13-37
83            <intent-filter android:priority="1000" >
83-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:66:13-71:29
83-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:66:28-51
84                <action android:name="android.intent.action.BOOT_COMPLETED" />
84-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:67:17-79
84-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:67:25-76
85                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
85-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:68:17-84
85-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:68:25-81
86                <action android:name="android.intent.action.PACKAGE_REPLACED" />
86-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:69:17-81
86-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:69:25-78
87
88                <data android:scheme="package" />
88-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:70:17-50
88-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:70:23-47
89            </intent-filter>
90        </receiver>
91
92        <!-- Notification Dismiss Receiver -->
93        <receiver
93-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:75:9-77:40
94            android:name="com.workly.app.notification.NotificationDismissReceiver"
94-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:76:13-69
95            android:exported="false" />
95-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:77:13-37
96
97        <!-- Work Manager -->
98        <provider
99            android:name="androidx.startup.InitializationProvider"
99-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:81:13-67
100            android:authorities="com.workly.app.androidx-startup"
100-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:82:13-68
101            android:exported="false" >
101-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:83:13-37
102            <meta-data
102-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:85:13-87:52
103                android:name="androidx.work.WorkManagerInitializer"
103-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:86:17-68
104                android:value="androidx.startup" />
104-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:87:17-49
105            <meta-data
105-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b1b8179c4e142f5c9a8430a2c3ceea5\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
106                android:name="androidx.emoji2.text.EmojiCompatInitializer"
106-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b1b8179c4e142f5c9a8430a2c3ceea5\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
107                android:value="androidx.startup" />
107-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b1b8179c4e142f5c9a8430a2c3ceea5\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
108            <meta-data
108-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fb2cadf6a9c20bd881ef628d816b94d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
109                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
109-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fb2cadf6a9c20bd881ef628d816b94d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
110                android:value="androidx.startup" />
110-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fb2cadf6a9c20bd881ef628d816b94d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
111            <meta-data
111-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
112                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
112-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
113                android:value="androidx.startup" />
113-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
114        </provider>
115
116        <service
116-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
117            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
117-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
118            android:directBootAware="false"
118-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
119            android:enabled="@bool/enable_system_alarm_service_default"
119-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
120            android:exported="false" />
120-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
121        <service
121-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
122            android:name="androidx.work.impl.background.systemjob.SystemJobService"
122-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
123            android:directBootAware="false"
123-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
124            android:enabled="@bool/enable_system_job_service_default"
124-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
125            android:exported="true"
125-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
126            android:permission="android.permission.BIND_JOB_SERVICE" />
126-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
127        <service
127-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
128            android:name="androidx.work.impl.foreground.SystemForegroundService"
128-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
129            android:directBootAware="false"
129-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
130            android:enabled="@bool/enable_system_foreground_service_default"
130-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
131            android:exported="false" />
131-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
132
133        <receiver
133-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
134            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
134-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
135            android:directBootAware="false"
135-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
136            android:enabled="true"
136-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
137            android:exported="false" />
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
138        <receiver
138-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
139            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
139-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
140            android:directBootAware="false"
140-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
141            android:enabled="false"
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
142            android:exported="false" >
142-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
143            <intent-filter>
143-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
144                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
144-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
144-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
145                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
146            </intent-filter>
147        </receiver>
148        <receiver
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
149            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
150            android:directBootAware="false"
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
151            android:enabled="false"
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
152            android:exported="false" >
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
153            <intent-filter>
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
154                <action android:name="android.intent.action.BATTERY_OKAY" />
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
155                <action android:name="android.intent.action.BATTERY_LOW" />
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
156            </intent-filter>
157        </receiver>
158        <receiver
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
159            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
160            android:directBootAware="false"
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
161            android:enabled="false"
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
162            android:exported="false" >
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
163            <intent-filter>
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
164                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
165                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
166            </intent-filter>
167        </receiver>
168        <receiver
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
169            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
170            android:directBootAware="false"
170-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
171            android:enabled="false"
171-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
172            android:exported="false" >
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
173            <intent-filter>
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
174                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
175            </intent-filter>
176        </receiver>
177        <receiver
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
178            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
179            android:directBootAware="false"
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
180            android:enabled="false"
180-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
181            android:exported="false" >
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
182            <intent-filter>
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
183                <action android:name="android.intent.action.BOOT_COMPLETED" />
183-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:67:17-79
183-->G:\IT\PROJECT-IDEAL\Workly_android_new\app\src\main\AndroidManifest.xml:67:25-76
184                <action android:name="android.intent.action.TIME_SET" />
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
185                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
186            </intent-filter>
187        </receiver>
188        <receiver
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
189            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
190            android:directBootAware="false"
190-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
191            android:enabled="@bool/enable_system_alarm_service_default"
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
192            android:exported="false" >
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
193            <intent-filter>
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
194                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
195            </intent-filter>
196        </receiver>
197        <receiver
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
198            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
199            android:directBootAware="false"
199-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
200            android:enabled="true"
200-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
201            android:exported="true"
201-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
202            android:permission="android.permission.DUMP" >
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
203            <intent-filter>
203-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
204                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
204-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
204-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\68376da0eeb9b4f595296a0b33c6e15b\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
205            </intent-filter>
206        </receiver>
207
208        <activity
208-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\69a61ce86c778bbae23854f9e31e5eb0\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
209            android:name="androidx.activity.ComponentActivity"
209-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\69a61ce86c778bbae23854f9e31e5eb0\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
210            android:exported="true" />
210-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\69a61ce86c778bbae23854f9e31e5eb0\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
211        <activity
211-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\191dab1375b735307b2b6d64637a3912\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
212            android:name="androidx.compose.ui.tooling.PreviewActivity"
212-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\191dab1375b735307b2b6d64637a3912\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
213            android:exported="true" />
213-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\191dab1375b735307b2b6d64637a3912\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
214
215        <uses-library
215-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e8ad4fd69f735c0e2b9d9ded7f214bc\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
216            android:name="androidx.window.extensions"
216-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e8ad4fd69f735c0e2b9d9ded7f214bc\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
217            android:required="false" />
217-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e8ad4fd69f735c0e2b9d9ded7f214bc\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
218        <uses-library
218-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e8ad4fd69f735c0e2b9d9ded7f214bc\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
219            android:name="androidx.window.sidecar"
219-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e8ad4fd69f735c0e2b9d9ded7f214bc\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
220            android:required="false" />
220-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e8ad4fd69f735c0e2b9d9ded7f214bc\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
221
222        <service
222-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8daae7748867931ad677aed9ea5a82b8\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
223            android:name="androidx.room.MultiInstanceInvalidationService"
223-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8daae7748867931ad677aed9ea5a82b8\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
224            android:directBootAware="true"
224-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8daae7748867931ad677aed9ea5a82b8\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
225            android:exported="false" />
225-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8daae7748867931ad677aed9ea5a82b8\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
226
227        <receiver
227-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
228            android:name="androidx.profileinstaller.ProfileInstallReceiver"
228-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
229            android:directBootAware="false"
229-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
230            android:enabled="true"
230-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
231            android:exported="true"
231-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
232            android:permission="android.permission.DUMP" >
232-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
233            <intent-filter>
233-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
234                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
234-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
234-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
235            </intent-filter>
236            <intent-filter>
236-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
237                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
237-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
237-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
238            </intent-filter>
239            <intent-filter>
239-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
240                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
240-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
240-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
241            </intent-filter>
242            <intent-filter>
242-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
243                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
243-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
243-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\349d176b5d147406d261fd9c7e378065\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
244            </intent-filter>
245        </receiver>
246    </application>
247
248</manifest>
