package com.workly.app.business

import com.workly.app.data.model.Note
import com.workly.app.data.model.NotePriority
import com.workly.app.data.model.Shift
import kotlinx.datetime.*
import java.time.LocalTime
import java.time.format.DateTimeFormatter

/**
 * Manages note prioritization and sorting logic
 * Implements "Slot Đảm bảo + Ưu tiên <PERSON>ắp xếp" algorithm
 */
class NotePriorityManager {
    
    /**
     * Sort notes using "Guaranteed Slot + Priority Sorting" algorithm
     * 
     * Priority order:
     * 1. Notes with upcoming reminders today (sorted by reminder time)
     * 2. High priority notes (sorted by creation time)
     * 3. Notes with reminders on other days (sorted by priority, then creation time)
     * 4. Regular notes (sorted by update time, newest first)
     */
    fun sortNotesWithPriority(
        notes: List<Note>,
        currentDayOfWeek: String,
        activeShift: Shift?,
        currentTime: String = getCurrentTime()
    ): List<Note> {
        val activeNotes = notes.filter { it.isActive }
        
        // Group notes by priority categories
        val upcomingToday = mutableListOf<Note>()
        val highPriorityNotes = mutableListOf<Note>()
        val reminderOtherDays = mutableListOf<Note>()
        val regularNotes = mutableListOf<Note>()
        
        activeNotes.forEach { note ->
            when {
                // Category 1: Notes with upcoming reminders today
                note.shouldRemindToday(currentDayOfWeek, activeShift) && 
                isReminderUpcoming(note.reminderTime, currentTime) -> {
                    upcomingToday.add(note)
                }
                
                // Category 2: High priority notes
                note.priority == NotePriority.HIGH -> {
                    highPriorityNotes.add(note)
                }
                
                // Category 3: Notes with reminders on other days
                note.reminderTime != null && !note.shouldRemindToday(currentDayOfWeek, activeShift) -> {
                    reminderOtherDays.add(note)
                }
                
                // Category 4: Regular notes
                else -> {
                    regularNotes.add(note)
                }
            }
        }
        
        // Sort each category
        val sortedUpcomingToday = upcomingToday.sortedWith(
            compareBy<Note> { it.reminderTime }
                .thenBy { it.priority.ordinal }
        )
        
        val sortedHighPriority = highPriorityNotes.sortedBy { it.createdAt }
        
        val sortedReminderOtherDays = reminderOtherDays.sortedWith(
            compareBy<Note> { it.priority.ordinal }
                .thenBy { it.createdAt }
        )
        
        val sortedRegularNotes = regularNotes.sortedByDescending { it.updatedAt }
        
        // Combine all categories
        return sortedUpcomingToday + sortedHighPriority + sortedReminderOtherDays + sortedRegularNotes
    }
    
    /**
     * Get top N notes for display in home screen
     */
    fun getTopNotesForDisplay(
        notes: List<Note>,
        currentDayOfWeek: String,
        activeShift: Shift?,
        limit: Int = 3,
        currentTime: String = getCurrentTime()
    ): List<Note> {
        val sortedNotes = sortNotesWithPriority(notes, currentDayOfWeek, activeShift, currentTime)
        return sortedNotes.take(limit)
    }
    
    /**
     * Check if a reminder is upcoming (within next 2 hours or already passed but still today)
     */
    private fun isReminderUpcoming(reminderTime: String?, currentTime: String): Boolean {
        if (reminderTime == null) return false
        
        return try {
            val formatter = DateTimeFormatter.ofPattern("HH:mm")
            val reminder = LocalTime.parse(reminderTime, formatter)
            val current = LocalTime.parse(currentTime, formatter)
            
            // If reminder is in the past but still today, consider it upcoming
            // If reminder is within next 2 hours, consider it upcoming
            val hoursUntilReminder = java.time.Duration.between(current, reminder).toHours()
            
            hoursUntilReminder <= 2 || hoursUntilReminder >= 22 // Handle day wrap-around
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Get current time in HH:mm format
     */
    private fun getCurrentTime(): String {
        val now = Clock.System.now()
        val localDateTime = now.toLocalDateTime(TimeZone.currentSystemDefault())
        return "${localDateTime.hour.toString().padStart(2, '0')}:${localDateTime.minute.toString().padStart(2, '0')}"
    }
    
    /**
     * Get notes that should be reminded today
     */
    fun getNotesForToday(
        notes: List<Note>,
        currentDayOfWeek: String,
        activeShift: Shift?
    ): List<Note> {
        return notes.filter { note ->
            note.isActive && note.shouldRemindToday(currentDayOfWeek, activeShift)
        }.sortedBy { it.reminderTime }
    }
    
    /**
     * Get notes with high priority
     */
    fun getHighPriorityNotes(notes: List<Note>): List<Note> {
        return notes.filter { it.isActive && it.priority == NotePriority.HIGH }
            .sortedBy { it.createdAt }
    }
    
    /**
     * Get notes with upcoming reminders (within next hour)
     */
    fun getUpcomingReminderNotes(
        notes: List<Note>,
        currentDayOfWeek: String,
        activeShift: Shift?,
        currentTime: String = getCurrentTime()
    ): List<Note> {
        return notes.filter { note ->
            note.isActive && 
            note.shouldRemindToday(currentDayOfWeek, activeShift) &&
            isReminderSoon(note.reminderTime, currentTime, 60) // Within 1 hour
        }.sortedBy { it.reminderTime }
    }
    
    /**
     * Check if reminder is within specified minutes
     */
    private fun isReminderSoon(reminderTime: String?, currentTime: String, withinMinutes: Int): Boolean {
        if (reminderTime == null) return false
        
        return try {
            val formatter = DateTimeFormatter.ofPattern("HH:mm")
            val reminder = LocalTime.parse(reminderTime, formatter)
            val current = LocalTime.parse(currentTime, formatter)
            
            val minutesUntilReminder = java.time.Duration.between(current, reminder).toMinutes()
            
            minutesUntilReminder in 0..withinMinutes
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Get note statistics for analysis
     */
    fun getNoteStatistics(notes: List<Note>): NoteStatistics {
        val activeNotes = notes.filter { it.isActive }
        
        return NoteStatistics(
            totalNotes = activeNotes.size,
            highPriorityNotes = activeNotes.count { it.priority == NotePriority.HIGH },
            notesWithReminders = activeNotes.count { it.reminderTime != null },
            notesWithShiftAssociation = activeNotes.count { it.associatedShiftIds.isNotEmpty() }
        )
    }
    
    data class NoteStatistics(
        val totalNotes: Int,
        val highPriorityNotes: Int,
        val notesWithReminders: Int,
        val notesWithShiftAssociation: Int
    )
}
