package com.workly.app.business

import android.content.Context
import com.workly.app.alarm.AlarmScheduler
import com.workly.app.data.model.Note
import com.workly.app.data.model.Shift
import com.workly.app.data.repository.NotesRepository
import com.workly.app.data.repository.SettingsRepository
import com.workly.app.data.repository.ShiftRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch

/**
 * High-level alarm management that coordinates with repositories and settings
 */
class AlarmManager(
    private val context: Context,
    private val shiftRepository: ShiftRepository,
    private val notesRepository: NotesRepository,
    private val settingsRepository: SettingsRepository,
    private val scope: CoroutineScope
) {
    
    private val alarmScheduler = AlarmScheduler(context)
    
    /**
     * Schedule all alarms for the active shift
     */
    fun scheduleActiveShiftAlarms() {
        scope.launch {
            try {
                val settings = settingsRepository.getSettings()
                
                // Only schedule if alarms are enabled
                if (!settings.alarmSoundEnabled && !settings.alarmVibrationEnabled) {
                    return@launch
                }
                
                val activeShift = shiftRepository.getActiveShift()
                if (activeShift != null) {
                    alarmScheduler.scheduleShiftAlarms(activeShift)
                }
            } catch (e: Exception) {
                // Log error but don't crash
            }
        }
    }
    
    /**
     * Cancel all alarms for the active shift
     */
    fun cancelActiveShiftAlarms() {
        scope.launch {
            try {
                val activeShift = shiftRepository.getActiveShift()
                if (activeShift != null) {
                    alarmScheduler.cancelShiftAlarms(activeShift)
                }
            } catch (e: Exception) {
                // Log error but don't crash
            }
        }
    }
    
    /**
     * Reschedule alarms when shift changes
     */
    fun onActiveShiftChanged(newShift: Shift?) {
        scope.launch {
            try {
                // Cancel all existing shift alarms
                cancelActiveShiftAlarms()
                
                // Schedule new alarms if there's an active shift
                if (newShift != null) {
                    alarmScheduler.scheduleShiftAlarms(newShift)
                }
                
                // Reschedule note reminders
                rescheduleNoteReminders()
            } catch (e: Exception) {
                // Log error but don't crash
            }
        }
    }
    
    /**
     * Schedule all note reminders
     */
    fun scheduleNoteReminders() {
        scope.launch {
            try {
                val settings = settingsRepository.getSettings()
                
                // Only schedule if alarms are enabled
                if (!settings.alarmSoundEnabled && !settings.alarmVibrationEnabled) {
                    return@launch
                }
                
                val activeShift = shiftRepository.getActiveShift()
                val notesWithReminders = notesRepository.getNotesWithReminders()
                
                notesWithReminders.forEach { note ->
                    alarmScheduler.scheduleNoteReminder(note, activeShift)
                }
            } catch (e: Exception) {
                // Log error but don't crash
            }
        }
    }
    
    /**
     * Cancel all note reminders
     */
    fun cancelAllNoteReminders() {
        scope.launch {
            try {
                val notes = notesRepository.getNotes()
                notes.forEach { note ->
                    alarmScheduler.cancelNoteReminder(note)
                }
            } catch (e: Exception) {
                // Log error but don't crash
            }
        }
    }
    
    /**
     * Reschedule note reminders (called when shift or notes change)
     */
    fun rescheduleNoteReminders() {
        scope.launch {
            try {
                // Cancel all existing note reminders
                cancelAllNoteReminders()
                
                // Schedule new reminders
                scheduleNoteReminders()
            } catch (e: Exception) {
                // Log error but don't crash
            }
        }
    }
    
    /**
     * Handle note changes
     */
    fun onNoteChanged(note: Note) {
        scope.launch {
            try {
                val settings = settingsRepository.getSettings()
                
                // Only schedule if alarms are enabled
                if (!settings.alarmSoundEnabled && !settings.alarmVibrationEnabled) {
                    return@launch
                }
                
                // Cancel existing reminder for this note
                alarmScheduler.cancelNoteReminder(note)
                
                // Schedule new reminder if note is active and has reminder time
                if (note.isActive && note.reminderTime != null) {
                    val activeShift = shiftRepository.getActiveShift()
                    alarmScheduler.scheduleNoteReminder(note, activeShift)
                }
            } catch (e: Exception) {
                // Log error but don't crash
            }
        }
    }
    
    /**
     * Handle note deletion
     */
    fun onNoteDeleted(note: Note) {
        scope.launch {
            try {
                alarmScheduler.cancelNoteReminder(note)
            } catch (e: Exception) {
                // Log error but don't crash
            }
        }
    }
    
    /**
     * Handle settings changes
     */
    fun onSettingsChanged() {
        scope.launch {
            try {
                val settings = settingsRepository.getSettings()
                
                if (settings.alarmSoundEnabled || settings.alarmVibrationEnabled) {
                    // Alarms are enabled, reschedule everything
                    scheduleActiveShiftAlarms()
                    scheduleNoteReminders()
                } else {
                    // Alarms are disabled, cancel everything
                    cancelActiveShiftAlarms()
                    cancelAllNoteReminders()
                }
            } catch (e: Exception) {
                // Log error but don't crash
            }
        }
    }
    
    /**
     * Initialize alarms on app start
     */
    fun initializeAlarms() {
        scope.launch {
            try {
                val settings = settingsRepository.getSettings()
                
                if (settings.alarmSoundEnabled || settings.alarmVibrationEnabled) {
                    scheduleActiveShiftAlarms()
                    scheduleNoteReminders()
                }
            } catch (e: Exception) {
                // Log error but don't crash
            }
        }
    }
    
    /**
     * Check if alarms are enabled in settings
     */
    suspend fun areAlarmsEnabled(): Boolean {
        return try {
            val settings = settingsRepository.getSettings()
            settings.alarmSoundEnabled || settings.alarmVibrationEnabled
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Get alarm settings for display
     */
    suspend fun getAlarmSettings(): AlarmSettings {
        return try {
            val settings = settingsRepository.getSettings()
            AlarmSettings(
                soundEnabled = settings.alarmSoundEnabled,
                vibrationEnabled = settings.alarmVibrationEnabled
            )
        } catch (e: Exception) {
            AlarmSettings(false, false)
        }
    }
}

data class AlarmSettings(
    val soundEnabled: Boolean,
    val vibrationEnabled: Boolean
)
