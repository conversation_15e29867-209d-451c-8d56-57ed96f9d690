package com.workly.app.data.repository

import com.google.gson.reflect.TypeToken
import com.workly.app.data.model.UserSettings
import com.workly.app.data.storage.DataStoreManager
import kotlinx.coroutines.flow.Flow

class SettingsRepository(private val dataStoreManager: DataStoreManager) {
    
    private val defaultSettings = UserSettings()
    private val settingsType = object : TypeToken<UserSettings>() {}
    
    suspend fun saveSettings(settings: UserSettings) {
        val updatedSettings = settings.copy(
            updatedAt = kotlinx.datetime.Clock.System.now().toString()
        )
        dataStoreManager.storeData(DataStoreManager.USER_SETTINGS_KEY, updatedSettings)
    }
    
    suspend fun getSettings(): UserSettings {
        return dataStoreManager.getData(
            DataStoreManager.USER_SETTINGS_KEY,
            settingsType,
            defaultSettings
        )
    }
    
    fun getSettingsFlow(): Flow<UserSettings> {
        return dataStoreManager.getDataFlow(
            DataStoreManager.USER_SETTINGS_KEY,
            settingsType,
            defaultSettings
        )
    }
    
    suspend fun updateLanguage(language: String) {
        val currentSettings = getSettings()
        saveSettings(currentSettings.copy(language = language))
    }
    
    suspend fun updateTheme(theme: String) {
        val currentSettings = getSettings()
        saveSettings(currentSettings.copy(theme = theme))
    }
    
    suspend fun updateMultiButtonMode(mode: String) {
        val currentSettings = getSettings()
        saveSettings(currentSettings.copy(multiButtonMode = mode))
    }
    
    suspend fun updateAlarmSettings(soundEnabled: Boolean, vibrationEnabled: Boolean) {
        val currentSettings = getSettings()
        saveSettings(currentSettings.copy(
            alarmSoundEnabled = soundEnabled,
            alarmVibrationEnabled = vibrationEnabled
        ))
    }
    
    suspend fun updateWeatherSettings(enabled: Boolean, location: com.workly.app.data.model.WeatherLocation? = null) {
        val currentSettings = getSettings()
        saveSettings(currentSettings.copy(
            weatherWarningEnabled = enabled,
            weatherLocation = location ?: currentSettings.weatherLocation
        ))
    }
    
    suspend fun updateChangeShiftReminderMode(mode: String) {
        val currentSettings = getSettings()
        saveSettings(currentSettings.copy(changeShiftReminderMode = mode))
    }
    
    suspend fun updateTimeFormat(format: String) {
        val currentSettings = getSettings()
        saveSettings(currentSettings.copy(timeFormat = format))
    }
    
    suspend fun updateFirstDayOfWeek(day: String) {
        val currentSettings = getSettings()
        saveSettings(currentSettings.copy(firstDayOfWeek = day))
    }

    suspend fun updateNotesDisplayLimit(limit: Int) {
        val currentSettings = getSettings()
        saveSettings(currentSettings.copy(notesDisplayLimit = limit))
    }

    suspend fun updateAdvancedCalculationSettings(
        lateThresholdMinutes: Int,
        otWeekdayRate: Float,
        otSaturdayRate: Float,
        otSundayRate: Float,
        otHolidayRate: Float
    ) {
        val currentSettings = getSettings()
        saveSettings(currentSettings.copy(
            lateThresholdMinutes = lateThresholdMinutes,
            otWeekdayRate = otWeekdayRate,
            otSaturdayRate = otSaturdayRate,
            otSundayRate = otSundayRate,
            otHolidayRate = otHolidayRate
        ))
    }

    suspend fun updateRotationSettings(
        shiftIds: List<String>,
        frequency: String
    ) {
        val currentSettings = getSettings()
        saveSettings(currentSettings.copy(
            rotationShiftIds = shiftIds,
            rotationFrequency = frequency
        ))
    }

    suspend fun updateRotationLastAppliedDate(date: String) {
        val currentSettings = getSettings()
        saveSettings(currentSettings.copy(rotationLastAppliedDate = date))
    }
    
    suspend fun resetToDefaults() {
        val newSettings = defaultSettings.copy(
            createdAt = kotlinx.datetime.Clock.System.now().toString(),
            updatedAt = kotlinx.datetime.Clock.System.now().toString()
        )
        saveSettings(newSettings)
    }
}
