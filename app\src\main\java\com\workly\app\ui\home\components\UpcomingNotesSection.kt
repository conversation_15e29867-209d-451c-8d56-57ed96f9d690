package com.workly.app.ui.home.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.icons.filled.Notes
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.workly.app.business.NotePriorityManager
import com.workly.app.data.model.Note
import com.workly.app.data.model.Shift
import kotlinx.datetime.*

@Composable
fun UpcomingNotesSection(
    notes: List<Note>,
    activeShift: Shift?,
    displayLimit: Int = 3,
    onViewAllClick: () -> Unit,
    onAddNoteClick: () -> Unit,
    onEditNoteClick: (String) -> Unit,
    onDeleteNoteClick: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val notePriorityManager = remember { NotePriorityManager() }
    val currentDayOfWeek = remember {
        val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
        getDayOfWeekString(today.dayOfWeek)
    }
    
    // Get prioritized notes for display
    val prioritizedNotes = remember(notes, activeShift, displayLimit) {
        notePriorityManager.getTopNotesForDisplay(
            notes = notes,
            currentDayOfWeek = currentDayOfWeek,
            activeShift = activeShift,
            limit = displayLimit
        )
    }
    
    Card(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Header with title and actions
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Notes,
                        contentDescription = null,
                        modifier = Modifier.size(20.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "Ghi Chú Sắp Tới",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold
                    )
                }
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // View All button
                    TextButton(
                        onClick = onViewAllClick,
                        contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp)
                    ) {
                        Text(
                            text = "Xem Tất Cả",
                            style = MaterialTheme.typography.labelMedium
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Icon(
                            imageVector = Icons.Default.ArrowForward,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                    
                    // Add Note button
                    IconButton(
                        onClick = onAddNoteClick,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "Thêm ghi chú",
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Notes content
            if (prioritizedNotes.isEmpty()) {
                EmptyNotesState(
                    onAddNoteClick = onAddNoteClick,
                    modifier = Modifier.fillMaxWidth()
                )
            } else {
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    prioritizedNotes.forEach { note ->
                        NoteItemCompact(
                            note = note,
                            activeShift = activeShift,
                            currentDayOfWeek = currentDayOfWeek,
                            onEditClick = onEditNoteClick,
                            onDeleteClick = onDeleteNoteClick
                        )
                    }
                    
                    // Show indicator if there are more notes
                    if (notes.size > displayLimit) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 8.dp),
                            horizontalArrangement = Arrangement.Center
                        ) {
                            Text(
                                text = "và ${notes.size - displayLimit} ghi chú khác...",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun EmptyNotesState(
    onAddNoteClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(vertical = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "📝",
            style = MaterialTheme.typography.headlineMedium
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = "Chưa có ghi chú nào",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = "Thêm ghi chú để nhắc nhở công việc",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer(modifier = Modifier.height(12.dp))
        
        FilledTonalButton(
            onClick = onAddNoteClick,
            modifier = Modifier.fillMaxWidth()
        ) {
            Icon(
                imageVector = Icons.Default.Add,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("Thêm ghi chú đầu tiên")
        }
    }
}

/**
 * Convert DayOfWeek to string format used in the app
 */
private fun getDayOfWeekString(dayOfWeek: DayOfWeek): String {
    return when (dayOfWeek) {
        DayOfWeek.MONDAY -> "Mon"
        DayOfWeek.TUESDAY -> "Tue"
        DayOfWeek.WEDNESDAY -> "Wed"
        DayOfWeek.THURSDAY -> "Thu"
        DayOfWeek.FRIDAY -> "Fri"
        DayOfWeek.SATURDAY -> "Sat"
        DayOfWeek.SUNDAY -> "Sun"
    }
}
