package com.workly.app.ui.notes

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.workly.app.R
import com.workly.app.ui.viewmodel.AddEditNoteViewModel
import com.workly.app.ui.viewmodel.ViewModelFactory

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddEditNoteScreen(
    navController: NavController,
    noteId: String? = null
) {
    val application = androidx.compose.ui.platform.LocalContext.current.applicationContext as com.workly.app.WorklyApplication
    val viewModel: AddEditNoteViewModel = viewModel(
        factory = ViewModelFactory(application)
    )
    
    val uiState by viewModel.uiState.collectAsState()
    val availableShifts by viewModel.availableShifts.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    
    val isEditing = noteId != null
    
    LaunchedEffect(noteId) {
        if (noteId != null) {
            viewModel.loadNote(noteId)
        }
    }
    
    // Handle save success
    LaunchedEffect(viewModel.saveSuccess.collectAsState().value) {
        if (viewModel.saveSuccess.value) {
            navController.popBackStack()
        }
    }
    
    // Show error snackbar
    error?.let { errorMessage ->
        LaunchedEffect(errorMessage) {
            // TODO: Show snackbar
            viewModel.clearError()
        }
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top bar
        TopAppBar(
            title = { 
                Text(
                    if (isEditing) "Sửa ghi chú" 
                    else "Thêm ghi chú"
                ) 
            },
            navigationIcon = {
                IconButton(onClick = { navController.popBackStack() }) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                }
            },
            actions = {
                TextButton(
                    onClick = { viewModel.saveNote() },
                    enabled = !isLoading && uiState.isValid
                ) {
                    Text(stringResource(R.string.save))
                }
            }
        )
        
        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Basic Information
                NoteBasicInfoSection(
                    uiState = uiState,
                    onTitleChange = viewModel::updateTitle,
                    onContentChange = viewModel::updateContent
                )
                
                // Reminder Settings
                ReminderSettingsSection(
                    reminderTime = uiState.reminderTime,
                    onReminderTimeChange = viewModel::updateReminderTime
                )
                
                // Shift Association
                ShiftAssociationSection(
                    availableShifts = availableShifts,
                    selectedShiftIds = uiState.associatedShiftIds,
                    onShiftSelectionChange = viewModel::updateAssociatedShifts
                )
                
                // Explicit Days (when not using shift association)
                if (uiState.associatedShiftIds.isEmpty()) {
                    ExplicitDaysSection(
                        selectedDays = uiState.explicitReminderDays,
                        onDaysChange = viewModel::updateExplicitReminderDays
                    )
                }
                
                // Active Status
                ActiveStatusSection(
                    isActive = uiState.isActive,
                    onActiveChange = viewModel::updateIsActive
                )
                
                // Validation Errors
                if (uiState.validationErrors.isNotEmpty()) {
                    ValidationErrorsSection(uiState.validationErrors)
                }
                
                Spacer(modifier = Modifier.height(32.dp))
            }
        }
    }
}

@Composable
private fun NoteBasicInfoSection(
    uiState: AddEditNoteUiState,
    onTitleChange: (String) -> Unit,
    onContentChange: (String) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Thông tin cơ bản",
                style = MaterialTheme.typography.titleMedium
            )
            
            OutlinedTextField(
                value = uiState.title,
                onValueChange = onTitleChange,
                label = { Text("Tiêu đề") },
                modifier = Modifier.fillMaxWidth(),
                isError = uiState.validationErrors.any { it.contains("tiêu đề") },
                supportingText = {
                    Text("${uiState.title.length}/100")
                }
            )
            
            OutlinedTextField(
                value = uiState.content,
                onValueChange = onContentChange,
                label = { Text("Nội dung") },
                modifier = Modifier.fillMaxWidth(),
                minLines = 3,
                maxLines = 6,
                supportingText = {
                    Text("${uiState.content.length}/500")
                }
            )
        }
    }
}

@Composable
private fun ReminderSettingsSection(
    reminderTime: String?,
    onReminderTimeChange: (String?) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Cài đặt nhắc nhở",
                style = MaterialTheme.typography.titleMedium
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = reminderTime != null,
                    onCheckedChange = { enabled ->
                        if (enabled) {
                            onReminderTimeChange("09:00")
                        } else {
                            onReminderTimeChange(null)
                        }
                    }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("Bật nhắc nhở")
            }
            
            if (reminderTime != null) {
                OutlinedTextField(
                    value = reminderTime,
                    onValueChange = onReminderTimeChange,
                    label = { Text("Thời gian nhắc nhở") },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = { Text("09:00") }
                )
            }
        }
    }
}

@Composable
private fun ShiftAssociationSection(
    availableShifts: List<com.workly.app.data.model.Shift>,
    selectedShiftIds: List<String>,
    onShiftSelectionChange: (List<String>) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "Liên kết với ca làm việc",
                style = MaterialTheme.typography.titleMedium
            )

            Text(
                text = "Ghi chú sẽ được nhắc nhở khi ca làm việc được chọn hoạt động",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            if (availableShifts.isEmpty()) {
                Text(
                    text = "Chưa có ca làm việc nào. Hãy tạo ca làm việc trước.",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            } else {
                availableShifts.forEach { shift ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Checkbox(
                            checked = selectedShiftIds.contains(shift.id),
                            onCheckedChange = { checked ->
                                val newSelection = if (checked) {
                                    selectedShiftIds + shift.id
                                } else {
                                    selectedShiftIds - shift.id
                                }
                                onShiftSelectionChange(newSelection)
                            }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Column {
                            Text(shift.name)
                            Text(
                                text = "${shift.startTime} - ${shift.endTime}",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun ExplicitDaysSection(
    selectedDays: List<String>,
    onDaysChange: (List<String>) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "Ngày nhắc nhở cụ thể",
                style = MaterialTheme.typography.titleMedium
            )

            Text(
                text = "Chọn các ngày trong tuần để nhắc nhở (khi không liên kết với ca làm việc)",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            val dayLabels = mapOf(
                "Mon" to "Thứ 2",
                "Tue" to "Thứ 3",
                "Wed" to "Thứ 4",
                "Thu" to "Thứ 5",
                "Fri" to "Thứ 6",
                "Sat" to "Thứ 7",
                "Sun" to "Chủ nhật"
            )

            dayLabels.forEach { (dayCode, dayLabel) ->
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = selectedDays.contains(dayCode),
                        onCheckedChange = { checked ->
                            val newDays = if (checked) {
                                selectedDays + dayCode
                            } else {
                                selectedDays - dayCode
                            }
                            onDaysChange(newDays)
                        }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(dayLabel)
                }
            }
        }
    }
}

@Composable
private fun ActiveStatusSection(
    isActive: Boolean,
    onActiveChange: (Boolean) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = isActive,
                onCheckedChange = onActiveChange
            )
            Spacer(modifier = Modifier.width(8.dp))
            Column {
                Text(
                    text = "Kích hoạt ghi chú",
                    style = MaterialTheme.typography.titleSmall
                )
                Text(
                    text = "Ghi chú sẽ hiển thị và nhắc nhở khi được kích hoạt",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun ValidationErrorsSection(
    errors: List<String>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Lỗi validation",
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
            Spacer(modifier = Modifier.height(8.dp))

            errors.forEach { error ->
                Text(
                    text = "• $error",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
            }
        }
    }
}

// Data class for UI state
data class AddEditNoteUiState(
    val title: String = "",
    val content: String = "",
    val reminderTime: String? = null,
    val associatedShiftIds: List<String> = emptyList(),
    val explicitReminderDays: List<String> = emptyList(),
    val isActive: Boolean = true,
    val validationErrors: List<String> = emptyList(),
    val isValid: Boolean = false
)
