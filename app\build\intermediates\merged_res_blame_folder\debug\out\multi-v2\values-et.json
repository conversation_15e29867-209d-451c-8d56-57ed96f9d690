{"logs": [{"outputFile": "com.workly.app-mergeDebugResources-77:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\870103ce12d4503c553c2f969e6a0d11\\transformed\\navigation-ui-2.7.5\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,120", "endOffsets": "159,280"}, "to": {"startLines": "138,139", "startColumns": "4,4", "startOffsets": "12784,12893", "endColumns": "108,120", "endOffsets": "12888,13009"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d56bc5ab2f06c8c057783f978bdff8f7\\transformed\\material3-1.1.2\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,289,399,514,592,689,804,933,1049,1193,1276,1373,1463,1558,1670,1793,1893,2024,2153,2280,2451,2573,2688,2806,2925,3016,3109,3225,3355,3456,3555,3656,3782,3913,4017,4115,4188,4266,4349,4430,4532,4608,4690,4787,4887,4977,5077,5162,5267,5364,5466,5579,5655,5755", "endColumns": "115,117,109,114,77,96,114,128,115,143,82,96,89,94,111,122,99,130,128,126,170,121,114,117,118,90,92,115,129,100,98,100,125,130,103,97,72,77,82,80,101,75,81,96,99,89,99,84,104,96,101,112,75,99,94", "endOffsets": "166,284,394,509,587,684,799,928,1044,1188,1271,1368,1458,1553,1665,1788,1888,2019,2148,2275,2446,2568,2683,2801,2920,3011,3104,3220,3350,3451,3550,3651,3777,3908,4012,4110,4183,4261,4344,4425,4527,4603,4685,4782,4882,4972,5072,5157,5262,5359,5461,5574,5650,5750,5845"}, "to": {"startLines": "33,34,35,36,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,84,87,147,150,152,156,157,158,159,160,161,162,163,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2993,3109,3227,3337,4737,4815,4912,5027,5156,5272,5416,5499,5596,5686,5781,5893,6016,6116,6247,6376,6503,6674,6796,6911,7029,7148,7239,7332,7448,7578,7679,7778,7879,8005,8136,8240,8529,8748,13571,13809,13991,14363,14439,14521,14618,14718,14808,14908,14993,15098,15195,15297,15410,15486,15586", "endColumns": "115,117,109,114,77,96,114,128,115,143,82,96,89,94,111,122,99,130,128,126,170,121,114,117,118,90,92,115,129,100,98,100,125,130,103,97,72,77,82,80,101,75,81,96,99,89,99,84,104,96,101,112,75,99,94", "endOffsets": "3104,3222,3332,3447,4810,4907,5022,5151,5267,5411,5494,5591,5681,5776,5888,6011,6111,6242,6371,6498,6669,6791,6906,7024,7143,7234,7327,7443,7573,7674,7773,7874,8000,8131,8235,8333,8597,8821,13649,13885,14088,14434,14516,14613,14713,14803,14903,14988,15093,15190,15292,15405,15481,15581,15676"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b5d7701da16b09423affca14e07f926c\\transformed\\material-1.4.0-beta01\\res\\values-et\\values-et.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,221,306,405,522,604,668,753,821,885,972,1036,1095,1167,1231,1285,1404,1464,1525,1579,1652,1785,1869,1962,2070,2150,2229,2317,2384,2450,2523,2602,2688,2761,2836,2910,2982,3070,3147,3238,3330,3402,3476,3567,3621,3690,3773,3859,3921,3985,4048,4151,4255,4352,4457", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,84,98,116,81,63,84,67,63,86,63,58,71,63,53,118,59,60,53,72,132,83,92,107,79,78,87,66,65,72,78,85,72,74,73,71,87,76,90,91,71,73,90,53,68,82,85,61,63,62,102,103,96,104,80", "endOffsets": "216,301,400,517,599,663,748,816,880,967,1031,1090,1162,1226,1280,1399,1459,1520,1574,1647,1780,1864,1957,2065,2145,2224,2312,2379,2445,2518,2597,2683,2756,2831,2905,2977,3065,3142,3233,3325,3397,3471,3562,3616,3685,3768,3854,3916,3980,4043,4146,4250,4347,4452,4533"}, "to": {"startLines": "2,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3452,4266,4365,4482,8684,8826,8911,9148,9212,9299,9363,9422,9494,9558,9612,9731,9791,9852,9906,9979,10112,10196,10289,10397,10477,10556,10644,10711,10777,10850,10929,11015,11088,11163,11237,11309,11397,11474,11565,11657,11729,11803,11894,11948,12017,12100,12186,12248,12312,12375,12478,12582,12679,13323", "endLines": "5,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "endColumns": "12,84,98,116,81,63,84,67,63,86,63,58,71,63,53,118,59,60,53,72,132,83,92,107,79,78,87,66,65,72,78,85,72,74,73,71,87,76,90,91,71,73,90,53,68,82,85,61,63,62,102,103,96,104,80", "endOffsets": "266,3532,4360,4477,4559,8743,8906,8974,9207,9294,9358,9417,9489,9553,9607,9726,9786,9847,9901,9974,10107,10191,10284,10392,10472,10551,10639,10706,10772,10845,10924,11010,11083,11158,11232,11304,11392,11469,11560,11652,11724,11798,11889,11943,12012,12095,12181,12243,12307,12370,12473,12577,12674,12779,13399"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\85666ebb484fc6a37ee9760c5f520352\\transformed\\appcompat-1.2.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "271,377,476,587,673,775,892,973,1050,1142,1236,1332,1434,1543,1637,1738,1832,1924,2017,2100,2211,2315,2414,2524,2626,2725,2891,13654", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "372,471,582,668,770,887,968,1045,1137,1231,1327,1429,1538,1632,1733,1827,1919,2012,2095,2206,2310,2409,2519,2621,2720,2886,2988,13732"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9209685f1972143d9ab1eeffcca4ab41\\transformed\\core-1.12.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "38,39,40,41,42,43,44,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3537,3632,3734,3832,3935,4041,4146,13890", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "3627,3729,3827,3930,4036,4141,4261,13986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\631285464bc3699b5e0c057776a78762\\transformed\\ui-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,278,374,469,551,629,720,811,895,963,1029,1111,1196,1268,1345,1416", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,76,70,121", "endOffsets": "193,273,369,464,546,624,715,806,890,958,1024,1106,1191,1263,1340,1411,1533"}, "to": {"startLines": "48,49,82,83,85,90,91,140,141,142,143,145,146,149,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4564,4657,8338,8434,8602,8979,9057,13014,13105,13189,13257,13404,13486,13737,14093,14170,14241", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,76,70,121", "endOffsets": "4652,4732,8429,8524,8679,9052,9143,13100,13184,13252,13318,13481,13566,13804,14165,14236,14358"}}]}]}