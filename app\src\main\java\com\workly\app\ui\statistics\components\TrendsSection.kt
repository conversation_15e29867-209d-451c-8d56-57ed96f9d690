package com.workly.app.ui.statistics.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.workly.app.ui.viewmodel.StatisticsTrends
import com.workly.app.ui.viewmodel.TrendDirection

@Composable
fun TrendsSection(trends: StatisticsTrends) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Xu hướng",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            // Hours per day trend
            TrendItem(
                title = "Giờ làm việc/ngày",
                trend = trends.hoursPerDayTrend,
                description = getTrendDescription(trends.hoursPerDayTrend, "giờ làm việc")
            )
            
            // Overtime trend
            TrendItem(
                title = "Tăng ca",
                trend = trends.overtimeTrend,
                description = getTrendDescription(trends.overtimeTrend, "tăng ca")
            )
            
            // Punctuality trend
            TrendItem(
                title = "Tính đúng giờ",
                trend = trends.punctualityTrend,
                description = getTrendDescription(trends.punctualityTrend, "tính đúng giờ")
            )
        }
    }
}

@Composable
private fun TrendItem(
    title: String,
    trend: TrendDirection,
    description: String
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = getTrendIcon(trend),
            contentDescription = null,
            tint = getTrendColor(trend),
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        // Trend indicator chip
        AssistChip(
            onClick = { },
            label = { 
                Text(
                    text = getTrendLabel(trend),
                    style = MaterialTheme.typography.labelSmall
                )
            },
            colors = AssistChipDefaults.assistChipColors(
                containerColor = getTrendColor(trend).copy(alpha = 0.1f),
                labelColor = getTrendColor(trend)
            )
        )
    }
}

private fun getTrendIcon(trend: TrendDirection): androidx.compose.ui.graphics.vector.ImageVector {
    return when (trend) {
        TrendDirection.INCREASING -> Icons.Default.TrendingUp
        TrendDirection.DECREASING -> Icons.Default.TrendingDown
        TrendDirection.STABLE -> Icons.Default.TrendingFlat
    }
}

private fun getTrendColor(trend: TrendDirection): Color {
    return when (trend) {
        TrendDirection.INCREASING -> Color(0xFF4CAF50) // Green
        TrendDirection.DECREASING -> Color(0xFFF44336) // Red
        TrendDirection.STABLE -> Color(0xFF9E9E9E) // Grey
    }
}

private fun getTrendLabel(trend: TrendDirection): String {
    return when (trend) {
        TrendDirection.INCREASING -> "Tăng"
        TrendDirection.DECREASING -> "Giảm"
        TrendDirection.STABLE -> "Ổn định"
    }
}

private fun getTrendDescription(trend: TrendDirection, metric: String): String {
    return when (trend) {
        TrendDirection.INCREASING -> "Xu hướng $metric đang tăng"
        TrendDirection.DECREASING -> "Xu hướng $metric đang giảm"
        TrendDirection.STABLE -> "Xu hướng $metric ổn định"
    }
}
