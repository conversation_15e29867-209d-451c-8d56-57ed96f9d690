{"logs": [{"outputFile": "com.workly.app-mergeDebugResources-77:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9209685f1972143d9ab1eeffcca4ab41\\transformed\\core-1.12.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "38,39,40,41,42,43,44,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3483,3582,3684,3783,3883,3984,4090,13707", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "3577,3679,3778,3878,3979,4085,4202,13803"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\631285464bc3699b5e0c057776a78762\\transformed\\ui-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,192,271,365,463,549,631,734,819,902,969,1035,1116,1198,1272,1347,1414", "endColumns": "86,78,93,97,85,81,102,84,82,66,65,80,81,73,74,66,116", "endOffsets": "187,266,360,458,544,626,729,814,897,964,1030,1111,1193,1267,1342,1409,1526"}, "to": {"startLines": "48,49,82,83,85,90,91,140,141,142,143,145,146,149,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4517,4604,8219,8313,8487,8874,8956,12847,12932,13015,13082,13226,13307,13553,13903,13978,14045", "endColumns": "86,78,93,97,85,81,102,84,82,66,65,80,81,73,74,66,116", "endOffsets": "4599,4678,8308,8406,8568,8951,9054,12927,13010,13077,13143,13302,13384,13622,13973,14040,14157"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\870103ce12d4503c553c2f969e6a0d11\\transformed\\navigation-ui-2.7.5\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,114", "endOffsets": "159,274"}, "to": {"startLines": "138,139", "startColumns": "4,4", "startOffsets": "12623,12732", "endColumns": "108,114", "endOffsets": "12727,12842"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d56bc5ab2f06c8c057783f978bdff8f7\\transformed\\material3-1.1.2\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,277,387,502,578,670,781,916,1028,1159,1240,1339,1427,1520,1630,1749,1853,1982,2109,2227,2388,2506,2615,2728,2842,2931,3025,3142,3270,3373,3472,3573,3699,3831,3933,4038,4114,4193,4275,4355,4450,4528,4608,4705,4802,4895,4991,5074,5174,5270,5368,5484,5562,5662", "endColumns": "111,109,109,114,75,91,110,134,111,130,80,98,87,92,109,118,103,128,126,117,160,117,108,112,113,88,93,116,127,102,98,100,125,131,101,104,75,78,81,79,94,77,79,96,96,92,95,82,99,95,97,115,77,99,93", "endOffsets": "162,272,382,497,573,665,776,911,1023,1154,1235,1334,1422,1515,1625,1744,1848,1977,2104,2222,2383,2501,2610,2723,2837,2926,3020,3137,3265,3368,3467,3568,3694,3826,3928,4033,4109,4188,4270,4350,4445,4523,4603,4700,4797,4890,4986,5069,5169,5265,5363,5479,5557,5657,5751"}, "to": {"startLines": "33,34,35,36,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,84,87,147,150,152,156,157,158,159,160,161,162,163,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2954,3066,3176,3286,4683,4759,4851,4962,5097,5209,5340,5421,5520,5608,5701,5811,5930,6034,6163,6290,6408,6569,6687,6796,6909,7023,7112,7206,7323,7451,7554,7653,7754,7880,8012,8114,8411,8636,13389,13627,13808,14162,14240,14320,14417,14514,14607,14703,14786,14886,14982,15080,15196,15274,15374", "endColumns": "111,109,109,114,75,91,110,134,111,130,80,98,87,92,109,118,103,128,126,117,160,117,108,112,113,88,93,116,127,102,98,100,125,131,101,104,75,78,81,79,94,77,79,96,96,92,95,82,99,95,97,115,77,99,93", "endOffsets": "3061,3171,3281,3396,4754,4846,4957,5092,5204,5335,5416,5515,5603,5696,5806,5925,6029,6158,6285,6403,6564,6682,6791,6904,7018,7107,7201,7318,7446,7549,7648,7749,7875,8007,8109,8214,8482,8710,13466,13702,13898,14235,14315,14412,14509,14602,14698,14781,14881,14977,15075,15191,15269,15369,15463"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b5d7701da16b09423affca14e07f926c\\transformed\\material-1.4.0-beta01\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,296,393,522,606,669,759,828,888,979,1043,1102,1169,1231,1286,1409,1467,1528,1583,1655,1792,1873,1955,2055,2129,2203,2289,2356,2422,2493,2570,2651,2724,2798,2868,2942,3028,3102,3191,3283,3357,3430,3519,3570,3637,3720,3804,3866,3930,3993,4087,4194,4287,4392", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,96,128,83,62,89,68,59,90,63,58,66,61,54,122,57,60,54,71,136,80,81,99,73,73,85,66,65,70,76,80,72,73,69,73,85,73,88,91,73,72,88,50,66,82,83,61,63,62,93,106,92,104,77", "endOffsets": "209,291,388,517,601,664,754,823,883,974,1038,1097,1164,1226,1281,1404,1462,1523,1578,1650,1787,1868,1950,2050,2124,2198,2284,2351,2417,2488,2565,2646,2719,2793,2863,2937,3023,3097,3186,3278,3352,3425,3514,3565,3632,3715,3799,3861,3925,3988,4082,4189,4282,4387,4465"}, "to": {"startLines": "2,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3401,4207,4304,4433,8573,8715,8805,9059,9119,9210,9274,9333,9400,9462,9517,9640,9698,9759,9814,9886,10023,10104,10186,10286,10360,10434,10520,10587,10653,10724,10801,10882,10955,11029,11099,11173,11259,11333,11422,11514,11588,11661,11750,11801,11868,11951,12035,12097,12161,12224,12318,12425,12518,13148", "endLines": "5,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "endColumns": "12,81,96,128,83,62,89,68,59,90,63,58,66,61,54,122,57,60,54,71,136,80,81,99,73,73,85,66,65,70,76,80,72,73,69,73,85,73,88,91,73,72,88,50,66,82,83,61,63,62,93,106,92,104,77", "endOffsets": "259,3478,4299,4428,4512,8631,8800,8869,9114,9205,9269,9328,9395,9457,9512,9635,9693,9754,9809,9881,10018,10099,10181,10281,10355,10429,10515,10582,10648,10719,10796,10877,10950,11024,11094,11168,11254,11328,11417,11509,11583,11656,11745,11796,11863,11946,12030,12092,12156,12219,12313,12420,12513,12618,13221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\85666ebb484fc6a37ee9760c5f520352\\transformed\\appcompat-1.2.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "264,374,475,586,670,771,886,966,1043,1136,1231,1323,1417,1519,1614,1711,1805,1898,1988,2070,2178,2282,2380,2486,2591,2696,2853,13471", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "369,470,581,665,766,881,961,1038,1131,1226,1318,1412,1514,1609,1706,1800,1893,1983,2065,2173,2277,2375,2481,2586,2691,2848,2949,13548"}}]}]}