package com.workly.app.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// Workly specific colors
val WorklyPrimary = Color(0xFF1976D2)
val WorklyPrimaryVariant = Color(0xFF1565C0)
val WorklySecondary = Color(0xFF03DAC6)
val WorklySecondaryVariant = Color(0xFF018786)

// Status colors
val StatusComplete = Color(0xFF4CAF50)
val StatusIncomplete = Color(0xFFF44336)
val StatusWarning = Color(0xFFFF9800)
val StatusInfo = Color(0xFF2196F3)
val StatusNeutral = Color(0xFF9E9E9E)

// Work status colors
val StatusDuCong = Color(0xFF4CAF50)      // Green - Complete work
val StatusThieuLog = Color(0xFFFF9800)    // Orange - Missing log
val StatusDiMuon = Color(0xFFFF5722)      // Red-Orange - Late
val StatusVeSom = Color(0xFFFF5722)       // Red-Orange - Early leave
val StatusNghiPhep = Color(0xFF2196F3)    // Blue - Vacation
val StatusNghiBenh = Color(0xFF9C27B0)    // Purple - Sick leave
val StatusNghiLe = Color(0xFF607D8B)      // Blue-Grey - Holiday
val StatusVangMat = Color(0xFFF44336)     // Red - Absent

// Weather warning colors
val WeatherWarning = Color(0xFFFF5722)
val WeatherCaution = Color(0xFFFF9800)
val WeatherInfo = Color(0xFF2196F3)
