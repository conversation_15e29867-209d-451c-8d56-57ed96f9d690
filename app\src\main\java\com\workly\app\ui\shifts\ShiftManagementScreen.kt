package com.workly.app.ui.shifts

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.ContentCopy
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.workly.app.R
import com.workly.app.data.model.Shift
import com.workly.app.ui.viewmodel.MainViewModel
import com.workly.app.ui.viewmodel.ShiftManagementViewModel
import com.workly.app.ui.viewmodel.ViewModelFactory

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ShiftManagementScreen(
    navController: NavController,
    mainViewModel: MainViewModel,
    isRotationMode: Boolean = false
) {
    val application = androidx.compose.ui.platform.LocalContext.current.applicationContext as com.workly.app.WorklyApplication
    val viewModel: ShiftManagementViewModel = viewModel(
        factory = ViewModelFactory(application)
    )

    val shifts by viewModel.shifts.collectAsState()
    val activeShiftId by viewModel.activeShiftId.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()

    // Rotation mode state
    var selectedShiftsForRotation by remember { mutableStateOf(setOf<String>()) }
    val successMessage by viewModel.successMessage.collectAsState()

    // Show error snackbar
    error?.let { errorMessage ->
        LaunchedEffect(errorMessage) {
            // TODO: Show snackbar
            viewModel.clearError()
        }
    }

    // Show success snackbar
    successMessage?.let { message ->
        LaunchedEffect(message) {
            // TODO: Show snackbar
            viewModel.clearSuccessMessage()
        }
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top bar
        TopAppBar(
            title = {
                Text(
                    if (isRotationMode) {
                        stringResource(R.string.rotation_mode_selection)
                    } else {
                        stringResource(R.string.shift_management)
                    }
                )
            },
            navigationIcon = {
                IconButton(onClick = { navController.popBackStack() }) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                }
            },
            actions = {
                if (isRotationMode) {
                    // Confirm button for rotation mode
                    TextButton(
                        onClick = {
                            if (selectedShiftsForRotation.size >= 2) {
                                // TODO: Save rotation selection
                                navController.popBackStack()
                            }
                        },
                        enabled = selectedShiftsForRotation.size >= 2
                    ) {
                        Text(stringResource(R.string.confirm_rotation_selection))
                    }
                } else {
                    IconButton(onClick = { navController.navigate("shift_templates") }) {
                        Icon(Icons.Default.ContentCopy, contentDescription = "Mẫu ca làm việc")
                    }
                    IconButton(onClick = { navController.navigate("add_shift") }) {
                        Icon(Icons.Default.Add, contentDescription = stringResource(R.string.add_shift))
                    }
                }
            }
        )

        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                if (shifts.isEmpty()) {
                    item {
                        EmptyShiftsState(
                            onAddShiftClick = { navController.navigate("add_shift") },
                            onTemplatesClick = { navController.navigate("shift_templates") }
                        )
                    }
                } else {
                    if (isRotationMode) {
                        // Rotation mode header
                        item {
                            Card(
                                modifier = Modifier.fillMaxWidth(),
                                colors = CardDefaults.cardColors(
                                    containerColor = MaterialTheme.colorScheme.primaryContainer
                                )
                            ) {
                                Column(
                                    modifier = Modifier.padding(16.dp)
                                ) {
                                    Text(
                                        text = stringResource(R.string.select_shifts_for_rotation),
                                        style = MaterialTheme.typography.titleMedium,
                                        fontWeight = FontWeight.SemiBold
                                    )
                                    Spacer(modifier = Modifier.height(8.dp))
                                    Text(
                                        text = "Chọn 2-3 ca để thiết lập xoay vòng tự động",
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = MaterialTheme.colorScheme.onPrimaryContainer
                                    )
                                    if (selectedShiftsForRotation.isNotEmpty()) {
                                        Spacer(modifier = Modifier.height(8.dp))
                                        Text(
                                            text = stringResource(R.string.rotation_shifts_selected, selectedShiftsForRotation.size),
                                            style = MaterialTheme.typography.bodySmall,
                                            color = MaterialTheme.colorScheme.primary,
                                            fontWeight = FontWeight.Medium
                                        )
                                    }
                                }
                            }
                        }
                    }

                    items(shifts) { shift ->
                        if (isRotationMode) {
                            ShiftItemSelectable(
                                shift = shift,
                                isSelected = selectedShiftsForRotation.contains(shift.id),
                                onSelectionChange = { isSelected ->
                                    selectedShiftsForRotation = if (isSelected) {
                                        if (selectedShiftsForRotation.size < 3) {
                                            selectedShiftsForRotation + shift.id
                                        } else {
                                            selectedShiftsForRotation // Don't add if already 3 selected
                                        }
                                    } else {
                                        selectedShiftsForRotation - shift.id
                                    }
                                }
                            )
                        } else {
                            ShiftItem(
                                shift = shift,
                                isActive = shift.id == activeShiftId,
                                onSelectClick = { viewModel.setActiveShift(shift.id) },
                                onEditClick = { navController.navigate("edit_shift/${shift.id}") },
                                onDeleteClick = { viewModel.deleteShift(shift) }
                            )
                        }
                    }

                    item {
                        Spacer(modifier = Modifier.height(16.dp))

                        if (activeShiftId != null) {
                            OutlinedButton(
                                onClick = { viewModel.clearActiveShift() },
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Text("Bỏ chọn ca hiện tại")
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun EmptyShiftsState(
    onAddShiftClick: () -> Unit,
    onTemplatesClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "⏰",
                style = MaterialTheme.typography.headlineLarge
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "Chưa có ca làm việc nào",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "Thêm ca làm việc đầu tiên để bắt đầu sử dụng ứng dụng",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.height(24.dp))
            Button(
                onClick = onAddShiftClick,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(Icons.Default.Add, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text(stringResource(R.string.add_shift))
            }

            Spacer(modifier = Modifier.height(12.dp))
            OutlinedButton(
                onClick = onTemplatesClick,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(Icons.Default.ContentCopy, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("Chọn từ mẫu có sẵn")
            }
        }
    }
}

@Composable
private fun ShiftItemSelectable(
    shift: Shift,
    isSelected: Boolean,
    onSelectionChange: (Boolean) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onSelectionChange(!isSelected) },
        colors = if (isSelected) {
            CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        } else {
            CardDefaults.cardColors()
        }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = isSelected,
                onCheckedChange = onSelectionChange
            )

            Spacer(modifier = Modifier.width(12.dp))

            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = shift.name,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "${shift.startTime} - ${shift.officeEndTime}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = "Áp dụng: ${shift.daysApplied.joinToString(", ")}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun ShiftItem(
    shift: Shift,
    isActive: Boolean,
    onSelectClick: () -> Unit,
    onEditClick: () -> Unit,
    onDeleteClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = if (isActive) {
            CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        } else {
            CardDefaults.cardColors()
        }
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = shift.name,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.SemiBold
                        )
                        if (isActive) {
                            Spacer(modifier = Modifier.width(8.dp))
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = "Active",
                                tint = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "${shift.startTime} - ${shift.endTime}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    Text(
                        text = "Xuất phát: ${shift.departureTime}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "Áp dụng: ${shift.daysApplied.joinToString(", ")}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                Row {
                    IconButton(onClick = onEditClick) {
                        Icon(
                            imageVector = Icons.Default.Edit,
                            contentDescription = "Edit"
                        )
                    }
                    IconButton(onClick = onDeleteClick) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "Delete",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }

            if (!isActive) {
                Spacer(modifier = Modifier.height(12.dp))
                Button(
                    onClick = onSelectClick,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("Chọn ca này")
                }
            }
        }
    }
}
