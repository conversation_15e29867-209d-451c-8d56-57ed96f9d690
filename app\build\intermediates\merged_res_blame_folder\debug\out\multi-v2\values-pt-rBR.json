{"logs": [{"outputFile": "com.workly.app-mergeDebugResources-77:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b5d7701da16b09423affca14e07f926c\\transformed\\material-1.4.0-beta01\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,312,413,533,614,678,770,849,909,999,1070,1133,1208,1272,1326,1453,1511,1573,1627,1706,1847,1934,2016,2125,2208,2292,2379,2446,2512,2586,2666,2753,2826,2903,2972,3046,3134,3211,3304,3400,3474,3554,3651,3703,3769,3856,3944,4006,4070,4133,4245,4354,4461,4571", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,83,100,119,80,63,91,78,59,89,70,62,74,63,53,126,57,61,53,78,140,86,81,108,82,83,86,66,65,73,79,86,72,76,68,73,87,76,92,95,73,79,96,51,65,86,87,61,63,62,111,108,106,109,76", "endOffsets": "223,307,408,528,609,673,765,844,904,994,1065,1128,1203,1267,1321,1448,1506,1568,1622,1701,1842,1929,2011,2120,2203,2287,2374,2441,2507,2581,2661,2748,2821,2898,2967,3041,3129,3206,3299,3395,3469,3549,3646,3698,3764,3851,3939,4001,4065,4128,4240,4349,4456,4566,4643"}, "to": {"startLines": "2,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3472,4291,4392,4512,8774,8912,9004,9263,9323,9413,9484,9547,9622,9686,9740,9867,9925,9987,10041,10120,10261,10348,10430,10539,10622,10706,10793,10860,10926,11000,11080,11167,11240,11317,11386,11460,11548,11625,11718,11814,11888,11968,12065,12117,12183,12270,12358,12420,12484,12547,12659,12768,12875,13536", "endLines": "5,37,45,46,47,86,88,89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144", "endColumns": "12,83,100,119,80,63,91,78,59,89,70,62,74,63,53,126,57,61,53,78,140,86,81,108,82,83,86,66,65,73,79,86,72,76,68,73,87,76,92,95,73,79,96,51,65,86,87,61,63,62,111,108,106,109,76", "endOffsets": "273,3551,4387,4507,4588,8833,8999,9078,9318,9408,9479,9542,9617,9681,9735,9862,9920,9982,10036,10115,10256,10343,10425,10534,10617,10701,10788,10855,10921,10995,11075,11162,11235,11312,11381,11455,11543,11620,11713,11809,11883,11963,12060,12112,12178,12265,12353,12415,12479,12542,12654,12763,12870,12980,13608"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\631285464bc3699b5e0c057776a78762\\transformed\\ui-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,998,1067,1152,1242,1318,1394,1461", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,993,1062,1147,1237,1313,1389,1456,1569"}, "to": {"startLines": "48,49,82,83,85,90,91,140,141,142,143,145,146,149,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4593,4688,8410,8507,8688,9083,9166,13217,13308,13395,13467,13613,13698,13959,14318,14394,14461", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "4683,4769,8502,8601,8769,9161,9258,13303,13390,13462,13531,13693,13783,14030,14389,14456,14569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\85666ebb484fc6a37ee9760c5f520352\\transformed\\appcompat-1.2.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "278,398,504,611,700,801,920,1005,1085,1176,1269,1364,1458,1558,1651,1746,1841,1932,2023,2108,2215,2326,2428,2536,2644,2754,2916,13873", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "393,499,606,695,796,915,1000,1080,1171,1264,1359,1453,1553,1646,1741,1836,1927,2018,2103,2210,2321,2423,2531,2639,2749,2911,3011,13954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9209685f1972143d9ab1eeffcca4ab41\\transformed\\core-1.12.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3556,3653,3755,3854,3954,4061,4171,14120", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3648,3750,3849,3949,4056,4166,4286,14216"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\870103ce12d4503c553c2f969e6a0d11\\transformed\\navigation-ui-2.7.5\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "138,139", "startColumns": "4,4", "startOffsets": "12985,13097", "endColumns": "111,119", "endOffsets": "13092,13212"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d56bc5ab2f06c8c057783f978bdff8f7\\transformed\\material3-1.1.2\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,399,511,587,679,789,919,1033,1180,1260,1358,1449,1545,1656,1782,1885,2020,2154,2290,2452,2584,2700,2821,2945,3037,3130,3246,3358,3454,3561,3666,3802,3943,4049,4147,4229,4303,4388,4473,4570,4646,4726,4823,4925,5013,5108,5192,5300,5397,5496,5611,5687,5783", "endColumns": "114,116,111,111,75,91,109,129,113,146,79,97,90,95,110,125,102,134,133,135,161,131,115,120,123,91,92,115,111,95,106,104,135,140,105,97,81,73,84,84,96,75,79,96,101,87,94,83,107,96,98,114,75,95,87", "endOffsets": "165,282,394,506,582,674,784,914,1028,1175,1255,1353,1444,1540,1651,1777,1880,2015,2149,2285,2447,2579,2695,2816,2940,3032,3125,3241,3353,3449,3556,3661,3797,3938,4044,4142,4224,4298,4383,4468,4565,4641,4721,4818,4920,5008,5103,5187,5295,5392,5491,5606,5682,5778,5866"}, "to": {"startLines": "33,34,35,36,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,84,87,147,150,152,156,157,158,159,160,161,162,163,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3016,3131,3248,3360,4774,4850,4942,5052,5182,5296,5443,5523,5621,5712,5808,5919,6045,6148,6283,6417,6553,6715,6847,6963,7084,7208,7300,7393,7509,7621,7717,7824,7929,8065,8206,8312,8606,8838,13788,14035,14221,14574,14650,14730,14827,14929,15017,15112,15196,15304,15401,15500,15615,15691,15787", "endColumns": "114,116,111,111,75,91,109,129,113,146,79,97,90,95,110,125,102,134,133,135,161,131,115,120,123,91,92,115,111,95,106,104,135,140,105,97,81,73,84,84,96,75,79,96,101,87,94,83,107,96,98,114,75,95,87", "endOffsets": "3126,3243,3355,3467,4845,4937,5047,5177,5291,5438,5518,5616,5707,5803,5914,6040,6143,6278,6412,6548,6710,6842,6958,7079,7203,7295,7388,7504,7616,7712,7819,7924,8060,8201,8307,8405,8683,8907,13868,14115,14313,14645,14725,14822,14924,15012,15107,15191,15299,15396,15495,15610,15686,15782,15870"}}]}]}