package com.workly.app.ui.home.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.workly.app.business.MultiButtonStateMachine

@Composable
fun MultiFunctionButton(
    onButtonClick: () -> Unit,
    onPunchClick: () -> Unit,
    onResetClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // TODO: Get actual state from ViewModel
    val buttonState = MultiButtonStateMachine.ButtonState.GO_WORK
    val canPunch = false
    val canReset = false
    val isWaiting = buttonState.isWaiting
    
    Card(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                contentAlignment = Alignment.Center
            ) {
                // Main button
                MainButton(
                    state = buttonState,
                    isWaiting = isWaiting,
                    onClick = onButtonClick
                )
                
                // Reset button (small, in corner)
                if (canReset) {
                    ResetButton(
                        onClick = onResetClick,
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .offset(x = 8.dp, y = (-8).dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Punch button (if applicable)
            if (canPunch) {
                Button(
                    onClick = onPunchClick,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.secondary
                    ),
                    modifier = Modifier.fillMaxWidth(0.6f)
                ) {
                    Text("Ký Công")
                }
            }
            
            // State description
            Text(
                text = getStateDescription(buttonState),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(top = 8.dp)
            )
        }
    }
}

@Composable
private fun MainButton(
    state: MultiButtonStateMachine.ButtonState,
    isWaiting: Boolean,
    onClick: () -> Unit
) {
    val buttonColor = Color(android.graphics.Color.parseColor(getButtonColor(state)))
    val scale by animateFloatAsState(
        targetValue = if (isWaiting) 0.95f else 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000),
            repeatMode = RepeatMode.Reverse
        ),
        label = "button_scale"
    )
    
    Box(
        modifier = Modifier
            .size(120.dp)
            .scale(scale)
            .clip(CircleShape)
            .background(buttonColor)
            .clickable(enabled = state.isEnabled && !isWaiting) { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = state.icon,
                fontSize = 32.sp
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = state.displayText,
                color = Color.White,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center
            )
        }
        
        // Loading indicator for waiting states
        if (isWaiting) {
            CircularProgressIndicator(
                modifier = Modifier.size(130.dp),
                color = Color.White.copy(alpha = 0.7f),
                strokeWidth = 2.dp
            )
        }
    }
}

@Composable
private fun ResetButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(32.dp)
            .clip(CircleShape)
            .background(MaterialTheme.colorScheme.error)
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = Icons.Default.Refresh,
            contentDescription = "Reset",
            tint = Color.White,
            modifier = Modifier.size(16.dp)
        )
    }
}

private fun getButtonColor(state: MultiButtonStateMachine.ButtonState): String {
    return when (state) {
        MultiButtonStateMachine.ButtonState.GO_WORK -> "#2196F3" // Blue
        MultiButtonStateMachine.ButtonState.GOING -> "#FF9800" // Orange
        MultiButtonStateMachine.ButtonState.CHECK_IN -> "#4CAF50" // Green
        MultiButtonStateMachine.ButtonState.WORKING -> "#FF9800" // Orange
        MultiButtonStateMachine.ButtonState.CHECK_OUT -> "#F44336" // Red
        MultiButtonStateMachine.ButtonState.READY_COMPLETE -> "#9C27B0" // Purple
        MultiButtonStateMachine.ButtonState.COMPLETE -> "#4CAF50" // Green
        MultiButtonStateMachine.ButtonState.COMPLETED -> "#9E9E9E" // Grey
    }
}

private fun getStateDescription(state: MultiButtonStateMachine.ButtonState): String {
    return when (state) {
        MultiButtonStateMachine.ButtonState.GO_WORK -> "Bấm khi bắt đầu đi làm"
        MultiButtonStateMachine.ButtonState.GOING -> "Đang trên đường đi làm"
        MultiButtonStateMachine.ButtonState.CHECK_IN -> "Bấm khi đến nơi làm việc"
        MultiButtonStateMachine.ButtonState.WORKING -> "Đang trong giờ làm việc"
        MultiButtonStateMachine.ButtonState.CHECK_OUT -> "Bấm khi kết thúc làm việc"
        MultiButtonStateMachine.ButtonState.READY_COMPLETE -> "Bấm để hoàn tất ca làm việc"
        MultiButtonStateMachine.ButtonState.COMPLETE -> "Bấm để xác nhận hoàn tất"
        MultiButtonStateMachine.ButtonState.COMPLETED -> "Đã hoàn tất ca làm việc hôm nay"
    }
}
