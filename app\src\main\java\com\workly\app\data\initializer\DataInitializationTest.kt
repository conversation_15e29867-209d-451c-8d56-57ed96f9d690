package com.workly.app.data.initializer

import android.content.Context
import com.workly.app.data.model.ShiftTemplates
import com.workly.app.data.repository.ShiftRepository
import com.workly.app.data.storage.DataStoreManager
import kotlinx.coroutines.runBlocking

/**
 * Test class to verify data initialization works correctly
 * This can be used for debugging and testing purposes
 */
class DataInitializationTest(private val context: Context) {
    
    private val dataStoreManager = DataStoreManager(context)
    private val shiftRepository = ShiftRepository(dataStoreManager)
    private val shiftDataInitializer = ShiftDataInitializer(context, shiftRepository)
    
    /**
     * Test template creation
     */
    fun testTemplateCreation(): TestResult {
        return try {
            val templates = ShiftTemplates.getAllTemplates()
            
            if (templates.isEmpty()) {
                TestResult.Failed("No templates found")
            } else {
                val templateNames = templates.map { it.name }
                TestResult.Success("Found ${templates.size} templates: ${templateNames.joinToString(", ")}")
            }
        } catch (e: Exception) {
            TestResult.Failed("Error creating templates: ${e.message}")
        }
    }
    
    /**
     * Test data initialization
     */
    fun testDataInitialization(): TestResult {
        return try {
            runBlocking {
                // Clear existing data first
                val existingShifts = shiftRepository.getShifts()
                existingShifts.forEach { shift ->
                    shiftRepository.deleteShift(shift.id)
                }
                
                // Initialize default data
                shiftDataInitializer.initializeDefaultShifts()
                
                // Check if data was initialized
                val shifts = shiftRepository.getShifts()
                val isInitialized = shiftDataInitializer.isInitialized()
                
                if (shifts.isEmpty()) {
                    TestResult.Failed("No shifts were initialized")
                } else if (!isInitialized) {
                    TestResult.Failed("Initialization flag not set")
                } else {
                    val shiftNames = shifts.map { it.name }
                    TestResult.Success("Initialized ${shifts.size} shifts: ${shiftNames.joinToString(", ")}")
                }
            }
        } catch (e: Exception) {
            TestResult.Failed("Error during initialization: ${e.message}")
        }
    }
    
    /**
     * Test template addition
     */
    fun testTemplateAddition(): TestResult {
        return try {
            runBlocking {
                // Clear existing data first
                val existingShifts = shiftRepository.getShifts()
                existingShifts.forEach { shift ->
                    shiftRepository.deleteShift(shift.id)
                }
                
                // Add specific template
                val templateName = "Ca hành chính"
                val success = shiftRepository.addShiftFromTemplate(templateName)
                
                if (!success) {
                    TestResult.Failed("Failed to add template: $templateName")
                } else {
                    val shifts = shiftRepository.getShifts()
                    val addedShift = shifts.find { it.name == templateName }
                    
                    if (addedShift == null) {
                        TestResult.Failed("Template was not found after addition")
                    } else {
                        TestResult.Success("Successfully added template: ${addedShift.name} (${addedShift.startTime}-${addedShift.officeEndTime})")
                    }
                }
            }
        } catch (e: Exception) {
            TestResult.Failed("Error adding template: ${e.message}")
        }
    }
    
    /**
     * Test available templates
     */
    fun testAvailableTemplates(): TestResult {
        return try {
            runBlocking {
                // Add some templates first
                shiftRepository.addShiftFromTemplate("Ca hành chính")
                shiftRepository.addShiftFromTemplate("Ca ngày")
                
                // Get available templates (should exclude the ones we just added)
                val availableTemplates = shiftRepository.getAvailableTemplates()
                val allTemplates = ShiftTemplates.getTemplateNames()
                
                val expectedAvailable = allTemplates.size - 2 // We added 2 templates
                
                if (availableTemplates.size != expectedAvailable) {
                    TestResult.Failed("Expected $expectedAvailable available templates, got ${availableTemplates.size}")
                } else {
                    TestResult.Success("Available templates: ${availableTemplates.joinToString(", ")}")
                }
            }
        } catch (e: Exception) {
            TestResult.Failed("Error getting available templates: ${e.message}")
        }
    }
    
    /**
     * Run all tests
     */
    fun runAllTests(): List<Pair<String, TestResult>> {
        return listOf(
            "Template Creation" to testTemplateCreation(),
            "Data Initialization" to testDataInitialization(),
            "Template Addition" to testTemplateAddition(),
            "Available Templates" to testAvailableTemplates()
        )
    }
    
    sealed class TestResult {
        data class Success(val message: String) : TestResult()
        data class Failed(val error: String) : TestResult()
        
        fun isSuccess(): Boolean = this is Success
        fun getMessage(): String = when (this) {
            is Success -> message
            is Failed -> error
        }
    }
}
