package com.workly.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.workly.app.business.ShiftValidator
import com.workly.app.data.model.Shift
import com.workly.app.data.repository.ShiftRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

class ShiftManagementViewModel(
    private val shiftRepository: ShiftRepository
) : ViewModel() {

    private val shiftValidator = ShiftValidator()

    // State flows
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    private val _successMessage = MutableStateFlow<String?>(null)
    val successMessage: StateFlow<String?> = _successMessage.asStateFlow()

    // Shifts data
    val shifts = shiftRepository.getShiftsFlow()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    val activeShiftId = shiftRepository.getActiveShiftIdFlow()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = null
        )

    /**
     * Set active shift
     */
    fun setActiveShift(shiftId: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                shiftRepository.setActiveShift(shiftId)
                _successMessage.value = "Đã chọn ca làm việc"
            } catch (e: Exception) {
                _error.value = "Lỗi khi chọn ca: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Delete shift
     */
    fun deleteShift(shift: Shift) {
        viewModelScope.launch {
            try {
                _isLoading.value = true

                // Validate if shift can be deleted
                val isActive = activeShiftId.value == shift.id
                val validationResult = shiftValidator.canDeleteShift(shift, isActive)

                if (!validationResult.isValid) {
                    _error.value = validationResult.errors.first().message
                    return@launch
                }

                shiftRepository.deleteShift(shift.id)
                _successMessage.value = "Đã xóa ca '${shift.name}'"

            } catch (e: Exception) {
                _error.value = "Lỗi khi xóa ca: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Clear active shift
     */
    fun clearActiveShift() {
        viewModelScope.launch {
            try {
                shiftRepository.clearActiveShift()
                _successMessage.value = "Đã bỏ chọn ca làm việc"
            } catch (e: Exception) {
                _error.value = "Lỗi: ${e.message}"
            }
        }
    }

    fun clearError() {
        _error.value = null
    }

    fun clearSuccessMessage() {
        _successMessage.value = null
    }
}
