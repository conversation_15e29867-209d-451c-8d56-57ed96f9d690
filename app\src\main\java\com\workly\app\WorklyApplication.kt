package com.workly.app

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import androidx.work.Configuration
import androidx.work.WorkManager
import com.workly.app.business.AlarmManager
import com.workly.app.data.initializer.ShiftDataInitializer
import com.workly.app.data.repository.SettingsRepository
import com.workly.app.data.repository.ShiftRepository
import com.workly.app.data.repository.AttendanceRepository
import com.workly.app.data.repository.NotesRepository
import com.workly.app.data.storage.DataStoreManager
import com.workly.app.notification.NotificationManager
import com.workly.app.worker.BackgroundTaskWorker
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob

class WorklyApplication : Application(), Configuration.Provider {
    
    // Application scope for long-running operations
    val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    
    // Data layer
    lateinit var dataStoreManager: DataStoreManager
    
    // Repositories
    lateinit var settingsRepository: SettingsRepository
        private set
    lateinit var shiftRepository: ShiftRepository
        private set
    lateinit var attendanceRepository: AttendanceRepository
        private set
    lateinit var notesRepository: NotesRepository
        private set

    // Business services
    lateinit var alarmManager: AlarmManager
        private set
    lateinit var notificationManager: NotificationManager
        private set

    // Data initializers
    lateinit var shiftDataInitializer: ShiftDataInitializer
        private set
    
    override fun onCreate() {
        super.onCreate()
        
        // Initialize data layer
        initializeDataLayer()

        // Initialize business services
        initializeBusinessServices()

        // Initialize default data
        initializeDefaultData()

        // Create notification channels
        createNotificationChannels()

        // Initialize WorkManager
        WorkManager.initialize(this, workManagerConfiguration)

        // Initialize alarms
        alarmManager.initializeAlarms()

        // Schedule background tasks
        BackgroundTaskWorker.schedulePeriodicTasks(this)
    }
    
    private fun initializeDataLayer() {
        dataStoreManager = DataStoreManager(this)
        
        settingsRepository = SettingsRepository(dataStoreManager)
        shiftRepository = ShiftRepository(dataStoreManager)
        attendanceRepository = AttendanceRepository(dataStoreManager)
        notesRepository = NotesRepository(dataStoreManager)
    }

    private fun initializeBusinessServices() {
        notificationManager = NotificationManager(this)
        alarmManager = AlarmManager(
            context = this,
            shiftRepository = shiftRepository,
            notesRepository = notesRepository,
            settingsRepository = settingsRepository,
            scope = applicationScope
        )

        // Initialize data initializers
        shiftDataInitializer = ShiftDataInitializer(this, shiftRepository)
    }

    private fun initializeDefaultData() {
        // Initialize default shifts asynchronously
        shiftDataInitializer.initializeAsync()
    }
    
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            // Alarm channel - High priority for alarms
            val alarmChannel = NotificationChannel(
                ALARM_CHANNEL_ID,
                getString(R.string.alarm_channel_name),
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = getString(R.string.alarm_channel_description)
                enableVibration(true)
                setBypassDnd(true)
                lockscreenVisibility = android.app.Notification.VISIBILITY_PUBLIC
            }
            
            // Weather warning channel - High priority for weather alerts
            val weatherChannel = NotificationChannel(
                WEATHER_CHANNEL_ID,
                getString(R.string.weather_channel_name),
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = getString(R.string.weather_channel_description)
                enableVibration(true)
            }
            
            // General notifications channel - Default priority
            val generalChannel = NotificationChannel(
                GENERAL_CHANNEL_ID,
                getString(R.string.general_channel_name),
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = getString(R.string.general_channel_description)
            }
            
            notificationManager.createNotificationChannels(
                listOf(alarmChannel, weatherChannel, generalChannel)
            )
        }
    }
    
    override fun getWorkManagerConfiguration(): Configuration {
        return Configuration.Builder()
            .setMinimumLoggingLevel(if (BuildConfig.DEBUG) android.util.Log.DEBUG else android.util.Log.ERROR)
            .build()
    }
    
    companion object {
        const val ALARM_CHANNEL_ID = "workly_alarm_channel"
        const val WEATHER_CHANNEL_ID = "workly_weather_channel"
        const val GENERAL_CHANNEL_ID = "workly_general_channel"
    }
}
