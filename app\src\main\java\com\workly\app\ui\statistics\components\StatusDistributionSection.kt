package com.workly.app.ui.statistics.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.workly.app.data.model.WorkStatus

@Composable
fun StatusDistributionSection(statusDistribution: Map<WorkStatus, Int>) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Phân bố trạng thái",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            if (statusDistribution.isEmpty()) {
                Text(
                    text = "Chưa có dữ liệu",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            } else {
                val totalDays = statusDistribution.values.sum()
                
                statusDistribution.forEach { (status, count) ->
                    StatusDistributionItem(
                        status = status,
                        count = count,
                        percentage = if (totalDays > 0) (count.toFloat() / totalDays * 100) else 0f
                    )
                }
            }
        }
    }
}

@Composable
private fun StatusDistributionItem(
    status: WorkStatus,
    count: Int,
    percentage: Float
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .size(12.dp)
                        .clip(RoundedCornerShape(2.dp))
                        .background(getStatusColor(status))
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = getStatusDisplayName(status),
                    style = MaterialTheme.typography.bodyMedium
                )
            }
            
            Text(
                text = "$count ngày (${String.format("%.1f", percentage)}%)",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        // Progress bar
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(4.dp)
                .clip(RoundedCornerShape(2.dp))
                .background(MaterialTheme.colorScheme.surfaceVariant)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth(percentage / 100f)
                    .fillMaxHeight()
                    .clip(RoundedCornerShape(2.dp))
                    .background(getStatusColor(status))
            )
        }
    }
}

private fun getStatusColor(status: WorkStatus): Color {
    return when (status) {
        WorkStatus.DU_CONG -> Color(0xFF4CAF50) // Green
        WorkStatus.THIEU_LOG -> Color(0xFFFF9800) // Orange
        WorkStatus.DI_MUON, WorkStatus.VE_SOM, WorkStatus.DI_MUON_VE_SOM -> Color(0xFFFF5722) // Red-Orange
        WorkStatus.NGHI_PHEP -> Color(0xFF2196F3) // Blue
        WorkStatus.NGHI_BENH -> Color(0xFF9C27B0) // Purple
        WorkStatus.NGHI_LE -> Color(0xFF607D8B) // Blue-Grey
        WorkStatus.VANG_MAT -> Color(0xFFF44336) // Red
        WorkStatus.UNKNOWN -> Color(0xFF9E9E9E) // Grey
    }
}

private fun getStatusDisplayName(status: WorkStatus): String {
    return when (status) {
        WorkStatus.DU_CONG -> "Đủ công"
        WorkStatus.THIEU_LOG -> "Thiếu log"
        WorkStatus.DI_MUON -> "Đi muộn"
        WorkStatus.VE_SOM -> "Về sớm"
        WorkStatus.DI_MUON_VE_SOM -> "Đi muộn, về sớm"
        WorkStatus.NGHI_PHEP -> "Nghỉ phép"
        WorkStatus.NGHI_BENH -> "Nghỉ bệnh"
        WorkStatus.NGHI_LE -> "Nghỉ lễ"
        WorkStatus.VANG_MAT -> "Vắng mặt"
        WorkStatus.UNKNOWN -> "Không xác định"
    }
}
