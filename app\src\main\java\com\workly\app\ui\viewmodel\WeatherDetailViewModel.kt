package com.workly.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.workly.app.data.model.WeatherData
import com.workly.app.data.repository.SettingsRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

class WeatherDetailViewModel(
    private val settingsRepository: SettingsRepository
) : ViewModel() {

    // State flows
    private val _weatherData = MutableStateFlow<WeatherData?>(null)
    val weatherData: StateFlow<WeatherData?> = _weatherData.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    private val _lastUpdated = MutableStateFlow<String?>(null)
    val lastUpdated: StateFlow<String?> = _lastUpdated.asStateFlow()

    private val _currentLocation = MutableStateFlow("home") // home or office
    val currentLocation: StateFlow<String> = _currentLocation.asStateFlow()

    // Settings flow
    private val settings = settingsRepository.getSettingsFlow()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = null
        )

    /**
     * Load weather data from cache or API
     */
    fun loadWeatherData() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                // Check if weather warning is enabled
                val currentSettings = settings.value
                if (currentSettings?.weatherWarningEnabled != true) {
                    _error.value = "Cảnh báo thời tiết đã bị tắt trong cài đặt"
                    return@launch
                }
                
                // Check if location is available
                val weatherLocation = currentSettings.weatherLocation
                if (weatherLocation == null) {
                    _error.value = "Chưa thiết lập vị trí. Vui lòng cấp quyền vị trí trong cài đặt."
                    return@launch
                }
                
                // For demo purposes, create sample data
                // In a real app, you would call weather API here
                val sampleData = if (_currentLocation.value == "home") {
                    WeatherData.createSample("Nhà - ${weatherLocation.name}")
                } else {
                    WeatherData.createWithWarnings("Công ty - ${weatherLocation.name}")
                }
                
                _weatherData.value = sampleData
                _lastUpdated.value = formatLastUpdated(sampleData.lastUpdated)
                
            } catch (e: Exception) {
                _error.value = "Lỗi khi tải dữ liệu thời tiết: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Refresh weather data
     */
    fun refreshWeatherData() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                // Simulate API call delay
                kotlinx.coroutines.delay(1000)
                
                // Load fresh data
                loadWeatherData()
                
            } catch (e: Exception) {
                _error.value = "Lỗi khi làm mới dữ liệu: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Toggle between home and office location
     */
    fun toggleLocation() {
        _currentLocation.value = if (_currentLocation.value == "home") "office" else "home"
        loadWeatherData() // Reload data for new location
    }

    /**
     * Check if multiple locations are available
     */
    fun hasMultipleLocations(): Boolean {
        // For demo, assume we always have both home and office
        // In a real app, check if both locations are configured
        return true
    }

    /**
     * Format last updated time
     */
    private fun formatLastUpdated(timestamp: String): String {
        return try {
            val instant = kotlinx.datetime.Instant.parse(timestamp)
            val localDateTime = instant.toLocalDateTime(kotlinx.datetime.TimeZone.currentSystemDefault())
            "${localDateTime.hour.toString().padStart(2, '0')}:${localDateTime.minute.toString().padStart(2, '0')}"
        } catch (e: Exception) {
            "Không xác định"
        }
    }

    /**
     * Get weather warnings for current conditions
     */
    fun getWeatherWarnings(): List<String> {
        val data = _weatherData.value ?: return emptyList()
        val warnings = mutableListOf<String>()
        
        // Add warnings based on weather conditions
        if (data.temperature > 35) {
            warnings.add("Cảnh báo nắng nóng gay gắt")
        }
        
        if (data.temperature < 5) {
            warnings.add("Cảnh báo rét đậm")
        }
        
        if (data.windSpeed > 40) {
            warnings.add("Cảnh báo gió mạnh")
        }
        
        if (data.isRainy()) {
            warnings.add("Có mưa, nên mang theo đồ che mưa")
        }
        
        if (data.uvIndex > 8) {
            warnings.add("Chỉ số UV cao, nên sử dụng kem chống nắng")
        }
        
        return warnings + data.warnings
    }

    /**
     * Check if weather is suitable for commuting
     */
    fun isWeatherSuitableForCommuting(): Boolean {
        val data = _weatherData.value ?: return true
        
        return !data.isSevereWeather() && 
               data.windSpeed < 50 && 
               data.visibility > 5
    }

    /**
     * Get commuting recommendations
     */
    fun getCommutingRecommendations(): List<String> {
        val data = _weatherData.value ?: return emptyList()
        val recommendations = mutableListOf<String>()
        
        when {
            data.isRainy() -> {
                recommendations.add("Mang theo áo mưa hoặc ô")
                recommendations.add("Đi sớm hơn 15-20 phút do có thể kẹt xe")
            }
            data.temperature > 30 -> {
                recommendations.add("Mang theo nước uống")
                recommendations.add("Đội mũ hoặc sử dụng kem chống nắng")
            }
            data.temperature < 15 -> {
                recommendations.add("Mặc áo ấm")
                recommendations.add("Đeo khẩu trang để tránh gió lạnh")
            }
        }
        
        if (data.windSpeed > 25) {
            recommendations.add("Cẩn thận khi đi xe máy do gió mạnh")
        }
        
        if (data.visibility < 8) {
            recommendations.add("Lái xe cẩn thận do tầm nhìn hạn chế")
        }
        
        return recommendations
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _error.value = null
    }

    /**
     * Get weather summary for notifications
     */
    fun getWeatherSummary(): String {
        val data = _weatherData.value ?: return "Không có dữ liệu thời tiết"
        
        return buildString {
            append("${data.getWeatherEmoji()} ${data.condition}")
            append(", ${data.temperature.toInt()}°C")
            
            if (data.isRainy()) {
                append(" - Có mưa")
            }
            
            if (data.hasWarnings()) {
                append(" ⚠️")
            }
        }
    }

    /**
     * Check if weather data is stale (older than 1 hour)
     */
    fun isWeatherDataStale(): Boolean {
        val data = _weatherData.value ?: return true
        
        return try {
            val lastUpdate = kotlinx.datetime.Instant.parse(data.lastUpdated)
            val now = kotlinx.datetime.Clock.System.now()
            val hoursSinceUpdate = (now - lastUpdate).inWholeHours
            
            hoursSinceUpdate >= 1
        } catch (e: Exception) {
            true
        }
    }
}
