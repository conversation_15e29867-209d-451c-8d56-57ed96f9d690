package com.workly.app.ui.notes

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.workly.app.R
import com.workly.app.data.model.NotePriority
import com.workly.app.ui.viewmodel.NoteDetailViewModel
import com.workly.app.ui.viewmodel.ViewModelFactory

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NoteDetailScreen(
    navController: NavController,
    noteId: String? = null
) {
    val application = androidx.compose.ui.platform.LocalContext.current.applicationContext as com.workly.app.WorklyApplication
    val viewModel: NoteDetailViewModel = viewModel(
        factory = ViewModelFactory(application)
    )

    val note by viewModel.note.collectAsState()
    val shifts by viewModel.availableShifts.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    val successMessage by viewModel.successMessage.collectAsState()

    // Form state
    var title by remember { mutableStateOf("") }
    var content by remember { mutableStateOf("") }
    var priority by remember { mutableStateOf(NotePriority.NORMAL) }
    var reminderTime by remember { mutableStateOf("") }
    var reminderType by remember { mutableStateOf("none") } // none, specific, shift
    var selectedShifts by remember { mutableStateOf(setOf<String>()) }
    var showTimePicker by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }

    val isEditMode = noteId != null

    // Load note data if editing
    LaunchedEffect(noteId) {
        if (noteId != null) {
            viewModel.loadNote(noteId)
        }
    }

    // Update form when note loads
    LaunchedEffect(note) {
        note?.let {
            title = it.title
            content = it.content
            priority = it.priority
            reminderTime = it.reminderTime ?: ""
            selectedShifts = it.associatedShiftIds.toSet()
            reminderType = when {
                it.reminderTime != null && it.associatedShiftIds.isNotEmpty() -> "shift"
                it.reminderTime != null -> "specific"
                else -> "none"
            }
        }
    }

    // Handle success/error messages
    successMessage?.let { message ->
        LaunchedEffect(message) {
            navController.popBackStack()
            viewModel.clearSuccessMessage()
        }
    }

    error?.let { errorMessage ->
        LaunchedEffect(errorMessage) {
            // TODO: Show snackbar
            viewModel.clearError()
        }
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top bar
        TopAppBar(
            title = { 
                Text(
                    if (isEditMode) {
                        stringResource(R.string.edit_note)
                    } else {
                        stringResource(R.string.add_note)
                    }
                )
            },
            navigationIcon = {
                IconButton(onClick = { navController.popBackStack() }) {
                    Icon(Icons.Default.ArrowBack, contentDescription = stringResource(R.string.back))
                }
            },
            actions = {
                if (isEditMode) {
                    IconButton(
                        onClick = { showDeleteDialog = true }
                    ) {
                        Icon(
                            Icons.Default.Delete, 
                            contentDescription = stringResource(R.string.delete_note),
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
                IconButton(
                    onClick = {
                        if (isEditMode) {
                            viewModel.updateNote(
                                noteId!!,
                                title,
                                content,
                                priority,
                                if (reminderType != "none") reminderTime else null,
                                if (reminderType == "shift") selectedShifts.toList() else emptyList()
                            )
                        } else {
                            viewModel.createNote(
                                title,
                                content,
                                priority,
                                if (reminderType != "none") reminderTime else null,
                                if (reminderType == "shift") selectedShifts.toList() else emptyList()
                            )
                        }
                    },
                    enabled = title.isNotBlank()
                ) {
                    Icon(Icons.Default.Save, contentDescription = stringResource(R.string.save))
                }
            }
        )

        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Basic Information
                Card {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Text(
                            text = stringResource(R.string.shift_basic_info),
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.SemiBold
                        )
                        
                        // Title
                        OutlinedTextField(
                            value = title,
                            onValueChange = { title = it },
                            label = { Text(stringResource(R.string.note_title)) },
                            modifier = Modifier.fillMaxWidth(),
                            singleLine = true
                        )
                        
                        // Content
                        OutlinedTextField(
                            value = content,
                            onValueChange = { content = it },
                            label = { Text(stringResource(R.string.note_content)) },
                            modifier = Modifier.fillMaxWidth(),
                            minLines = 3,
                            maxLines = 6
                        )
                    }
                }

                // Priority Settings
                Card {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Text(
                            text = stringResource(R.string.note_priority),
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.SemiBold
                        )
                        
                        Column(
                            modifier = Modifier.selectableGroup(),
                            verticalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            NotePriority.values().forEach { priorityOption ->
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .selectable(
                                            selected = (priority == priorityOption),
                                            onClick = { priority = priorityOption },
                                            role = Role.RadioButton
                                        )
                                        .padding(vertical = 4.dp),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    RadioButton(
                                        selected = (priority == priorityOption),
                                        onClick = null
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        text = "${priorityOption.icon} ${priorityOption.displayName}",
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                }
                            }
                        }
                    }
                }

                // Reminder Settings
                Card {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Text(
                            text = stringResource(R.string.reminder_settings),
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.SemiBold
                        )
                        
                        // Reminder type selection
                        Column(
                            modifier = Modifier.selectableGroup(),
                            verticalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            val reminderOptions = listOf(
                                "none" to "Không nhắc nhở",
                                "specific" to "Đặt lịch cụ thể",
                                "shift" to "Nhắc theo ca"
                            )
                            
                            reminderOptions.forEach { (value, label) ->
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .selectable(
                                            selected = (reminderType == value),
                                            onClick = { reminderType = value },
                                            role = Role.RadioButton
                                        )
                                        .padding(vertical = 4.dp),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    RadioButton(
                                        selected = (reminderType == value),
                                        onClick = null
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        text = label,
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                }
                            }
                        }
                        
                        // Time picker for specific reminder
                        if (reminderType == "specific" || reminderType == "shift") {
                            OutlinedTextField(
                                value = reminderTime,
                                onValueChange = { reminderTime = it },
                                label = { Text(stringResource(R.string.reminder_time)) },
                                modifier = Modifier.fillMaxWidth(),
                                placeholder = { Text("HH:mm") },
                                singleLine = true
                            )
                        }
                        
                        // Shift selection for shift-based reminder
                        if (reminderType == "shift") {
                            Text(
                                text = stringResource(R.string.associated_shifts),
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.Medium
                            )
                            
                            shifts.forEach { shift ->
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Checkbox(
                                        checked = selectedShifts.contains(shift.id),
                                        onCheckedChange = { checked ->
                                            selectedShifts = if (checked) {
                                                selectedShifts + shift.id
                                            } else {
                                                selectedShifts - shift.id
                                            }
                                        }
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Column {
                                        Text(
                                            text = shift.name,
                                            style = MaterialTheme.typography.bodyMedium
                                        )
                                        Text(
                                            text = "${shift.startTime} - ${shift.officeEndTime}",
                                            style = MaterialTheme.typography.bodySmall,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // Delete confirmation dialog
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = { Text(stringResource(R.string.delete_note)) },
            text = { Text(stringResource(R.string.confirm_delete_note)) },
            confirmButton = {
                TextButton(
                    onClick = {
                        viewModel.deleteNote(noteId!!)
                        showDeleteDialog = false
                    },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("Xóa")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteDialog = false }) {
                    Text("Hủy")
                }
            }
        )
    }
}
