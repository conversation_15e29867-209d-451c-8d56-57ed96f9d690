package com.workly.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.workly.app.data.model.Note
import com.workly.app.data.repository.NotesRepository
import com.workly.app.data.repository.ShiftRepository
import com.workly.app.ui.notes.AddEditNoteUiState
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

class AddEditNoteViewModel(
    private val notesRepository: NotesRepository,
    private val shiftRepository: ShiftRepository,
    private val alarmManager: com.workly.app.business.AlarmManager? = null
) : ViewModel() {
    
    // State flows
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    private val _saveSuccess = MutableStateFlow(false)
    val saveSuccess: StateFlow<Boolean> = _saveSuccess.asStateFlow()
    
    private val _currentNoteId = MutableStateFlow<String?>(null)
    
    // UI State
    private val _uiState = MutableStateFlow(AddEditNoteUiState())
    val uiState: StateFlow<AddEditNoteUiState> = _uiState.asStateFlow()
    
    // Available shifts
    val availableShifts = shiftRepository.getShiftsFlow()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    init {
        // Validate whenever UI state changes
        viewModelScope.launch {
            _uiState.collect { state ->
                validateCurrentState()
            }
        }
    }
    
    /**
     * Load existing note for editing
     */
    fun loadNote(noteId: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _currentNoteId.value = noteId
                
                val note = notesRepository.getNoteById(noteId)
                if (note != null) {
                    _uiState.value = AddEditNoteUiState(
                        title = note.title,
                        content = note.content,
                        reminderTime = note.reminderTime,
                        associatedShiftIds = note.associatedShiftIds,
                        explicitReminderDays = note.explicitReminderDays,
                        isActive = note.isActive
                    )
                } else {
                    _error.value = "Không tìm thấy ghi chú"
                }
            } catch (e: Exception) {
                _error.value = "Lỗi khi tải ghi chú: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Save note (create new or update existing)
     */
    fun saveNote() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                val currentState = _uiState.value
                if (!currentState.isValid) {
                    _error.value = "Vui lòng sửa các lỗi trước khi lưu"
                    return@launch
                }
                
                val note = if (_currentNoteId.value != null) {
                    // Update existing note
                    val existingNote = notesRepository.getNoteById(_currentNoteId.value!!)
                    existingNote?.copy(
                        title = currentState.title,
                        content = currentState.content,
                        reminderTime = currentState.reminderTime,
                        associatedShiftIds = currentState.associatedShiftIds,
                        explicitReminderDays = currentState.explicitReminderDays,
                        isActive = currentState.isActive,
                        updatedAt = kotlinx.datetime.Clock.System.now().toString()
                    )
                } else {
                    // Create new note
                    Note.createNew(
                        title = currentState.title,
                        content = currentState.content,
                        reminderTime = currentState.reminderTime,
                        associatedShiftIds = currentState.associatedShiftIds,
                        explicitReminderDays = currentState.explicitReminderDays
                    ).copy(isActive = currentState.isActive)
                }
                
                if (note != null) {
                    if (_currentNoteId.value != null) {
                        notesRepository.updateNote(note)
                    } else {
                        notesRepository.addNote(note)
                    }
                    
                    // Update alarm
                    alarmManager?.onNoteChanged(note)
                    
                    _saveSuccess.value = true
                }
                
            } catch (e: Exception) {
                _error.value = "Lỗi khi lưu ghi chú: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    // Update methods for UI state
    fun updateTitle(title: String) {
        _uiState.value = _uiState.value.copy(title = title)
    }
    
    fun updateContent(content: String) {
        _uiState.value = _uiState.value.copy(content = content)
    }
    
    fun updateReminderTime(reminderTime: String?) {
        _uiState.value = _uiState.value.copy(reminderTime = reminderTime)
    }
    
    fun updateAssociatedShifts(shiftIds: List<String>) {
        _uiState.value = _uiState.value.copy(associatedShiftIds = shiftIds)
    }
    
    fun updateExplicitReminderDays(days: List<String>) {
        _uiState.value = _uiState.value.copy(explicitReminderDays = days)
    }
    
    fun updateIsActive(isActive: Boolean) {
        _uiState.value = _uiState.value.copy(isActive = isActive)
    }
    
    private fun validateCurrentState() {
        val currentState = _uiState.value
        val errors = mutableListOf<String>()
        
        // Validate title
        if (currentState.title.isBlank()) {
            errors.add("Tiêu đề không được để trống")
        } else if (currentState.title.length > 100) {
            errors.add("Tiêu đề không được quá 100 ký tự")
        }
        
        // Validate content length
        if (currentState.content.length > 500) {
            errors.add("Nội dung không được quá 500 ký tự")
        }
        
        // Validate reminder time format
        if (currentState.reminderTime != null) {
            if (!isValidTimeFormat(currentState.reminderTime)) {
                errors.add("Định dạng thời gian nhắc nhở không hợp lệ (HH:mm)")
            }
        }
        
        // Validate reminder configuration
        if (currentState.reminderTime != null) {
            if (currentState.associatedShiftIds.isEmpty() && currentState.explicitReminderDays.isEmpty()) {
                errors.add("Vui lòng chọn ca làm việc hoặc ngày cụ thể để nhắc nhở")
            }
        }
        
        _uiState.value = currentState.copy(
            validationErrors = errors,
            isValid = errors.isEmpty()
        )
    }
    
    private fun isValidTimeFormat(timeString: String): Boolean {
        return try {
            kotlinx.datetime.LocalTime.parse(timeString)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    fun clearError() {
        _error.value = null
    }
}
