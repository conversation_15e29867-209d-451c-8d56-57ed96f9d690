package com.workly.app.ui.settings.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.workly.app.data.model.UserSettings
import com.workly.app.data.model.WeatherLocation

@Composable
fun WeatherSettingsSection(
    settings: UserSettings,
    onWeatherWarningToggle: (Boolean) -> Unit,
    onForgetLocation: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Cài đặt thời tiết",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            // Weather Warning Setting
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Cloud,
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.width(16.dp))
                
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "Cảnh báo thời tiết",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "Nhận thông báo khi có thời tiết xấu",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                Switch(
                    checked = settings.weatherWarningEnabled,
                    onCheckedChange = onWeatherWarningToggle
                )
            }
            
            // Location Setting (only show if weather warning is enabled)
            if (settings.weatherWarningEnabled) {
                Divider()
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.LocationOn,
                        contentDescription = null,
                        modifier = Modifier.size(24.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "Vị trí",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = settings.weatherLocation?.cityName ?: "Chưa thiết lập",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    if (settings.weatherLocation != null) {
                        TextButton(
                            onClick = onForgetLocation,
                            colors = ButtonDefaults.textButtonColors(
                                contentColor = MaterialTheme.colorScheme.error
                            )
                        ) {
                            Text("Quên vị trí")
                        }
                    } else {
                        TextButton(
                            onClick = {
                                // TODO: Open location picker
                            }
                        ) {
                            Text("Thiết lập")
                        }
                    }
                }
                
                if (settings.weatherLocation == null) {
                    Card(
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.warningContainer
                        )
                    ) {
                        Row(
                            modifier = Modifier.padding(12.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Info,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.onWarningContainer
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "Vui lòng thiết lập vị trí để nhận cảnh báo thời tiết",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onWarningContainer
                            )
                        }
                    }
                }
            }
        }
    }
}

// Extension property for warning container color (if not available in theme)
private val ColorScheme.warningContainer: androidx.compose.ui.graphics.Color
    get() = surfaceVariant

private val ColorScheme.onWarningContainer: androidx.compose.ui.graphics.Color
    get() = onSurfaceVariant
