package com.workly.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.workly.app.business.AttendanceCalculator
import com.workly.app.business.WeeklyStatusCalculator
import com.workly.app.data.model.DailyWorkStatus
import com.workly.app.data.model.WorkStatus
import com.workly.app.data.repository.AttendanceRepository
import com.workly.app.data.repository.ShiftRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.datetime.*

class StatisticsViewModel(
    private val attendanceRepository: AttendanceRepository,
    private val shiftRepository: ShiftRepository
) : ViewModel() {

    private val attendanceCalculator = AttendanceCalculator()
    private val weeklyStatusCalculator = WeeklyStatusCalculator()

    // State flows
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    // Filter states
    private val _selectedPeriod = MutableStateFlow(StatisticsPeriod.THIS_MONTH)
    val selectedPeriod: StateFlow<StatisticsPeriod> = _selectedPeriod.asStateFlow()

    private val _customDateRange = MutableStateFlow<DateRange?>(null)
    val customDateRange: StateFlow<DateRange?> = _customDateRange.asStateFlow()

    private val _selectedShiftId = MutableStateFlow<String?>(null)
    val selectedShiftId: StateFlow<String?> = _selectedShiftId.asStateFlow()

    // Available shifts for filtering
    val availableShifts = shiftRepository.getShiftsFlow()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    // Statistics data
    val statisticsData = combine(
        selectedPeriod,
        customDateRange,
        selectedShiftId,
        attendanceRepository.getDailyWorkStatusesFlow()
    ) { period, customRange, shiftId, allStatuses ->
        calculateStatistics(period, customRange, shiftId, allStatuses)
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = StatisticsData()
    )

    /**
     * Update selected period
     */
    fun updateSelectedPeriod(period: StatisticsPeriod) {
        _selectedPeriod.value = period
    }

    /**
     * Update custom date range
     */
    fun updateCustomDateRange(startDate: LocalDate, endDate: LocalDate) {
        _customDateRange.value = DateRange(startDate, endDate)
        _selectedPeriod.value = StatisticsPeriod.CUSTOM
    }

    /**
     * Update selected shift filter
     */
    fun updateSelectedShift(shiftId: String?) {
        _selectedShiftId.value = shiftId
    }

    /**
     * Export statistics data
     */
    fun exportStatistics(onSuccess: (String) -> Unit) {
        viewModelScope.launch {
            try {
                _isLoading.value = true

                val data = statisticsData.value
                val exportData = createExportData(data)

                onSuccess(exportData)

            } catch (e: Exception) {
                _error.value = "Lỗi khi xuất báo cáo: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    private suspend fun calculateStatistics(
        period: StatisticsPeriod,
        customRange: DateRange?,
        shiftId: String?,
        allStatuses: Map<String, DailyWorkStatus>
    ): StatisticsData {

        val dateRange = when (period) {
            StatisticsPeriod.THIS_WEEK -> getCurrentWeekRange()
            StatisticsPeriod.THIS_MONTH -> getCurrentMonthRange()
            StatisticsPeriod.LAST_MONTH -> getLastMonthRange()
            StatisticsPeriod.THIS_YEAR -> getCurrentYearRange()
            StatisticsPeriod.CUSTOM -> customRange ?: getCurrentMonthRange()
        }

        // Filter statuses by date range and shift
        val filteredStatuses = allStatuses.values.filter { status ->
            val statusDate = LocalDate.parse(status.date)
            val inDateRange = statusDate >= dateRange.startDate && statusDate <= dateRange.endDate
            val matchesShift = shiftId == null || status.shiftId == shiftId

            inDateRange && matchesShift
        }

        // Calculate overall statistics
        val periodStats = attendanceCalculator.calculatePeriodStatistics(filteredStatuses)

        // Calculate weekly breakdown
        val weeklyBreakdown = calculateWeeklyBreakdown(filteredStatuses, dateRange)

        // Calculate status distribution
        val statusDistribution = calculateStatusDistribution(filteredStatuses)

        // Calculate trends
        val trends = calculateTrends(filteredStatuses, dateRange)

        return StatisticsData(
            period = period,
            dateRange = dateRange,
            totalDays = periodStats.totalDays,
            totalHours = periodStats.totalHours,
            totalOtHours = periodStats.totalOtHours,
            totalPenaltyMinutes = periodStats.totalPenaltyMinutes,
            averageHoursPerDay = if (periodStats.totalDays > 0) periodStats.totalHours / periodStats.totalDays else 0.0,
            weeklyBreakdown = weeklyBreakdown,
            statusDistribution = statusDistribution,
            trends = trends
        )
    }

    private fun calculateWeeklyBreakdown(
        statuses: List<DailyWorkStatus>,
        dateRange: DateRange
    ): List<WeeklyStats> {
        val weeklyStats = mutableListOf<WeeklyStats>()
        var currentWeekStart = getWeekStartDate(dateRange.startDate)

        while (currentWeekStart <= dateRange.endDate) {
            val weekEnd = currentWeekStart.plus(6, DateTimeUnit.DAY)
            val weekStatuses = statuses.filter { status ->
                val statusDate = LocalDate.parse(status.date)
                statusDate >= currentWeekStart && statusDate <= weekEnd
            }

            val weekStats = attendanceCalculator.calculatePeriodStatistics(weekStatuses)

            weeklyStats.add(
                WeeklyStats(
                    weekStart = currentWeekStart,
                    weekEnd = weekEnd,
                    totalHours = weekStats.totalHours,
                    totalOtHours = weekStats.totalOtHours,
                    workDays = weekStats.totalDays
                )
            )

            currentWeekStart = currentWeekStart.plus(7, DateTimeUnit.DAY)
        }

        return weeklyStats
    }

    private fun calculateStatusDistribution(statuses: List<DailyWorkStatus>): Map<WorkStatus, Int> {
        return statuses.groupingBy { it.status }.eachCount()
    }

    private fun calculateTrends(
        statuses: List<DailyWorkStatus>,
        dateRange: DateRange
    ): StatisticsTrends {
        val sortedStatuses = statuses.sortedBy { it.date }

        // Calculate average hours trend (first half vs second half)
        val midPoint = sortedStatuses.size / 2
        val firstHalf = sortedStatuses.take(midPoint)
        val secondHalf = sortedStatuses.drop(midPoint)

        val firstHalfAvg = if (firstHalf.isNotEmpty()) firstHalf.sumOf { it.totalHours } / firstHalf.size else 0.0
        val secondHalfAvg = if (secondHalf.isNotEmpty()) secondHalf.sumOf { it.totalHours } / secondHalf.size else 0.0

        val hoursPerDayTrend = when {
            secondHalfAvg > firstHalfAvg + 0.5 -> TrendDirection.INCREASING
            secondHalfAvg < firstHalfAvg - 0.5 -> TrendDirection.DECREASING
            else -> TrendDirection.STABLE
        }

        return StatisticsTrends(
            hoursPerDayTrend = hoursPerDayTrend,
            overtimeTrend = TrendDirection.STABLE, // TODO: Calculate OT trend
            punctualityTrend = TrendDirection.STABLE // TODO: Calculate punctuality trend
        )
    }

    private fun createExportData(data: StatisticsData): String {
        // Create CSV format export
        val csv = StringBuilder()
        csv.appendLine("Workly Statistics Report")
        csv.appendLine("Period: ${data.period}")
        csv.appendLine("Date Range: ${data.dateRange.startDate} to ${data.dateRange.endDate}")
        csv.appendLine("")
        csv.appendLine("Summary:")
        csv.appendLine("Total Days,Total Hours,Average Hours/Day,Total OT Hours,Total Penalty Minutes")
        csv.appendLine("${data.totalDays},${String.format("%.2f", data.totalHours)},${String.format("%.2f", data.averageHoursPerDay)},${String.format("%.2f", data.totalOtHours)},${data.totalPenaltyMinutes}")
        csv.appendLine("")
        csv.appendLine("Weekly Breakdown:")
        csv.appendLine("Week Start,Week End,Hours,OT Hours,Work Days")
        data.weeklyBreakdown.forEach { week ->
            csv.appendLine("${week.weekStart},${week.weekEnd},${String.format("%.2f", week.totalHours)},${String.format("%.2f", week.totalOtHours)},${week.workDays}")
        }

        return csv.toString()
    }

    private fun getCurrentWeekRange(): DateRange {
        val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
        val weekStart = getWeekStartDate(today)
        val weekEnd = weekStart.plus(6, DateTimeUnit.DAY)
        return DateRange(weekStart, weekEnd)
    }

    private fun getCurrentMonthRange(): DateRange {
        val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
        val monthStart = LocalDate(today.year, today.month, 1)
        val monthEnd = monthStart.plus(1, DateTimeUnit.MONTH).minus(1, DateTimeUnit.DAY)
        return DateRange(monthStart, monthEnd)
    }

    private fun getLastMonthRange(): DateRange {
        val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
        val lastMonth = today.minus(1, DateTimeUnit.MONTH)
        val monthStart = LocalDate(lastMonth.year, lastMonth.month, 1)
        val monthEnd = monthStart.plus(1, DateTimeUnit.MONTH).minus(1, DateTimeUnit.DAY)
        return DateRange(monthStart, monthEnd)
    }

    private fun getCurrentYearRange(): DateRange {
        val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
        val yearStart = LocalDate(today.year, 1, 1)
        val yearEnd = LocalDate(today.year, 12, 31)
        return DateRange(yearStart, yearEnd)
    }

    private fun getWeekStartDate(date: LocalDate): LocalDate {
        val daysFromMonday = (date.dayOfWeek.ordinal) % 7
        return date.minus(daysFromMonday, DateTimeUnit.DAY)
    }

    fun clearError() {
        _error.value = null
    }
}

// Data classes for statistics
enum class StatisticsPeriod {
    THIS_WEEK,
    THIS_MONTH,
    LAST_MONTH,
    THIS_YEAR,
    CUSTOM
}

data class DateRange(
    val startDate: LocalDate,
    val endDate: LocalDate
)

data class StatisticsData(
    val period: StatisticsPeriod = StatisticsPeriod.THIS_MONTH,
    val dateRange: DateRange = DateRange(
        Clock.System.todayIn(TimeZone.currentSystemDefault()),
        Clock.System.todayIn(TimeZone.currentSystemDefault())
    ),
    val totalDays: Int = 0,
    val totalHours: Double = 0.0,
    val totalOtHours: Double = 0.0,
    val totalPenaltyMinutes: Int = 0,
    val averageHoursPerDay: Double = 0.0,
    val weeklyBreakdown: List<WeeklyStats> = emptyList(),
    val statusDistribution: Map<WorkStatus, Int> = emptyMap(),
    val trends: StatisticsTrends = StatisticsTrends()
)

data class WeeklyStats(
    val weekStart: LocalDate,
    val weekEnd: LocalDate,
    val totalHours: Double,
    val totalOtHours: Double,
    val workDays: Int
)

data class StatisticsTrends(
    val hoursPerDayTrend: TrendDirection = TrendDirection.STABLE,
    val overtimeTrend: TrendDirection = TrendDirection.STABLE,
    val punctualityTrend: TrendDirection = TrendDirection.STABLE
)

enum class TrendDirection {
    INCREASING,
    DECREASING,
    STABLE
}
